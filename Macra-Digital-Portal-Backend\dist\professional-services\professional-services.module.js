"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfessionalServicesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const professional_services_controller_1 = require("./professional-services.controller");
const professional_services_service_1 = require("./professional-services.service");
const professional_services_entity_1 = require("../entities/professional-services.entity");
let ProfessionalServicesModule = class ProfessionalServicesModule {
};
exports.ProfessionalServicesModule = ProfessionalServicesModule;
exports.ProfessionalServicesModule = ProfessionalServicesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([professional_services_entity_1.ProfessionalServices])],
        controllers: [professional_services_controller_1.ProfessionalServicesController],
        providers: [professional_services_service_1.ProfessionalServicesService],
        exports: [professional_services_service_1.ProfessionalServicesService],
    })
], ProfessionalServicesModule);
//# sourceMappingURL=professional-services.module.js.map