"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const permission_entity_1 = require("../entities/permission.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
let PermissionsService = class PermissionsService {
    permissionsRepository;
    constructor(permissionsRepository) {
        this.permissionsRepository = permissionsRepository;
    }
    async create(createPermissionDto) {
        const existingPermission = await this.permissionsRepository.findOne({
            where: { name: createPermissionDto.name },
        });
        if (existingPermission) {
            throw new common_1.ConflictException('Permission with this name already exists');
        }
        const permission = this.permissionsRepository.create(createPermissionDto);
        return this.permissionsRepository.save(permission);
    }
    async findAll(query) {
        console.log('PermissionsService: findAll called with query:', JSON.stringify(query, null, 2));
        const config = {
            sortableColumns: ['name', 'category', 'created_at'],
            searchableColumns: ['name', 'description', 'category'],
            defaultSortBy: [['category', 'ASC'], ['name', 'ASC']],
            defaultLimit: 10,
            maxLimit: 100,
            filterableColumns: {
                category: true,
            },
            relations: ['roles'],
        };
        console.log('PermissionsService: Using config:', JSON.stringify(config, null, 2));
        const result = await (0, nestjs_paginate_1.paginate)(query, this.permissionsRepository, config);
        console.log('PermissionsService: Raw pagination result:', JSON.stringify(result, null, 2));
        const transformedResult = pagination_interface_1.PaginationTransformer.transform(result);
        console.log('PermissionsService: Transformed result meta:', JSON.stringify(transformedResult.meta, null, 2));
        return transformedResult;
    }
    async findAllSimple() {
        return this.permissionsRepository.find({
            relations: ['roles'],
            order: { category: 'ASC', name: 'ASC' },
        });
    }
    async findByCategory() {
        const permissions = await this.findAllSimple();
        return permissions.reduce((grouped, permission) => {
            const category = permission.category;
            if (!grouped[category]) {
                grouped[category] = [];
            }
            grouped[category].push(permission);
            return grouped;
        }, {});
    }
    async findOne(id) {
        const permission = await this.permissionsRepository.findOne({
            where: { permission_id: id },
            relations: ['roles'],
        });
        if (!permission) {
            throw new common_1.NotFoundException('Permission not found');
        }
        return permission;
    }
    async findByName(name) {
        return this.permissionsRepository.findOne({
            where: { name: name },
        });
    }
    async update(id, updatePermissionDto) {
        const permission = await this.findOne(id);
        Object.assign(permission, updatePermissionDto);
        return this.permissionsRepository.save(permission);
    }
    async remove(id) {
        const permission = await this.findOne(id);
        if (permission.roles && permission.roles.length > 0) {
            throw new common_1.ConflictException('Cannot delete permission that is assigned to roles');
        }
        await this.permissionsRepository.softDelete(id);
    }
};
exports.PermissionsService = PermissionsService;
exports.PermissionsService = PermissionsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PermissionsService);
//# sourceMappingURL=permissions.service.js.map