(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/auth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAuthenticatedAxios": (()=>createAuthenticatedAxios),
    "getAuthToken": (()=>getAuthToken),
    "isAuthenticated": (()=>isAuthenticated),
    "removeAuthToken": (()=>removeAuthToken),
    "setAuthToken": (()=>setAuthToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
const getAuthToken = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return localStorage.getItem('auth_token');
};
const setAuthToken = (token)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.setItem('auth_token', token);
    }
};
const removeAuthToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.removeItem('auth_token');
    }
};
const isAuthenticated = ()=>{
    return !!getAuthToken();
};
const createAuthenticatedAxios = (API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:3001") || 'http://localhost:3001')=>{
    const token = getAuthToken();
    const instance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
        baseURL: API_BASE_URL,
        timeout: 10000,
        headers: {
            'Authorization': token ? `Bearer ${token}` : '',
            'Content-Type': 'application/json'
        }
    });
    // Add request interceptor for debugging
    instance.interceptors.request.use({
        "createAuthenticatedAxios.use": (config)=>{
            console.log(`Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);
            return config;
        }
    }["createAuthenticatedAxios.use"], {
        "createAuthenticatedAxios.use": (error)=>{
            console.error('Request interceptor error:', error);
            return Promise.reject(error);
        }
    }["createAuthenticatedAxios.use"]);
    // Add response interceptor for better error handling
    instance.interceptors.response.use({
        "createAuthenticatedAxios.use": (response)=>{
            return response;
        }
    }["createAuthenticatedAxios.use"], {
        "createAuthenticatedAxios.use": (error)=>{
            console.error('API Error Details:', {
                url: error.config?.url,
                method: error.config?.method,
                status: error.response?.status,
                statusText: error.response?.statusText,
                message: error.message,
                code: error.code,
                data: error.response?.data
            });
            // Handle authentication errors - auto logout on 401
            if (error.response?.status === 401) {
                console.warn('Authentication failed - token invalid or expired. Logging out...');
                removeAuthToken();
                // Redirect to login page
                if ("TURBOPACK compile-time truthy", 1) {
                    window.location.href = '/auth/login';
                }
            }
            // Handle network errors
            if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
                console.error('Network error - backend may not be accessible');
            }
            return Promise.reject(error);
        }
    }["createAuthenticatedAxios.use"]);
    return instance;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/authUtils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkTokenExpiry": (()=>checkTokenExpiry),
    "forceLogout": (()=>forceLogout),
    "isTokenExpired": (()=>isTokenExpired),
    "processApiResponse": (()=>processApiResponse),
    "startTokenValidationTimer": (()=>startTokenValidationTimer),
    "validateAuthState": (()=>validateAuthState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-client] (ecmascript)");
;
;
const isTokenExpired = (token)=>{
    if (!token) return true;
    try {
        // Decode JWT payload (without verification - just for expiry check)
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        // Check if token has expired
        return payload.exp < currentTime;
    } catch (error) {
        console.error('Error decoding token:', error);
        return true; // Treat invalid tokens as expired
    }
};
const validateAuthState = ()=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
    const userCookie = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('auth_user');
    // Check if token exists and is not expired
    if (!token || isTokenExpired(token)) {
        console.warn('Token is missing or expired');
        return false;
    }
    // Check if user data exists
    if (!userCookie) {
        console.warn('User data is missing');
        return false;
    }
    try {
        JSON.parse(userCookie); // Validate user data format
        return true;
    } catch (error) {
        console.error('Invalid user data format:', error);
        return false;
    }
};
const forceLogout = ()=>{
    console.warn('Forcing logout due to invalid authentication state');
    // Clear all auth data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeAuthToken"])();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_token');
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_user');
    // Clear localStorage and sessionStorage
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_preferences');
        sessionStorage.clear();
        // Redirect to appropriate login based on current portal
        const isCustomerPortal = window.location.pathname.startsWith('/customer') || window.location.hostname.includes('customer');
        window.location.href = isCustomerPortal ? '/customer/auth/login' : '/auth/login';
    }
};
const startTokenValidationTimer = (intervalMs = 60000)=>{
    return setInterval(()=>{
        if (!validateAuthState()) {
            forceLogout();
        }
    }, intervalMs);
};
const processApiResponse = (response)=>{
    // Check if it's a standard datatable success response format
    if (response?.data?.meta !== undefined && response.data.data) {
        return response.data;
    }
    // Check if it's a standard success response format
    if (response?.data?.data) {
        return response.data.data;
    } else if (response.data) {
        return response.data;
    }
    return response.data;
};
const checkTokenExpiry = (warningMinutes = 5)=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
    if (!token) return;
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const expiryTime = payload.exp * 1000; // Convert to milliseconds
        const currentTime = Date.now();
        const timeUntilExpiry = expiryTime - currentTime;
        const warningTime = warningMinutes * 60 * 1000; // Convert to milliseconds
        if (timeUntilExpiry <= warningTime && timeUntilExpiry > 0) {
            console.warn(`Token will expire in ${Math.floor(timeUntilExpiry / 60000)} minutes`);
        // You can add a toast notification here if needed
        }
    } catch (error) {
        console.error('Error checking token expiry:', error);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/apiClient.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "apiClient": (()=>apiClient),
    "auditApiClient": (()=>auditApiClient),
    "authApiClient": (()=>authApiClient),
    "createApiClient": (()=>createApiClient),
    "rolesApiClient": (()=>rolesApiClient),
    "usersApiClient": (()=>usersApiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
;
;
;
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:3001") || 'http://localhost:3001';
const createApiClient = (baseURL = API_BASE_URL)=>{
    const instance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
        baseURL,
        timeout: 120000,
        headers: {
            'Content-Type': 'application/json'
        }
    });
    // Request interceptor to add auth token and rate limiting
    instance.interceptors.request.use({
        "createApiClient.use": async (config)=>{
            // // Apply rate limiting based on endpoint
            // const endpoint = config.url || '';
            // const rateLimitKey = endpoint.includes('license-types') || endpoint.includes('license-categories')
            //   ? 'license-data'
            //   : 'general';
            // // Check rate limit before making request
            // await apiRateLimiter.checkRateLimit(rateLimitKey);
            const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            // Log request for debugging (only in development)
            if ("TURBOPACK compile-time truthy", 1) {
                console.log(`[Staff API] Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);
            }
            return config;
        }
    }["createApiClient.use"], {
        "createApiClient.use": (error)=>{
            console.error('Request interceptor error:', error);
            return Promise.reject(error);
        }
    }["createApiClient.use"]);
    // Response interceptor for error handling and auto-logout
    instance.interceptors.response.use({
        "createApiClient.use": (response)=>{
            return response;
        }
    }["createApiClient.use"], {
        "createApiClient.use": async (error)=>{
            const originalRequest = error.config;
            // Handle 429 Rate Limiting (same as customer API client)
            if (error.response?.status === 429) {
                // Ensure originalRequest exists and has the required properties
                if (originalRequest && !originalRequest._retry) {
                    originalRequest._retry = true;
                    // Get retry delay from headers or use exponential backoff
                    const retryAfter = error.response.headers['retry-after'];
                    const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);
                    originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;
                    // Don't retry more than 3 times
                    if (originalRequest._retryCount <= 10) {
                        if ("TURBOPACK compile-time truthy", 1) {
                            console.warn(`[Staff API] Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);
                        }
                        await new Promise({
                            "createApiClient.use": (resolve)=>setTimeout(resolve, delay)
                        }["createApiClient.use"]);
                        return instance(originalRequest);
                    } else {
                        // Exhausted retries
                        if ("TURBOPACK compile-time truthy", 1) {
                            console.error('[Staff API] Rate limiting exhausted retries:', {
                                url: error.config?.url,
                                method: error.config?.method,
                                retryCount: originalRequest._retryCount
                            });
                        }
                    }
                } else {
                    // Already retrying or no original request
                    if ("TURBOPACK compile-time truthy", 1) {
                        console.warn('[Staff API] Rate limited but already retrying or no original request');
                    }
                }
            }
            // Only log detailed errors in development (for non-rate-limiting errors or exhausted retries)
            if (("TURBOPACK compile-time value", "development") === 'development' && error.response?.status !== 429) {
                console.error('API Error Details:', {
                    url: error.config?.url,
                    method: error.config?.method,
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    message: error.message,
                    code: error.code,
                    data: error.response?.data || 'No response data',
                    headers: error.response?.headers || 'No response headers',
                    stack: error.stack
                });
            }
            // Log network errors separately for better debugging
            if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
                console.error('Network Error Details:', {
                    baseURL: error.config?.baseURL,
                    url: error.config?.url,
                    method: error.config?.method,
                    timeout: error.config?.timeout,
                    message: error.message,
                    code: error.code
                });
            }
            // Handle authentication errors - auto logout on 401
            if (error.response?.status === 401) {
                console.warn('Authentication failed - token invalid or expired. Forcing logout...');
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forceLogout"])();
                return Promise.reject(new Error('Authentication failed. Please log in again.'));
            }
            // Handle authorization errors
            if (error.response?.status === 403) {
                console.warn('Access denied - insufficient permissions');
            // You can add a toast notification here
            }
            // Handle validation/conflict errors (duplicate registration, etc.)
            if (error.response?.status === 409 || error.response?.status === 422) {
                const errorData = error.response?.data;
                console.warn('Validation/Conflict error:', errorData?.message || error.message);
            // Let the calling service handle the specific error message
            }
            // Handle network errors
            if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
                console.error('Network error - backend may not be accessible');
            // You can add a toast notification here
            }
            // Handle timeout errors
            if (error.code === 'ECONNABORTED') {
                console.error('Request timeout - server took too long to respond');
            // You can add a toast notification here
            }
            return Promise.reject(error);
        }
    }["createApiClient.use"]);
    return instance;
};
const apiClient = createApiClient();
const authApiClient = createApiClient(`${API_BASE_URL}/auth`);
const usersApiClient = createApiClient(`${API_BASE_URL}/users`);
const rolesApiClient = createApiClient(`${API_BASE_URL}/roles`);
const auditApiClient = createApiClient(`${API_BASE_URL}/audit-trail`);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/auth.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authService": (()=>authService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
;
;
class AuthService {
    api;
    constructor(){
        // Use the centralized auth API client with proper error handling
        this.api = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApiClient"];
    }
    async login(data) {
        const response = await this.api.post('/login', data);
        // The backend returns data directly, not nested in a data property
        const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        // Check if the auth data is empty (which indicates an error)
        if (!authData || Object.keys(authData).length === 0) {
            throw new Error('Authentication failed - invalid credentials');
        }
        // Validate that we have the required fields
        if (!authData.access_token || !authData.user) {
            throw new Error('Authentication failed - incomplete response');
        }
        return authData;
    }
    async register(data) {
        const response = await this.api.post('/register', data);
        // The backend returns data directly, not nested in a data property
        return response.data;
    }
    async forgotPassword(data) {
        const response = await this.api.post('/forgot-password', data);
        return response.data;
    }
    async resetPassword(data) {
        try {
            console.log('🔄 Calling reset password API:', {
                ...data,
                new_password: '***'
            });
            const response = await this.api.post('/reset-password', data);
            console.log('✅ Reset password API response:', response.data);
            return response.data;
        } catch (error) {
            console.error('❌ Reset password API error:', error);
            throw error;
        }
    }
    async verify2FA(data) {
        try {
            console.log('🔄 Calling verify 2FA API:', data);
            const response = await this.api.post('/verify-2fa', data);
            console.log('✅ Verify 2FA API response:', response.data);
            return response.data;
        } catch (error) {
            console.error('❌ Verify 2FA API error:', error);
            throw error;
        }
    }
    async verifyEmail(data) {
        try {
            console.log('🔄 Calling verify email API:', data);
            const response = await this.api.post('/verify-email', data);
            console.log('✅ Verify email API response:', response.data);
            return response.data;
        } catch (error) {
            console.error('❌ Verify email API error:', error);
            throw error;
        }
    }
    async setupTwoFactorAuth(data) {
        const response = await this.api.post('/setup-2fa', data);
        return response.data;
    }
    async verifyTwoFactorCode(data) {
        const response = await this.api.post('/verify-2fa', data);
        return response.data;
    }
    async generateTwoFactorCode(userId, action) {
        const response = await this.api.post('/generate-2fa', {
            user_id: userId,
            action
        });
        return response.data;
    }
    async refreshToken() {
        const response = await this.api.post('/refresh');
        return response.data;
    }
    setAuthToken(token) {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem('auth_token', token);
        }
    }
    getAuthToken() {
        if ("TURBOPACK compile-time truthy", 1) {
            return localStorage.getItem('auth_token');
        }
        "TURBOPACK unreachable";
    }
    clearAuthToken() {
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.removeItem('auth_token');
        }
    }
}
const authService = new AuthService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/auth.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const AuthProvider = ({ children })=>{
    _s1();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [token, setToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Set mounted to true after hydration
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            setMounted(true);
        }
    }["AuthProvider.useEffect"], []);
    // Start token validation timer when mounted and authenticated
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            if (!mounted || !user || !token) return;
            // Start periodic token validation (check every 5 minutes instead of 1 minute)
            const validationTimer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startTokenValidationTimer"])(300000);
            return ({
                "AuthProvider.useEffect": ()=>{
                    clearInterval(validationTimer);
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], [
        mounted,
        user,
        token
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            if (!mounted) return;
            // Check for existing token on mount with validation
            const initAuth = {
                "AuthProvider.useEffect.initAuth": ()=>{
                    // Get saved data from cookies
                    const savedToken = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('auth_token');
                    const savedUser = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('auth_user');
                    if (savedToken && savedUser) {
                        try {
                            const parsedUser = JSON.parse(savedUser);
                            // Ensure isAdmin property is set for backward compatibility
                            const userWithAdmin = {
                                ...parsedUser,
                                isAdmin: parsedUser.roles?.includes('administrator') || parsedUser.isAdmin || false,
                                isCustomer: parsedUser.roles?.includes('customer')
                            };
                            // Validate token is not expired
                            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTokenExpired"])(savedToken)) {
                                setToken(savedToken);
                                setUser(userWithAdmin);
                                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].setAuthToken(savedToken);
                            } else {
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_token');
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_user');
                                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].clearAuthToken();
                            }
                        } catch (error) {
                            console.error('AuthContext: Failed to parse saved user data:', error);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_token');
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_user');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].clearAuthToken();
                        }
                    } else {
                        console.log('AuthContext: No saved authentication data found');
                    }
                    setLoading(false);
                }
            }["AuthProvider.useEffect.initAuth"];
            // Execute immediately instead of waiting for next render cycle
            initAuth();
        }
    }["AuthProvider.useEffect"], [
        mounted
    ]);
    const login = async (email, password, rememberMe = false)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].login({
                email,
                password
            });
            console.log("=====================================LOGIN RESPONSE======================================");
            console.log('Access Token:', response.access_token ? 'Present' : 'Missing');
            console.log('User Data:', response.user);
            console.log("=================================================================================");
            // Check for account recovery requirement first
            if (response.requiresRecovery) {
                console.log('AuthContext: Account recovery required');
                return {
                    requiresRecovery: true,
                    requiresTwoFactor: response.requiresTwoFactor || false,
                    userId: response.user.user_id,
                    user: response.user,
                    token: response.access_token || ''
                };
            }
            // Validate response structure for normal login
            if (!response || !response.user || !response.access_token) {
                throw new Error('Invalid response from authentication service');
            }
            // Ensure roles is an array
            const roles = Array.isArray(response.user.roles) ? response.user.roles : [];
            // Add computed isAdmin property for backward compatibility
            const userWithAdmin = {
                ...response.user,
                roles,
                isAdmin: roles.includes('administrator'),
                isCustomer: roles.includes('customer')
            };
            // Check for 2FA requirement first, before setting user/token in context
            if (response.user) {
                // Use backend's requiresTwoFactor decision if available, otherwise fall back to old logic
                const backendRequires2FA = response.requiresTwoFactor !== undefined ? response.requiresTwoFactor : response.user.two_factor_enabled;
                if (backendRequires2FA) {
                    if (response.user.two_factor_enabled) {
                        // User has 2FA enabled and backend requires verification
                        console.log('AuthContext: 2FA verification required by backend');
                        return {
                            requiresTwoFactor: true,
                            userId: response.user.user_id,
                            user: userWithAdmin,
                            token: response.access_token
                        };
                    } else {
                        // User doesn't have 2FA enabled, they need to set it up
                        console.log('AuthContext: 2FA not enabled, requiring 2FA setup');
                        return {
                            requiresTwoFactor: true,
                            userId: response.user.user_id,
                            user: userWithAdmin,
                            token: response.access_token
                        };
                    }
                } else {
                    // Backend says no 2FA required, proceed with normal login
                    console.log('AuthContext: No 2FA required by backend, proceeding with login');
                    setUser(userWithAdmin);
                    setToken(response.access_token);
                    // Set cookies with appropriate expiration based on rememberMe
                    const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('auth_token', response.access_token, {
                        expires: cookieExpiration
                    });
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('auth_user', JSON.stringify(userWithAdmin), {
                        expires: cookieExpiration
                    });
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].setAuthToken(response.access_token);
                    return {
                        requiresTwoFactor: false,
                        userId: response.user.user_id,
                        user: userWithAdmin,
                        token: response.access_token
                    };
                }
            }
        } catch (error) {
            console.error('AuthContext: Login failed', error);
            throw error;
        }
    };
    const completeTwoFactorLogin = async (token, userData, rememberMe = false)=>{
        try {
            // Ensure roles is an array
            const roles = Array.isArray(userData.roles) ? userData.roles : [];
            // Add computed isAdmin property for backward compatibility
            const userWithAdmin = {
                ...userData,
                roles,
                isAdmin: roles.includes('administrator'),
                isCustomer: roles.includes('customer')
            };
            if ("TURBOPACK compile-time truthy", 1) {
                console.log('AuthContext: 2FA login successful, setting user', userWithAdmin);
            }
            setToken(token);
            setUser(userWithAdmin);
            // Set cookies with appropriate expiration based on rememberMe
            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('auth_token', token, {
                expires: cookieExpiration
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('auth_user', JSON.stringify(userWithAdmin), {
                expires: cookieExpiration
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].setAuthToken(token);
        } catch (error) {
            console.error('AuthContext: 2FA login completion failed', error);
            throw error;
        }
    };
    const register = async (userData)=>{
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].register(userData);
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('AuthContext: Registration successful - user should login manually');
        }
        // Don't automatically log in the user after registration
        // User should be redirected to login page to manually log in
        // This follows the requirement that after account creation,
        // users should be redirected to login page, not dashboard
        return result;
    };
    const logout = ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('AuthContext: Logging out user');
        }
        // Clear state
        setUser(null);
        setToken(null);
        // Remove from cookies
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_token');
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove('auth_user');
        // Clear auth service token
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].clearAuthToken();
        // Clear any other localStorage items related to auth
        if (mounted && "object" !== 'undefined') {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_preferences');
            // Clear any cached data
            sessionStorage.clear();
        }
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('AuthContext: Logout complete');
        }
    };
    const updateUser = (updatedUser)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('AuthContext: Updating user', updatedUser);
        }
        // Convert roles to string array if they're objects
        let roles = [];
        if (updatedUser.roles) {
            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');
        }
        // Ensure isAdmin property is set for backward compatibility
        const userWithAdmin = {
            user_id: updatedUser.user_id,
            email: updatedUser.email,
            first_name: updatedUser.first_name,
            last_name: updatedUser.last_name,
            middle_name: updatedUser.middle_name,
            phone: updatedUser.phone,
            status: updatedUser.status,
            profile_image: updatedUser.profile_image,
            roles: roles,
            isAdmin: roles.includes('administrator') || updatedUser.isAdmin || false,
            isCustomer: roles.includes('customer')
        };
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('AuthContext: Setting updated user', userWithAdmin);
        }
        setUser(userWithAdmin);
        // Update cookies with new user data
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set('auth_user', JSON.stringify(userWithAdmin), {
            expires: 1
        });
    };
    const value = {
        user,
        token,
        login,
        completeTwoFactorLogin,
        register,
        logout,
        updateUser,
        loading: loading || !mounted,
        isAuthenticated: mounted && !!user && !!token
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 345,
        columnNumber: 10
    }, this);
};
_s1(AuthProvider, "DPNJ2QaTjDInasaRQJSlbg1DbxM=");
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Loader.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
'use client';
;
;
const Loader = ({ message = 'Loading...' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "text-center",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative w-20 h-20 mx-auto",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "absolute inset-0 animate-spin",
                        viewBox: "0 0 50 50",
                        fill: "none",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                    id: "fadeGradient",
                                    x1: "0%",
                                    y1: "0%",
                                    x2: "100%",
                                    y2: "0%",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                            offset: "0%",
                                            stopColor: "#dc2626",
                                            stopOpacity: "0"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Loader.tsx",
                                            lineNumber: 17,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                            offset: "20%",
                                            stopColor: "#dc2626",
                                            stopOpacity: "1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Loader.tsx",
                                            lineNumber: 18,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                            offset: "80%",
                                            stopColor: "#dc2626",
                                            stopOpacity: "1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Loader.tsx",
                                            lineNumber: 19,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                            offset: "100%",
                                            stopColor: "#dc2626",
                                            stopOpacity: "0"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Loader.tsx",
                                            lineNumber: 20,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/Loader.tsx",
                                    lineNumber: 16,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/Loader.tsx",
                                lineNumber: 15,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                cx: "25",
                                cy: "25",
                                r: "20",
                                stroke: "rgba(255, 255, 255, 0.0)",
                                strokeWidth: "2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/Loader.tsx",
                                lineNumber: 24,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                cx: "25",
                                cy: "25",
                                r: "20",
                                stroke: "url(#fadeGradient)",
                                strokeWidth: "1",
                                strokeDasharray: "70",
                                strokeDashoffset: "10",
                                fill: "none"
                            }, void 0, false, {
                                fileName: "[project]/src/components/Loader.tsx",
                                lineNumber: 31,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Loader.tsx",
                        lineNumber: 10,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        src: "/images/macra-logo.png",
                        alt: "MACRA Logo",
                        width: 40,
                        height: 40,
                        className: "object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"
                    }, void 0, false, {
                        fileName: "[project]/src/components/Loader.tsx",
                        lineNumber: 44,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Loader.tsx",
                lineNumber: 8,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-4 text-gray-600",
                children: message
            }, void 0, false, {
                fileName: "[project]/src/components/Loader.tsx",
                lineNumber: 54,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Loader.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
_c = Loader;
const __TURBOPACK__default__export__ = Loader;
var _c;
__turbopack_context__.k.register(_c, "Loader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/LoadingContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoadingProvider": (()=>LoadingProvider),
    "useLoading": (()=>useLoading)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Loader.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
const LoadingContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useLoading = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LoadingContext);
    if (!context) {
        throw new Error('useLoading must be used within a LoadingProvider');
    }
    return context;
};
_s(useLoading, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const LoadingProvider = ({ children })=>{
    _s1();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [loadingMessage, setLoadingMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('Loading...');
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    // Handle route changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LoadingProvider.useEffect": ()=>{
            const handleStart = {
                "LoadingProvider.useEffect.handleStart": ()=>{
                    setIsLoading(true);
                    setLoadingMessage('Loading page...');
                }
            }["LoadingProvider.useEffect.handleStart"];
            const handleComplete = {
                "LoadingProvider.useEffect.handleComplete": ()=>{
                    // Add a small delay to ensure smooth transition
                    setTimeout({
                        "LoadingProvider.useEffect.handleComplete": ()=>{
                            setIsLoading(false);
                        }
                    }["LoadingProvider.useEffect.handleComplete"], 300);
                }
            }["LoadingProvider.useEffect.handleComplete"];
            // Listen for route changes
            handleStart();
            handleComplete();
        }
    }["LoadingProvider.useEffect"], [
        pathname
    ]);
    const setLoading = (loading)=>{
        setIsLoading(loading);
    };
    const showLoader = (message = 'Loading...')=>{
        setLoadingMessage(message);
        setIsLoading(true);
    };
    const hideLoader = ()=>{
        setIsLoading(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingContext.Provider, {
        value: {
            isLoading,
            setLoading,
            showLoader,
            hideLoader
        },
        children: [
            children,
            isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    message: loadingMessage
                }, void 0, false, {
                    fileName: "[project]/src/contexts/LoadingContext.tsx",
                    lineNumber: 70,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/contexts/LoadingContext.tsx",
                lineNumber: 69,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/contexts/LoadingContext.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
};
_s1(LoadingProvider, "fK/dt0VJ+PjXyUgC6cduD5lEeF4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = LoadingProvider;
var _c;
__turbopack_context__.k.register(_c, "LoadingProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/ThemeContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider),
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])();
function ThemeProvider({ children }) {
    _s();
    const [theme, setTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('system');
    const [resolvedTheme, setResolvedTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Set mounted to true after hydration
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            setMounted(true);
        }
    }["ThemeProvider.useEffect"], []);
    // Initialize theme from localStorage or system preference
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (!mounted) return;
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme && [
                'light',
                'dark',
                'system'
            ].includes(savedTheme)) {
                setTheme(savedTheme);
            } else {
                setTheme('system');
            }
        }
    }["ThemeProvider.useEffect"], [
        mounted
    ]);
    // Update resolved theme based on current theme setting
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (!mounted) return;
            const updateResolvedTheme = {
                "ThemeProvider.useEffect.updateResolvedTheme": ()=>{
                    if (theme === 'system') {
                        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                        setResolvedTheme(systemTheme);
                    } else {
                        setResolvedTheme(theme);
                    }
                }
            }["ThemeProvider.useEffect.updateResolvedTheme"];
            updateResolvedTheme();
            // Listen for system theme changes when using system theme
            if (theme === 'system') {
                const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                mediaQuery.addEventListener('change', updateResolvedTheme);
                return ({
                    "ThemeProvider.useEffect": ()=>mediaQuery.removeEventListener('change', updateResolvedTheme)
                })["ThemeProvider.useEffect"];
            }
        }
    }["ThemeProvider.useEffect"], [
        theme,
        mounted
    ]);
    // Apply theme to document
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (!mounted) return;
            if (resolvedTheme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
    }["ThemeProvider.useEffect"], [
        resolvedTheme,
        mounted
    ]);
    const setThemePreference = (newTheme)=>{
        setTheme(newTheme);
        if (mounted) {
            localStorage.setItem('theme', newTheme);
        }
    };
    const toggleTheme = ()=>{
        const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';
        setThemePreference(newTheme);
    };
    // Prevent hydration mismatch by not rendering until mounted
    if (!mounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
            value: {
                theme: 'light',
                resolvedTheme: 'light',
                setTheme: ()=>{},
                toggleTheme: ()=>{}
            },
            children: children
        }, void 0, false, {
            fileName: "[project]/src/lib/ThemeContext.js",
            lineNumber: 78,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: {
            theme,
            resolvedTheme,
            setTheme: setThemePreference,
            toggleTheme
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/ThemeContext.js",
        lineNumber: 90,
        columnNumber: 5
    }, this);
}
_s(ThemeProvider, "udEhJh4aDwsFlu4t4WOodprnWR4=");
_c = ThemeProvider;
function useTheme() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
_s1(useTheme, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/Toast.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
const Toast = ({ message, type, duration = 5000, onClose })=>{
    _s();
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Toast.useEffect": ()=>{
            const timer = setTimeout({
                "Toast.useEffect.timer": ()=>{
                    setIsVisible(false);
                    setTimeout(onClose, 300); // Wait for fade out animation
                }
            }["Toast.useEffect.timer"], duration);
            return ({
                "Toast.useEffect": ()=>clearTimeout(timer)
            })["Toast.useEffect"];
        }
    }["Toast.useEffect"], [
        duration,
        onClose
    ]);
    const getToastStyles = ()=>{
        switch(type){
            case 'success':
                return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300';
            case 'error':
                return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300';
            case 'warning':
                return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300';
            case 'info':
                return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300';
            default:
                return 'bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-300';
        }
    };
    const getIcon = ()=>{
        switch(type){
            case 'success':
                return 'ri-check-circle-line';
            case 'error':
                return 'ri-error-warning-line';
            case 'warning':
                return 'ri-alert-line';
            case 'info':
                return 'ri-information-line';
            default:
                return 'ri-information-line';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `relative max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'} ${getToastStyles()}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-start",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-shrink-0",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: `${getIcon()} text-lg`
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/Toast.tsx",
                        lineNumber: 62,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/Toast.tsx",
                    lineNumber: 61,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "ml-3 flex-1",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm font-medium",
                        children: message
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/Toast.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/Toast.tsx",
                    lineNumber: 64,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "ml-4 flex-shrink-0",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>{
                            setIsVisible(false);
                            setTimeout(onClose, 300);
                        },
                        className: "inline-flex rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "sr-only",
                                children: "Dismiss"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/Toast.tsx",
                                lineNumber: 76,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-close-line text-sm"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/Toast.tsx",
                                lineNumber: 77,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/Toast.tsx",
                        lineNumber: 68,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/Toast.tsx",
                    lineNumber: 67,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/Toast.tsx",
            lineNumber: 60,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Toast.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
};
_s(Toast, "m22S9IQwDfEe/fCJY7LYj8YPDMo=");
_c = Toast;
const __TURBOPACK__default__export__ = Toast;
var _c;
__turbopack_context__.k.register(_c, "Toast");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/ToastContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ToastProvider": (()=>ToastProvider),
    "useToast": (()=>useToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Toast$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Toast.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
const ToastContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const ToastProvider = ({ children })=>{
    _s();
    const [toasts, setToasts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const showToast = (message, type, duration)=>{
        const id = Math.random().toString(36).substr(2, 9);
        const newToast = {
            id,
            message,
            type,
            duration
        };
        setToasts((prev)=>[
                ...prev,
                newToast
            ]);
    };
    const removeToast = (id)=>{
        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));
    };
    const showSuccess = (message, duration)=>{
        showToast(message, 'success', duration);
    };
    const showError = (message, duration)=>{
        showToast(message, 'error', duration);
    };
    const showWarning = (message, duration)=>{
        showToast(message, 'warning', duration);
    };
    const showInfo = (message, duration)=>{
        showToast(message, 'info', duration);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ToastContext.Provider, {
        value: {
            showToast,
            showSuccess,
            showError,
            showWarning,
            showInfo
        },
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "toast-container",
                children: toasts.map((toast, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "toast-wrapper",
                        "data-index": index,
                        "data-toast-index": index,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Toast$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            message: toast.message,
                            type: toast.type,
                            duration: toast.duration,
                            onClose: ()=>removeToast(toast.id)
                        }, void 0, false, {
                            fileName: "[project]/src/contexts/ToastContext.tsx",
                            lineNumber: 77,
                            columnNumber: 13
                        }, this)
                    }, toast.id, false, {
                        fileName: "[project]/src/contexts/ToastContext.tsx",
                        lineNumber: 71,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/contexts/ToastContext.tsx",
                lineNumber: 69,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/contexts/ToastContext.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
};
_s(ToastProvider, "nD8TBOiFYf9ajstmZpZK2DP4rNo=");
_c = ToastProvider;
const useToast = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};
_s1(useToast, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "ToastProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/NoSSR.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>NoSSR)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function NoSSR({ children, fallback = null }) {
    _s();
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NoSSR.useEffect": ()=>{
            setMounted(true);
        }
    }["NoSSR.useEffect"], []);
    if (!mounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: fallback
        }, void 0, false);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(NoSSR, "LrrVfNW3d1raFE0BNzCTILYmIfo=");
_c = NoSSR;
var _c;
__turbopack_context__.k.register(_c, "NoSSR");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ClientWrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ClientWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/LoadingContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ThemeContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ThemeContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ToastContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Loader.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$NoSSR$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/NoSSR.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
function ClientWrapper({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$NoSSR$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                message: "Initializing application..."
            }, void 0, false, {
                fileName: "[project]/src/components/ClientWrapper.tsx",
                lineNumber: 19,
                columnNumber: 9
            }, void 0)
        }, void 0, false, {
            fileName: "[project]/src/components/ClientWrapper.tsx",
            lineNumber: 18,
            columnNumber: 7
        }, void 0),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ThemeContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoadingProvider"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ToastProvider"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthProvider"], {
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/components/ClientWrapper.tsx",
                        lineNumber: 25,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ClientWrapper.tsx",
                    lineNumber: 24,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ClientWrapper.tsx",
                lineNumber: 23,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ClientWrapper.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ClientWrapper.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
_c = ClientWrapper;
var _c;
__turbopack_context__.k.register(_c, "ClientWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/LoadingOptimizer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
const LoadingOptimizer = ({ children })=>{
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LoadingOptimizer.useEffect": ()=>{
            // Optimize images with lazy loading
            const optimizeImages = {
                "LoadingOptimizer.useEffect.optimizeImages": ()=>{
                    const images = document.querySelectorAll('img[data-src]');
                    const imageObserver = new IntersectionObserver({
                        "LoadingOptimizer.useEffect.optimizeImages": (entries)=>{
                            entries.forEach({
                                "LoadingOptimizer.useEffect.optimizeImages": (entry)=>{
                                    if (entry.isIntersecting) {
                                        const img = entry.target;
                                        img.src = img.dataset.src || '';
                                        img.classList.remove('lazy');
                                        imageObserver.unobserve(img);
                                    }
                                }
                            }["LoadingOptimizer.useEffect.optimizeImages"]);
                        }
                    }["LoadingOptimizer.useEffect.optimizeImages"]);
                    images.forEach({
                        "LoadingOptimizer.useEffect.optimizeImages": (img)=>imageObserver.observe(img)
                    }["LoadingOptimizer.useEffect.optimizeImages"]);
                }
            }["LoadingOptimizer.useEffect.optimizeImages"];
            // Optimize page performance
            const optimizePerformance = {
                "LoadingOptimizer.useEffect.optimizePerformance": ()=>{
                    // Add will-change to elements that will animate
                    const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');
                    animatedElements.forEach({
                        "LoadingOptimizer.useEffect.optimizePerformance": (el)=>{
                            el.style.willChange = 'transform, background-color';
                        }
                    }["LoadingOptimizer.useEffect.optimizePerformance"]);
                    // Optimize scroll performance
                    document.documentElement.style.scrollBehavior = 'smooth';
                }
            }["LoadingOptimizer.useEffect.optimizePerformance"];
            // Run optimizations after DOM is ready
            const timer = setTimeout({
                "LoadingOptimizer.useEffect.timer": ()=>{
                    optimizeImages();
                    optimizePerformance();
                }
            }["LoadingOptimizer.useEffect.timer"], 100);
            return ({
                "LoadingOptimizer.useEffect": ()=>{
                    clearTimeout(timer);
                    // Cleanup will-change properties
                    const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');
                    animatedElements.forEach({
                        "LoadingOptimizer.useEffect": (el)=>{
                            el.style.willChange = 'auto';
                        }
                    }["LoadingOptimizer.useEffect"]);
                }
            })["LoadingOptimizer.useEffect"];
        }
    }["LoadingOptimizer.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
};
_s(LoadingOptimizer, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = LoadingOptimizer;
const __TURBOPACK__default__export__ = LoadingOptimizer;
var _c;
__turbopack_context__.k.register(_c, "LoadingOptimizer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_8ae10245._.js.map