'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import EvaluationLayout from '@/components/evaluation/EvaluationLayout';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { EvaluationForm } from '@/components/evaluation';



interface EvaluateApplicantInfoPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateApplicantInfoPage: React.FC<EvaluateApplicantInfoPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<any>(null);
  const [applicantData, setApplicantData] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);
  const [attachments, setAttachments] = useState<File[]>([]);

  // Dynamic navigation hook - same as apply pages
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep,
    previousStep,
    currentStep,
    totalSteps,
    licenseTypeCode: navLicenseTypeCode
  } = useDynamicNavigation({
    currentStepRoute: 'applicant-info',
    licenseCategoryId,
    applicationId
  });

  // Debug navigation
  useEffect(() => {
    console.log('🔍 Navigation Debug:', {
      licenseCategoryId,
      applicationId,
      licenseType,
      nextStep,
      previousStep,
      currentStep,
      totalSteps,
      navLicenseTypeCode
    });
  }, [licenseCategoryId, applicationId, licenseType, nextStep, previousStep, currentStep, totalSteps, navLicenseTypeCode]);

  // Load application and applicant data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);

        // Load application details
        const appResponse = await applicationService.getApplication(applicationId);
        setApplication(appResponse);

        // Set license category ID for navigation
        if (appResponse?.license_category_id) {
          setLicenseCategoryId(appResponse.license_category_id);
        }

        // Load applicant data if available
        if (appResponse.applicant_id) {
          try {
            const applicantResponse = await applicantService.getApplicant(appResponse.applicant_id);
            setApplicantData(applicantResponse);
          } catch (err) {
            console.error('Error loading applicant data:', err);
            // Continue without applicant data
          }
        }
      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated]);


  // Navigation handlers - modified for evaluation
  const handleNext = () => {
    if (!applicationId || !nextStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${nextStep.route}?${params.toString()}`);
  };

  const handlePrevious = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  // Evaluation handlers
  const handleStatusUpdate = async (status: string, comment: string, formAttachments?: File[]) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);

      // Use attachments from form or state
      const filesToUpload = formAttachments || attachments;

      // Use the evaluation service to update status with email notification
      const { evaluationService } = await import('@/services/evaluationService');
      await evaluationService.updateApplicationStatusWithEmail(applicationId, {
        status,
        comment,
        attachments: filesToUpload,
        step: 'applicant-info'
      });

      // Show success message
      console.log('Status updated and email sent successfully');
      // You might want to add a toast notification here

      // Clear attachments after successful submission
      setAttachments([]);

      // Optionally redirect or refresh data
      // router.push('/applications/license-management');
    } catch (err) {
      console.error('Error updating status:', err);
      setError('Failed to update application status');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSave = async (comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      // For now, just log the action - implement actual API call later
      console.log('Comment save:', { applicationId, step: 'applicant-info', comment });

      console.log('Comment saved successfully');
    } catch (err) {
      console.error('Error saving comment:', err);
      setError('Failed to save comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachmentUpload = async (file: File) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);

      // Add file to attachments state for later use in status update
      setAttachments(prev => [...prev, file]);

      console.log('Attachment added:', { applicationId, step: 'applicant-info', fileName: file.name });
      console.log('Attachment will be uploaded when status is updated');
    } catch (err) {
      console.error('Error handling attachment:', err);
      setError('Failed to handle attachment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  return (
      <EvaluationLayout
        applicationId={applicationId!}
        licenseTypeCode={licenseType}
        currentStepRoute="applicant-info"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
      {/* Applicant Information Display */}
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Organization Name
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
              <p className="text-gray-900 dark:text-gray-100">
                {applicantData?.name || 'Not provided'}
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Business Registration Number
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
              <p className="text-gray-900 dark:text-gray-100">
                {applicantData?.business_registration_number || 'Not provided'}
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              TPIN
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
              <p className="text-gray-900 dark:text-gray-100">
                {applicantData?.tpin || 'Not provided'}
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Website
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
              <p className="text-gray-900 dark:text-gray-100">
                {applicantData?.website || 'Not provided'}
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Email
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
              <p className="text-gray-900 dark:text-gray-100">
                {applicantData?.email || 'Not provided'}
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Phone
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
              <p className="text-gray-900 dark:text-gray-100">
                {applicantData?.phone || 'Not provided'}
              </p>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Date of Incorporation
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
              <p className="text-gray-900 dark:text-gray-100">
                {applicantData?.date_incorporation ? 
                  new Date(applicantData.date_incorporation).toLocaleDateString() : 
                  'Not provided'
                }
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Place of Incorporation
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border">
              <p className="text-gray-900 dark:text-gray-100">
                {applicantData?.place_incorporation || 'Not provided'}
              </p>
            </div>
          </div>
        </div>

        {/* Evaluation Form */}
        <EvaluationForm
          applicationId={applicationId!}
          currentStep="applicant-info"
          onStatusUpdate={handleStatusUpdate}
          onCommentSave={handleCommentSave}
          onAttachmentUpload={handleAttachmentUpload}
          isSubmitting={isSubmitting}
        />
      </div>
    </EvaluationLayout>
  );
};

export default EvaluateApplicantInfoPage;
