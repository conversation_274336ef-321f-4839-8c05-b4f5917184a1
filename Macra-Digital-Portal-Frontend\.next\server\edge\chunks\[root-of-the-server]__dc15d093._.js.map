{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport type { NextRequest } from 'next/server';\r\n\r\n/**\r\n * Check if a JWT token is expired\r\n * @param token - JWT token to check\r\n * @returns true if token is expired, false otherwise\r\n */\r\nconst isTokenExpired = (token: string): boolean => {\r\n  if (!token) return true;\r\n\r\n  try {\r\n    // Decode JWT payload (without verification - just for expiry check)\r\n    const payload = JSON.parse(atob(token.split('.')[1]));\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n\r\n    // Check if token has expired\r\n    return payload.exp < currentTime;\r\n  } catch (error) {\r\n    console.error('Error decoding token:', error);\r\n    return true; // Treat invalid tokens as expired\r\n  }\r\n};\r\n\r\nexport function middleware(request: NextRequest) {\r\n  const url = request.nextUrl.clone();\r\n\r\n  // Get auth tokens from cookies\r\n  const authToken = request.cookies.get('auth_token');\r\n  const authUser = request.cookies.get('auth_user');\r\n\r\n  // Parse user data if available\r\n  let user = null;\r\n\r\n\r\n  // Try to get user from customer auth first, then staff auth\r\n  if (authUser) {\r\n    try {\r\n      user = JSON.parse(authUser.value);\r\n    } catch (error) {\r\n      console.error('Failed to parse user data:', error);\r\n    }\r\n  }\r\n\r\n\r\n  // Always allow auth routes without redirection\r\n  if (url.pathname.startsWith('/customer/auth/') || url.pathname.startsWith('/auth/')) {\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // Handle root path redirections\r\n  if (url.pathname === '/') {\r\n    if (user && user.roles && user.roles.includes('customer')) {\r\n      // Check if customer token is expired\r\n      if (authToken && isTokenExpired(authToken.value)) {\r\n        url.pathname = '/customer/auth/login';\r\n        return NextResponse.redirect(url);\r\n      } else if (!user.two_factor_enabled) {\r\n        url.pathname = '/customer/auth/setup-2fa';\r\n        return NextResponse.redirect(url);\r\n      } else {\r\n        url.pathname = '/customer';\r\n        return NextResponse.redirect(url);\r\n      }\r\n    } else if (authToken) {\r\n      console.log('Auth token exists')\r\n      // Check if token is expired\r\n      if (isTokenExpired(authToken.value)) {\r\n        url.pathname = '/auth/verify-login';\r\n        return NextResponse.redirect(url);\r\n      } else {\r\n        url.pathname = '/dashboard';\r\n        return NextResponse.redirect(url);\r\n      }\r\n    } else {\r\n      console.log('No auth token')\r\n      url.pathname = '/customer/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n  }\r\n\r\n\r\n  // Handle customer routes\r\n  if (url.pathname.startsWith('/customer')) {\r\n    // Check if user exists and has customer role\r\n    if (!user || !user.roles || !user.roles.includes('customer')) {\r\n      url.pathname = '/customer/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // For other customer routes, check authentication and token expiry\r\n    if (!authToken || !user) {\r\n      url.pathname = '/customer/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // Check if token is expired\r\n    if (isTokenExpired(authToken.value)) {\r\n      url.pathname = '/customer/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // Allow authenticated customer users to access customer portal\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // Handle admin/staff dashboard routes\r\n  if (url.pathname.startsWith('/dashboard')) {\r\n    // If user is authenticated and is a customer, redirect to customer portal\r\n    if (user && user.roles && user.roles.includes('customer')) {\r\n      url.pathname = '/customer';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // If user is not authenticated, check referrer to determine which login page\r\n    if (!user || !user.roles) {\r\n      const referer = request.headers.get('referer');\r\n\r\n      // If coming from customer auth pages, redirect to customer login\r\n      if (referer && referer.includes('/customer/auth')) {\r\n        url.pathname = '/customer/auth/login';\r\n        return NextResponse.redirect(url);\r\n      }\r\n\r\n      // Default to admin login for dashboard access\r\n      url.pathname = '/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // Check authentication and token expiry for admin users\r\n    if (!authToken) {\r\n      url.pathname = '/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // Check if token is expired\r\n    if (isTokenExpired(authToken.value)) {\r\n      url.pathname = '/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // Allow authenticated admin/staff users to access dashboard\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // Handle root path and other routes\r\n  if (url.pathname === '/' || url.pathname === '/dashboard') {\r\n    if (!user || !user.roles) {\r\n      // Not authenticated, redirect to admin login\r\n      url.pathname = '/auth/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // Redirect based on user role\r\n    if (user.roles.includes('customer')) {\r\n      url.pathname = '/customer';\r\n      return NextResponse.redirect(url);\r\n    } else {\r\n      // Admin/staff user\r\n      url.pathname = '/dashboard';\r\n      return NextResponse.redirect(url);\r\n    }\r\n  }\r\n\r\n  // For other routes, allow access\r\n  return NextResponse.next();\r\n\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - images (public images)\r\n     */\r\n    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA;;;;CAIC,GACD,MAAM,iBAAiB,CAAC;IACtB,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI;QACF,oEAAoE;QACpE,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAE5C,6BAA6B;QAC7B,OAAO,QAAQ,GAAG,GAAG;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,MAAM,kCAAkC;IACjD;AACF;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;IAEjC,+BAA+B;IAC/B,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;IAErC,+BAA+B;IAC/B,IAAI,OAAO;IAGX,4DAA4D;IAC5D,IAAI,UAAU;QACZ,IAAI;YACF,OAAO,KAAK,KAAK,CAAC,SAAS,KAAK;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAGA,+CAA+C;IAC/C,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,sBAAsB,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW;QACnF,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,gCAAgC;IAChC,IAAI,IAAI,QAAQ,KAAK,KAAK;QACxB,IAAI,QAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa;YACzD,qCAAqC;YACrC,IAAI,aAAa,eAAe,UAAU,KAAK,GAAG;gBAChD,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B,OAAO,IAAI,CAAC,KAAK,kBAAkB,EAAE;gBACnC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B,OAAO;gBACL,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF,OAAO,IAAI,WAAW;YACpB,QAAQ,GAAG,CAAC;YACZ,4BAA4B;YAC5B,IAAI,eAAe,UAAU,KAAK,GAAG;gBACnC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B,OAAO;gBACL,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAGA,yBAAyB;IACzB,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc;QACxC,6CAA6C;QAC7C,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa;YAC5D,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,mEAAmE;QACnE,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,4BAA4B;QAC5B,IAAI,eAAe,UAAU,KAAK,GAAG;YACnC,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,+DAA+D;QAC/D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,sCAAsC;IACtC,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,eAAe;QACzC,0EAA0E;QAC1E,IAAI,QAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa;YACzD,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,6EAA6E;QAC7E,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;YACxB,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;YAEpC,iEAAiE;YACjE,IAAI,WAAW,QAAQ,QAAQ,CAAC,mBAAmB;gBACjD,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,8CAA8C;YAC9C,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,wDAAwD;QACxD,IAAI,CAAC,WAAW;YACd,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,4BAA4B;QAC5B,IAAI,eAAe,UAAU,KAAK,GAAG;YACnC,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,4DAA4D;QAC5D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,oCAAoC;IACpC,IAAI,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,cAAc;QACzD,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;YACxB,6CAA6C;YAC7C,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,8BAA8B;QAC9B,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa;YACnC,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B,OAAO;YACL,mBAAmB;YACnB,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,iCAAiC;IACjC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAE1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}