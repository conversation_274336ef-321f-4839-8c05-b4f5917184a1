import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from '../dto/document/create-document.dto';
import { UpdateDocumentDto } from '../dto/document/update-document.dto';
import { Documents } from '../entities/documents.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class DocumentsController {
    private readonly documentsService;
    constructor(documentsService: DocumentsService);
    create(createDocumentDto: CreateDocumentDto, req: any): Promise<Documents>;
    uploadFile(file: Express.Multer.File, uploadData: any, req: any): Promise<any>;
    findAll(query: PaginateQuery, req: any): Promise<PaginatedResult<Documents>>;
    getStats(): Promise<any>;
    getTotalFileSize(): Promise<{
        totalSize: number;
    }>;
    findByApplication(applicationId: string): Promise<Documents[]>;
    findByEntity(entityType: string, entityId: string): Promise<Documents[]>;
    findByDocumentType(documentType: string): Promise<Documents[]>;
    findByMimeType(mimeType: string): Promise<Documents[]>;
    findRequiredDocuments(): Promise<Documents[]>;
    findOne(id: string): Promise<Documents>;
    update(id: string, updateDocumentDto: UpdateDocumentDto, req: any): Promise<Documents>;
    downloadDocument(id: string, res: any): Promise<void>;
    previewDocument(id: string, res: any): Promise<void>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
