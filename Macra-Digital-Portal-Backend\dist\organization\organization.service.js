"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OrganizationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const organization_entity_1 = require("../entities/organization.entity");
let OrganizationService = OrganizationService_1 = class OrganizationService {
    orgRepo;
    logger = new common_1.Logger(OrganizationService_1.name);
    constructor(orgRepo) {
        this.orgRepo = orgRepo;
    }
    async create(dto) {
        const organization = this.orgRepo.create(dto);
        return this.orgRepo.save(organization);
    }
    async findAll() {
        return this.orgRepo.find({
            relations: ['physical_address', 'postal_address', 'contact', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findOne(id) {
        const organization = await this.orgRepo.findOne({
            where: { organization_id: id },
            relations: ['physical_address', 'postal_address', 'contact', 'creator', 'updater'],
        });
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${id} not found`);
        }
        return organization;
    }
    async update(id, dto) {
        const organization = await this.findOne(id);
        const updated = this.orgRepo.merge(organization, dto);
        return this.orgRepo.save(updated);
    }
    async remove(id) {
        const organization = await this.findOne(id);
        await this.orgRepo.softRemove(organization);
        this.logger.log(`Soft-deleted organization with ID ${id}`);
    }
    async hardDelete(id) {
        const organization = await this.findOne(id);
        await this.orgRepo.remove(organization);
        this.logger.warn(`Permanently deleted organization with ID ${id}`);
    }
};
exports.OrganizationService = OrganizationService;
exports.OrganizationService = OrganizationService = OrganizationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(organization_entity_1.Organization)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], OrganizationService);
//# sourceMappingURL=organization.service.js.map