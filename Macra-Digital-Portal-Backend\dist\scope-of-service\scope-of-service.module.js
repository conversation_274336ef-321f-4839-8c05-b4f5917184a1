"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScopeOfServiceModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const scope_of_service_controller_1 = require("./scope-of-service.controller");
const scope_of_service_service_1 = require("./scope-of-service.service");
const scope_of_service_entity_1 = require("../entities/scope-of-service.entity");
let ScopeOfServiceModule = class ScopeOfServiceModule {
};
exports.ScopeOfServiceModule = ScopeOfServiceModule;
exports.ScopeOfServiceModule = ScopeOfServiceModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([scope_of_service_entity_1.ScopeOfService])],
        controllers: [scope_of_service_controller_1.ScopeOfServiceController],
        providers: [scope_of_service_service_1.ScopeOfServiceService],
        exports: [scope_of_service_service_1.ScopeOfServiceService],
    })
], ScopeOfServiceModule);
//# sourceMappingURL=scope-of-service.module.js.map