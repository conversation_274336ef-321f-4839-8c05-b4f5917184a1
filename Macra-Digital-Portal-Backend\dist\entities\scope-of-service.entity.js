"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScopeOfService = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const applications_entity_1 = require("./applications.entity");
let ScopeOfService = class ScopeOfService {
    scope_of_service_id;
    application_id;
    nature_of_service;
    premises;
    transport_type;
    customer_assistance;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    application;
    creator;
    updater;
    generateId() {
        if (!this.scope_of_service_id) {
            this.scope_of_service_id = (0, uuid_1.v4)();
        }
    }
};
exports.ScopeOfService = ScopeOfService;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], ScopeOfService.prototype, "scope_of_service_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ScopeOfService.prototype, "application_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300 }),
    __metadata("design:type", String)
], ScopeOfService.prototype, "nature_of_service", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300 }),
    __metadata("design:type", String)
], ScopeOfService.prototype, "premises", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300 }),
    __metadata("design:type", String)
], ScopeOfService.prototype, "transport_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300 }),
    __metadata("design:type", String)
], ScopeOfService.prototype, "customer_assistance", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ScopeOfService.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ScopeOfService.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ScopeOfService.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ScopeOfService.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], ScopeOfService.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applications_entity_1.Applications),
    (0, typeorm_1.JoinColumn)({ name: 'application_id' }),
    __metadata("design:type", applications_entity_1.Applications)
], ScopeOfService.prototype, "application", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ScopeOfService.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], ScopeOfService.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ScopeOfService.prototype, "generateId", null);
exports.ScopeOfService = ScopeOfService = __decorate([
    (0, typeorm_1.Entity)('scope_of_service')
], ScopeOfService);
//# sourceMappingURL=scope-of-service.entity.js.map