{"version": 3, "file": "license-category-documents.seeder.js", "sourceRoot": "", "sources": ["../../../src/database/seeders/license-category-documents.seeder.ts"], "names": [], "mappings": ";;AACA,sGAA0F;AAC1F,wFAA6E;AAM7E,MAAqB,8BAA8B;IAC1C,KAAK,CAAC,GAAG,CAAC,UAAsB;QACrC,MAAM,kBAAkB,GAAG,UAAU,CAAC,aAAa,CAAC,0DAAuB,CAAC,CAAC;QAC7E,MAAM,kBAAkB,GAAG,UAAU,CAAC,aAAa,CAAC,6CAAiB,CAAC,CAAC;QAGvE,MAAM,aAAa,GAAG,MAAM,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACvD,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;YAC5E,OAAO;QACT,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,kBAAkB,CAAC,IAAI,EAAE,CAAC;QACnD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,KAAK,CAAC,0EAA0E,CAAC,CAAC;YAC1F,OAAO;QACT,CAAC;QAGD,MAAM,iBAAiB,GAAG;YACxB,eAAe;YACf,kBAAkB;YAClB,iBAAiB;YACjB,iCAAiC;YACjC,6DAA6D;YAC7D,kBAAkB;YAClB,mCAAmC;YACnC,iDAAiD;YACjD,oDAAoD;YACpD,kCAAkC;YAClC,mEAAmE;YACnE,sCAAsC;YACtC,4BAA4B;YAC5B,8BAA8B;YAC9B,yBAAyB;YACzB,gCAAgC;YAChC,iCAAiC;YACjC,+CAA+C;SAChD,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAGrD,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,oCAAoC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAEjE,KAAK,MAAM,YAAY,IAAI,iBAAiB,EAAE,CAAC;gBAC7C,MAAM,QAAQ,GAAG,kBAAkB,CAAC,MAAM,CAAC;oBACzC,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB;oBACjD,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,IAAI;iBAClB,CAAC,CAAC;gBAEH,MAAM,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,yBAAyB,YAAY,EAAE,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC/D,CAAC;CACF;AA7DD,iDA6DC"}