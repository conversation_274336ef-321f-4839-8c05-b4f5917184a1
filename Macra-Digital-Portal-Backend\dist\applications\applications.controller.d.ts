import { ApplicationsService } from './applications.service';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { Applications } from '../entities/applications.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class ApplicationsController {
    private readonly applicationsService;
    constructor(applicationsService: ApplicationsService);
    create(createApplicationDto: CreateApplicationDto, req: any): Promise<Applications>;
    findAll(query: PaginateQuery, req: any): Promise<PaginatedResult<Applications>>;
    getStats(): Promise<any>;
    findByApplicant(applicantId: string): Promise<Applications[]>;
    findByStatus(status: string): Promise<Applications[]>;
    getMyApplications(query: PaginateQuery, req: any): Promise<PaginatedResult<Applications>>;
    findOne(id: string): Promise<Applications>;
    update(id: string, updateApplicationDto: UpdateApplicationDto, req: any): Promise<Applications>;
    updateStatus(id: string, status: string, req: any): Promise<Applications>;
    updateProgress(id: string, currentStep: number, progressPercentage: number, req: any): Promise<Applications>;
    remove(id: string): Promise<{
        message: string;
    }>;
    assignApplication(id: string, assignData: {
        assignedTo: string;
    }, req: any): Promise<Applications>;
    getUnassignedApplications(query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<Applications>>;
    getMyAssignedApplications(query: PaginateQuery, req: any): Promise<import("nestjs-paginate").Paginated<Applications>>;
    getDebugApplications(query: PaginateQuery): Promise<PaginatedResult<Applications>>;
    testTaskCreation(id: string, req: any): Promise<{
        success: boolean;
        message: string;
        taskId?: string;
    }>;
    forceSubmitApplication(id: string, req: any): Promise<{
        success: boolean;
        message: string;
        previousStatus?: string;
        newStatus?: string;
    }>;
    checkNotifications(id: string): Promise<{
        success: boolean;
        message: string;
        details?: any;
    }>;
}
