import { User } from './user.entity';
export declare enum ClientSystemStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    MAINTENANCE = "maintenance",
    DEPRECATED = "deprecated"
}
export declare enum ClientSystemType {
    WEB_APPLICATION = "web_application",
    MOBILE_APP = "mobile_app",
    API_CLIENT = "api_client",
    THIRD_PARTY_INTEGRATION = "third_party_integration",
    INTERNAL_SYSTEM = "internal_system"
}
export declare class ClientSystems {
    client_system_id: string;
    name: string;
    system_code: string;
    description?: string;
    system_type: ClientSystemType;
    status: ClientSystemStatus;
    api_endpoint?: string;
    callback_url?: string;
    contact_email?: string;
    contact_phone?: string;
    organization?: string;
    access_permissions?: string;
    last_accessed_at?: Date;
    version?: string;
    notes?: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    creator?: User;
    updater?: User;
    generateId(): void;
}
