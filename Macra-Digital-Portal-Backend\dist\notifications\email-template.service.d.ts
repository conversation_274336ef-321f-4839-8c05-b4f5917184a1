export interface EmailTemplateData {
    [key: string]: any;
}
export declare class EmailTemplateService {
    generateApplicationSubmittedTemplate(data: {
        applicantName: string;
        applicationNumber: string;
        licenseType: string;
        submissionDate: string;
        portalUrl?: string;
    }): {
        subject: string;
        html: string;
    };
    generateTaskAssignedTemplate(data: {
        assigneeName: string;
        taskTitle: string;
        taskDescription: string;
        applicationNumber: string;
        applicantName: string;
        priority: string;
        dueDate?: string;
        portalUrl?: string;
    }): {
        subject: string;
        html: string;
    };
    generateTaskCompletedTemplate(data: {
        applicantName: string;
        taskTitle: string;
        applicationNumber: string;
        licenseType: string;
        completionDate: string;
        outcome: string;
        comments?: string;
        nextSteps?: string;
        portalUrl?: string;
    }): {
        subject: string;
        html: string;
    };
    generateApplicationStatusChangeTemplate(data: {
        applicantName: string;
        applicationNumber: string;
        licenseType: string;
        oldStatus: string;
        newStatus: string;
        changeDate: string;
        comments?: string;
        portalUrl?: string;
    }): {
        subject: string;
        html: string;
    };
    generateLicenseApprovedTemplate(data: {
        applicantName: string;
        applicationNumber: string;
        licenseType: string;
        licenseNumber: string;
        approvalDate: string;
        expiryDate: string;
        portalUrl?: string;
    }): {
        subject: string;
        html: string;
    };
}
