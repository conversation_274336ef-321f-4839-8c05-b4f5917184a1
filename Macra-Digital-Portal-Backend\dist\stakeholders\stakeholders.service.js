"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StakeholdersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const stakeholders_entity_1 = require("../entities/stakeholders.entity");
const uuid_1 = require("uuid");
let StakeholdersService = class StakeholdersService {
    stakeholderRepository;
    constructor(stakeholderRepository) {
        this.stakeholderRepository = stakeholderRepository;
    }
    async create(dto, createdBy) {
        const stakeholder = this.stakeholderRepository.create({
            ...dto,
            stakeholder_id: (0, uuid_1.v4)(),
            created_by: createdBy,
        });
        return await this.stakeholderRepository.save(stakeholder);
    }
    async findAll() {
        return await this.stakeholderRepository.find({
            where: { deleted_at: undefined },
        });
    }
    async findOne(id) {
        const stakeholder = await this.stakeholderRepository.findOne({
            where: { stakeholder_id: id, deleted_at: undefined },
        });
        if (!stakeholder) {
            throw new common_1.NotFoundException(`Stakeholder with ID ${id} not found`);
        }
        return stakeholder;
    }
    async findByApplicant(applicationId) {
        return await this.stakeholderRepository.find({
            where: { application_id: applicationId, deleted_at: undefined },
            order: { created_at: 'ASC' }
        });
    }
    async update(id, dto, updatedBy) {
        const stakeholder = await this.findOne(id);
        Object.assign(stakeholder, dto, { updated_by: updatedBy });
        return await this.stakeholderRepository.save(stakeholder);
    }
    async softDelete(id) {
        const stakeholder = await this.findOne(id);
        await this.stakeholderRepository.softRemove(stakeholder);
    }
};
exports.StakeholdersService = StakeholdersService;
exports.StakeholdersService = StakeholdersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(stakeholders_entity_1.Stakeholder)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], StakeholdersService);
//# sourceMappingURL=stakeholders.service.js.map