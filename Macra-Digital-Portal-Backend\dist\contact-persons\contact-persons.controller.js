"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactPersonsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const contact_persons_service_1 = require("./contact-persons.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_contact_person_dto_1 = require("../dto/contact-person/create-contact-person.dto");
const update_contact_person_dto_1 = require("../dto/contact-person/update-contact-person.dto");
const contact_persons_entity_1 = require("../entities/contact-persons.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let ContactPersonsController = class ContactPersonsController {
    contactPersonsService;
    constructor(contactPersonsService) {
        this.contactPersonsService = contactPersonsService;
    }
    async create(createContactPersonDto, req) {
        return this.contactPersonsService.create(createContactPersonDto, req.user.userId);
    }
    async findAll(query) {
        const result = await this.contactPersonsService.findAll(query);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async search(searchTerm) {
        return this.contactPersonsService.search(searchTerm);
    }
    async findByApplication(applicationId) {
        return this.contactPersonsService.findByApplicationId(applicationId);
    }
    async findByApplicationGrouped(applicationId) {
        const result = await this.contactPersonsService.getContactPersonsByApplicant(applicationId);
        return {
            ...result,
            emergency: []
        };
    }
    async findPrimaryByApplication(applicationId) {
        return this.contactPersonsService.findPrimaryByApplicationId(applicationId);
    }
    async findOne(id) {
        return this.contactPersonsService.findOne(id);
    }
    async update(id, updateContactPersonDto, req) {
        return this.contactPersonsService.update(id, updateContactPersonDto, req.user.userId);
    }
    async setPrimary(id, body, req) {
        return this.contactPersonsService.setPrimaryContact(body.application_id, id, req.user.userId);
    }
    async remove(id) {
        await this.contactPersonsService.remove(id);
        return { message: 'Contact person deleted successfully' };
    }
};
exports.ContactPersonsController = ContactPersonsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new contact person' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Contact person created successfully',
        type: contact_persons_entity_1.ContactPersons,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Created new contact person',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_contact_person_dto_1.CreateContactPersonDto, Object]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all contact persons with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact persons retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Viewed contact persons list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Search contact persons' }),
    (0, swagger_1.ApiQuery)({ name: 'q', description: 'Search term' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Search results retrieved successfully',
        type: [contact_persons_entity_1.ContactPersons],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Searched contact persons',
    }),
    __param(0, (0, common_1.Query)('q')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get contact persons by application ID' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact persons retrieved successfully',
        type: [contact_persons_entity_1.ContactPersons],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Viewed contact persons by application',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "findByApplication", null);
__decorate([
    (0, common_1.Get)('application/:applicationId/grouped'),
    (0, swagger_1.ApiOperation)({ summary: 'Get contact persons by application ID grouped by type' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact persons retrieved and grouped successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Viewed grouped contact persons by application',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "findByApplicationGrouped", null);
__decorate([
    (0, common_1.Get)('application/:applicationId/primary'),
    (0, swagger_1.ApiOperation)({ summary: 'Get primary contact person by application ID' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Primary contact person retrieved successfully',
        type: contact_persons_entity_1.ContactPersons,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Viewed primary contact person by application',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "findPrimaryByApplication", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get contact person by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact person UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact person retrieved successfully',
        type: contact_persons_entity_1.ContactPersons,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Viewed contact person details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update contact person' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact person UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact person updated successfully',
        type: contact_persons_entity_1.ContactPersons,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Updated contact person',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_contact_person_dto_1.UpdateContactPersonDto, Object]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "update", null);
__decorate([
    (0, common_1.Put)(':id/set-primary'),
    (0, swagger_1.ApiOperation)({ summary: 'Set contact person as primary for application' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact person UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Primary contact person set successfully',
        type: contact_persons_entity_1.ContactPersons,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Set primary contact person',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "setPrimary", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete contact person' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact person UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contact person deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ContactPerson',
        description: 'Deleted contact person',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContactPersonsController.prototype, "remove", null);
exports.ContactPersonsController = ContactPersonsController = __decorate([
    (0, swagger_1.ApiTags)('contact-persons'),
    (0, common_1.Controller)('contact-persons'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [contact_persons_service_1.ContactPersonsService])
], ContactPersonsController);
//# sourceMappingURL=contact-persons.controller.js.map