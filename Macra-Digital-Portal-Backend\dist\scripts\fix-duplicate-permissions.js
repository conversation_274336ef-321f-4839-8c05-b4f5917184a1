"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.fixDuplicatePermissions = fixDuplicatePermissions;
const typeorm_1 = require("typeorm");
const dotenv = __importStar(require("dotenv"));
dotenv.config();
async function fixDuplicatePermissions() {
    const dataSource = new typeorm_1.DataSource({
        type: process.env.DB_DRIVER,
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT || '3306', 10),
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        entities: [],
        ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    });
    try {
        await dataSource.initialize();
        console.log('✅ Database connection established');
        console.log('🔍 Checking for duplicate/empty permission names...');
        const emptyPermissions = await dataSource.query(`SELECT permission_id, name, description FROM permissions WHERE name = '' OR name IS NULL`);
        console.log(`Found ${emptyPermissions.length} permissions with empty/null names`);
        if (emptyPermissions.length > 0) {
            console.log('📋 Empty permissions found:');
            emptyPermissions.forEach((perm, index) => {
                console.log(`  ${index + 1}. ID: ${perm.permission_id}, Name: "${perm.name}", Description: "${perm.description}"`);
            });
            if (emptyPermissions.length > 1) {
                console.log('🗑️ Removing duplicate empty permissions...');
                const toDelete = emptyPermissions.slice(1);
                for (const perm of toDelete) {
                    await dataSource.query(`DELETE FROM permissions WHERE permission_id = ?`, [perm.permission_id]);
                    console.log(`   Deleted permission with ID: ${perm.permission_id}`);
                }
            }
            const remainingEmpty = await dataSource.query(`SELECT permission_id FROM permissions WHERE name = '' OR name IS NULL LIMIT 1`);
            if (remainingEmpty.length > 0) {
                console.log('🔧 Updating remaining empty permission with proper name...');
                await dataSource.query(`UPDATE permissions SET name = ?, description = ?, category = ? WHERE permission_id = ?`, ['system:placeholder', 'Placeholder permission (auto-generated to fix duplicate issue)', 'System', remainingEmpty[0].permission_id]);
                console.log('   Updated empty permission with name: system:placeholder');
            }
        }
        console.log('🔍 Checking for other duplicate permission names...');
        const duplicates = await dataSource.query(`SELECT name, COUNT(*) as count FROM permissions
       WHERE name IS NOT NULL AND name != ''
       GROUP BY name
       HAVING COUNT(*) > 1`);
        if (duplicates.length > 0) {
            console.log('⚠️ Found other duplicate permission names:');
            duplicates.forEach((dup) => {
                console.log(`  Name: "${dup.name}" appears ${dup.count} times`);
            });
            for (const dup of duplicates) {
                const duplicatePerms = await dataSource.query(`SELECT permission_id, name FROM permissions WHERE name = ?`, [dup.name]);
                console.log(`🔧 Fixing duplicates for "${dup.name}"...`);
                for (let i = 1; i < duplicatePerms.length; i++) {
                    const perm = duplicatePerms[i];
                    const newName = `${dup.name}_duplicate_${i}`;
                    await dataSource.query(`UPDATE permissions SET name = ? WHERE permission_id = ?`, [newName, perm.permission_id]);
                    console.log(`   Renamed duplicate to: ${newName}`);
                }
            }
        }
        console.log('✅ Duplicate permission cleanup completed');
        const finalCheck = await dataSource.query(`SELECT name, COUNT(*) as count FROM permissions
       GROUP BY name
       HAVING COUNT(*) > 1`);
        if (finalCheck.length === 0) {
            console.log('✅ No duplicate permission names found - ready for unique index creation');
        }
        else {
            console.log('⚠️ Still found duplicates:');
            finalCheck.forEach((dup) => {
                console.log(`  Name: "${dup.name}" appears ${dup.count} times`);
            });
        }
    }
    catch (error) {
        console.error('❌ Error fixing duplicate permissions:', error);
        throw error;
    }
    finally {
        await dataSource.destroy();
        console.log('🔌 Database connection closed');
    }
}
if (require.main === module) {
    fixDuplicatePermissions()
        .then(() => {
        console.log('🎉 Script completed successfully');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Script failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=fix-duplicate-permissions.js.map