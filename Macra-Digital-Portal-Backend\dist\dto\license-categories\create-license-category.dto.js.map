{"version": 3, "file": "create-license-category.dto.js", "sourceRoot": "", "sources": ["../../../src/dto/license-categories/create-license-category.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAsF;AACtF,6CAA8C;AAE9C,MAAa,wBAAwB;IAOnC,eAAe,CAAS;IASxB,SAAS,CAAU;IAUnB,IAAI,CAAS;IAUb,GAAG,CAAS;IAQZ,WAAW,CAAS;IAQpB,UAAU,CAAS;CACpB;AArDD,4DAqDC;AA9CC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,+CAA+C;QAC5D,OAAO,EAAE,sCAAsC;KAChD,CAAC;IACD,IAAA,wBAAM,GAAE;IACR,IAAA,4BAAU,GAAE;;iEACW;AASxB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,2DAA2D;QACxE,OAAO,EAAE,sCAAsC;QAC/C,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,wBAAM,GAAE;IACR,IAAA,4BAAU,GAAE;;2DACM;AAUnB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;QAC3C,OAAO,EAAE,qBAAqB;QAC9B,SAAS,EAAE,GAAG;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,EAAC,GAAG,CAAC;;sDACF;AAUb;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,+BAA+B;QAC5C,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,EAAE;KACd,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,EAAC,EAAE,CAAC;;qDACF;AAQZ;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE,oDAAoD;KAC9D,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6DACO;AAQpB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uCAAuC;QACpD,OAAO,EAAE,wEAAwE;KAClF,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;4DACM"}