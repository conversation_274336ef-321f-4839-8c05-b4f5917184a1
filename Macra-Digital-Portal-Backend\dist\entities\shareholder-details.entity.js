"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareholderDetails = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const stakeholders_entity_1 = require("./stakeholders.entity");
let ShareholderDetails = class ShareholderDetails {
    shareholder_id;
    stakeholder_id;
    shareholding_percent;
    description;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    stakeholder;
    creator;
    updater;
    generateId() {
        if (!this.shareholder_id) {
            this.shareholder_id = (0, uuid_1.v4)();
        }
    }
};
exports.ShareholderDetails = ShareholderDetails;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], ShareholderDetails.prototype, "shareholder_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ShareholderDetails.prototype, "stakeholder_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], ShareholderDetails.prototype, "shareholding_percent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300, nullable: true }),
    __metadata("design:type", String)
], ShareholderDetails.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ShareholderDetails.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ShareholderDetails.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ShareholderDetails.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ShareholderDetails.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], ShareholderDetails.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => stakeholders_entity_1.Stakeholder),
    (0, typeorm_1.JoinColumn)({ name: 'stakeholder_id' }),
    __metadata("design:type", stakeholders_entity_1.Stakeholder)
], ShareholderDetails.prototype, "stakeholder", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ShareholderDetails.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], ShareholderDetails.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ShareholderDetails.prototype, "generateId", null);
exports.ShareholderDetails = ShareholderDetails = __decorate([
    (0, typeorm_1.Entity)('shareholder_details')
], ShareholderDetails);
//# sourceMappingURL=shareholder-details.entity.js.map