"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateNotificationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const swagger_2 = require("@nestjs/swagger");
const notifications_entity_1 = require("../../entities/notifications.entity");
const create_notification_dto_1 = require("./create-notification.dto");
class UpdateNotificationDto extends (0, swagger_1.PartialType)(create_notification_dto_1.CreateNotificationDto) {
    status;
    external_id;
    error_message;
    retry_count;
    is_read;
    sent_at;
    delivered_at;
    read_at;
}
exports.UpdateNotificationDto = UpdateNotificationDto;
__decorate([
    (0, swagger_2.ApiPropertyOptional)({
        description: 'Notification status',
        enum: notifications_entity_1.NotificationStatus
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(notifications_entity_1.NotificationStatus),
    __metadata("design:type", String)
], UpdateNotificationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_2.ApiPropertyOptional)({
        description: 'External provider message ID'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateNotificationDto.prototype, "external_id", void 0);
__decorate([
    (0, swagger_2.ApiPropertyOptional)({
        description: 'Error message if notification failed'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateNotificationDto.prototype, "error_message", void 0);
__decorate([
    (0, swagger_2.ApiPropertyOptional)({
        description: 'Number of retry attempts'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], UpdateNotificationDto.prototype, "retry_count", void 0);
__decorate([
    (0, swagger_2.ApiPropertyOptional)({
        description: 'Whether notification has been read'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateNotificationDto.prototype, "is_read", void 0);
__decorate([
    (0, swagger_2.ApiPropertyOptional)({
        description: 'When notification was sent'
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], UpdateNotificationDto.prototype, "sent_at", void 0);
__decorate([
    (0, swagger_2.ApiPropertyOptional)({
        description: 'When notification was delivered'
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], UpdateNotificationDto.prototype, "delivered_at", void 0);
__decorate([
    (0, swagger_2.ApiPropertyOptional)({
        description: 'When notification was read'
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], UpdateNotificationDto.prototype, "read_at", void 0);
//# sourceMappingURL=update-notification.dto.js.map