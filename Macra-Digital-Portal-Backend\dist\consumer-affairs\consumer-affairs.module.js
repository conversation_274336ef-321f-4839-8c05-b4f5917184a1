"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsumerAffairsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const consumer_affairs_complaint_controller_1 = require("./consumer-affairs-complaint.controller");
const consumer_affairs_complaint_service_1 = require("./consumer-affairs-complaint.service");
const consumer_affairs_complaint_entity_1 = require("./consumer-affairs-complaint.entity");
let ConsumerAffairsModule = class ConsumerAffairsModule {
};
exports.ConsumerAffairsModule = ConsumerAffairsModule;
exports.ConsumerAffairsModule = ConsumerAffairsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                consumer_affairs_complaint_entity_1.ConsumerAffairsComplaint,
                consumer_affairs_complaint_entity_1.ConsumerAffairsComplaintAttachment,
                consumer_affairs_complaint_entity_1.ConsumerAffairsComplaintStatusHistory,
            ]),
        ],
        controllers: [consumer_affairs_complaint_controller_1.ConsumerAffairsComplaintController],
        providers: [consumer_affairs_complaint_service_1.ConsumerAffairsComplaintService],
        exports: [consumer_affairs_complaint_service_1.ConsumerAffairsComplaintService],
    })
], ConsumerAffairsModule);
//# sourceMappingURL=consumer-affairs.module.js.map