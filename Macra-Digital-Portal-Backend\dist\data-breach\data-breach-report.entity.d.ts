import { User } from '../entities/user.entity';
export declare enum DataBreachCategory {
    UNAUTHORIZED_ACCESS = "Unauthorized Data Access",
    DATA_MISUSE = "Data Misuse or Sharing",
    PRIVACY_VIOLATIONS = "Privacy Violations",
    IDENTITY_THEFT = "Identity Theft",
    PHISHING_ATTEMPTS = "Phishing Attempts",
    DATA_LOSS = "Data Loss or Theft",
    CONSENT_VIOLATIONS = "Consent Violations",
    OTHER = "Other"
}
export declare enum DataBreachSeverity {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export declare enum DataBreachStatus {
    SUBMITTED = "submitted",
    UNDER_REVIEW = "under_review",
    INVESTIGATING = "investigating",
    RESOLVED = "resolved",
    CLOSED = "closed"
}
export declare enum DataBreachPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare class DataBreachReport {
    report_id: string;
    report_number: string;
    reporter_id: string;
    title: string;
    description: string;
    category: DataBreachCategory;
    severity: DataBreachSeverity;
    status: DataBreachStatus;
    priority: DataBreachPriority;
    incident_date: Date;
    organization_involved: string;
    affected_data_types?: string;
    contact_attempts?: string;
    assigned_to?: string;
    resolution?: string;
    internal_notes?: string;
    resolved_at?: Date;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    created_by?: string;
    updated_by?: string;
    reporter: User;
    assignee?: User;
    creator?: User;
    updater?: User;
    attachments: DataBreachReportAttachment[];
    status_history: DataBreachReportStatusHistory[];
    generateId(): void;
}
export declare class DataBreachReportAttachment {
    attachment_id: string;
    report_id: string;
    file_name: string;
    file_path: string;
    file_type: string;
    file_size: number;
    uploaded_at: Date;
    uploaded_by: string;
    report: DataBreachReport;
    uploader: User;
    generateId(): void;
}
export declare class DataBreachReportStatusHistory {
    history_id: string;
    report_id: string;
    status: DataBreachStatus;
    comment?: string;
    created_at: Date;
    created_by: string;
    report: DataBreachReport;
    creator: User;
    generateId(): void;
}
