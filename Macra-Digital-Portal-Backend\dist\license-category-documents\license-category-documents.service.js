"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseCategoryDocumentsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const nestjs_paginate_1 = require("nestjs-paginate");
const license_category_document_entity_1 = require("../entities/license-category-document.entity");
let LicenseCategoryDocumentsService = class LicenseCategoryDocumentsService {
    licenseCategoryDocumentRepository;
    constructor(licenseCategoryDocumentRepository) {
        this.licenseCategoryDocumentRepository = licenseCategoryDocumentRepository;
    }
    async create(createLicenseCategoryDocumentDto, userId) {
        const existingDocument = await this.licenseCategoryDocumentRepository.findOne({
            where: {
                license_category_id: createLicenseCategoryDocumentDto.license_category_id,
                name: createLicenseCategoryDocumentDto.name,
            },
        });
        if (existingDocument) {
            throw new common_1.ConflictException(`Document with name "${createLicenseCategoryDocumentDto.name}" already exists for this license category`);
        }
        const licenseCategoryDocument = this.licenseCategoryDocumentRepository.create({
            ...createLicenseCategoryDocumentDto,
            is_required: createLicenseCategoryDocumentDto.is_required ?? true,
            created_by: userId,
        });
        return await this.licenseCategoryDocumentRepository.save(licenseCategoryDocument);
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.licenseCategoryDocumentRepository, {
            sortableColumns: ['name', 'is_required', 'created_at', 'updated_at'],
            searchableColumns: ['name'],
            defaultSortBy: [['created_at', 'DESC']],
            relations: ['license_category', 'creator', 'updater'],
            select: [
                'license_category_document_id',
                'license_category_id',
                'name',
                'is_required',
                'created_at',
                'updated_at',
                'license_category.license_category_id',
                'license_category.name',
                'creator.user_id',
                'creator.first_name',
                'creator.last_name',
                'creator.email',
                'updater.user_id',
                'updater.first_name',
                'updater.last_name',
                'updater.email',
            ],
        });
    }
    async findOne(id) {
        const licenseCategoryDocument = await this.licenseCategoryDocumentRepository.findOne({
            where: { license_category_document_id: id },
            relations: ['license_category', 'creator', 'updater'],
        });
        if (!licenseCategoryDocument) {
            throw new common_1.NotFoundException(`License category document with ID ${id} not found`);
        }
        return licenseCategoryDocument;
    }
    async findByLicenseCategory(licenseCategoryId) {
        return await this.licenseCategoryDocumentRepository.find({
            where: { license_category_id: licenseCategoryId },
            relations: ['license_category', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async update(id, updateLicenseCategoryDocumentDto, userId) {
        const licenseCategoryDocument = await this.findOne(id);
        if (updateLicenseCategoryDocumentDto.name && updateLicenseCategoryDocumentDto.name !== licenseCategoryDocument.name) {
            const existingDocument = await this.licenseCategoryDocumentRepository.findOne({
                where: {
                    license_category_id: updateLicenseCategoryDocumentDto.license_category_id || licenseCategoryDocument.license_category_id,
                    name: updateLicenseCategoryDocumentDto.name,
                },
            });
            if (existingDocument && existingDocument.license_category_document_id !== id) {
                throw new common_1.ConflictException(`Document with name "${updateLicenseCategoryDocumentDto.name}" already exists for this license category`);
            }
        }
        Object.assign(licenseCategoryDocument, updateLicenseCategoryDocumentDto);
        licenseCategoryDocument.updated_by = userId;
        return await this.licenseCategoryDocumentRepository.save(licenseCategoryDocument);
    }
    async remove(id) {
        const licenseCategoryDocument = await this.findOne(id);
        await this.licenseCategoryDocumentRepository.softDelete(id);
        return { message: `License category document "${licenseCategoryDocument.name}" has been deleted successfully` };
    }
};
exports.LicenseCategoryDocumentsService = LicenseCategoryDocumentsService;
exports.LicenseCategoryDocumentsService = LicenseCategoryDocumentsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(license_category_document_entity_1.LicenseCategoryDocument)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], LicenseCategoryDocumentsService);
//# sourceMappingURL=license-category-documents.service.js.map