"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTasksTable1736251800000 = void 0;
class CreateTasksTable1736251800000 {
    name = 'CreateTasksTable1736251800000';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS tasks (
        task_id VARCHAR(36) PRIMARY KEY,
        task_number VARCHAR(100) UNIQUE NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        task_type VARCHAR(50) DEFAULT 'application',
        status VARCHAR(50) DEFAULT 'pending',
        priority VARCHAR(50) DEFAULT 'medium',
        entity_type VARCHAR(50) NULL,
        entity_id VARCHAR(36) NULL,
        assigned_to VARCHAR(36) NULL,
        assigned_by VARCHAR(36) NOT NULL,
        assigned_at TIMESTAMP NULL,
        due_date TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        review TEXT NULL,
        review_notes TEXT NULL,
        completion_notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(36) NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(36) NULL,
        deleted_at TIMESTAMP NULL,
        INDEX idx_tasks_assigned_to (assigned_to),
        INDEX idx_tasks_assigned_by (assigned_by),
        INDEX idx_tasks_created_by (created_by),
        INDEX idx_tasks_status (status),
        INDEX idx_tasks_task_type (task_type),
        INDEX idx_tasks_entity_type (entity_type),
        INDEX idx_tasks_entity_id (entity_id),
        INDEX idx_tasks_created_at (created_at),
        CONSTRAINT fk_tasks_assigned_to FOREIGN KEY (assigned_to) REFERENCES users (user_id),
        CONSTRAINT fk_tasks_assigned_by FOREIGN KEY (assigned_by) REFERENCES users (user_id),
        CONSTRAINT fk_tasks_created_by FOREIGN KEY (created_by) REFERENCES users (user_id),
        CONSTRAINT fk_tasks_updated_by FOREIGN KEY (updated_by) REFERENCES users (user_id)
      )
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE tasks`);
    }
}
exports.CreateTasksTable1736251800000 = CreateTasksTable1736251800000;
//# sourceMappingURL=1736251800000-create-tasks-table.js.map