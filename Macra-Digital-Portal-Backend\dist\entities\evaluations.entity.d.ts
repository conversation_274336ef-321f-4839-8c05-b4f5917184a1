import { User } from './user.entity';
import { Applications } from './applications.entity';
export declare enum EvaluationType {
    INDIVIDUAL_LICENSE_A = "individual_license_a",
    CLASS_LICENSE_B = "class_license_b",
    NETWORK_SERVICE = "network_service",
    POSTAL_SERVICE = "postal_service",
    RADIO_COMMUNICATION = "radio_communication",
    SATELLITE_SERVICE = "satellite_service",
    TV_BROADCASTING = "tv_broadcasting",
    UNIVERSITY_RADIO = "university_radio"
}
export declare enum EvaluationStatus {
    DRAFT = "draft",
    COMPLETED = "completed",
    APPROVED = "approved",
    REJECTED = "rejected"
}
export declare enum EvaluationRecommendation {
    APPROVE = "approve",
    CONDITIONAL_APPROVE = "conditional_approve",
    REJECT = "reject"
}
export declare class Evaluations {
    evaluation_id: string;
    application_id: string;
    evaluator_id: string;
    evaluation_type: EvaluationType;
    status: EvaluationStatus;
    total_score: number;
    recommendation: EvaluationRecommendation;
    evaluators_notes?: string;
    shareholding_compliance?: boolean;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    completed_at?: Date;
    application: Applications;
    evaluator: User;
    creator: User;
    updater?: User;
    generateId(): void;
}
