"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationStatusTrackingController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const application_status_tracking_service_1 = require("../services/application-status-tracking.service");
const documents_service_1 = require("../documents/documents.service");
const applications_entity_1 = require("../entities/applications.entity");
const update_application_status_dto_1 = require("../dto/application-status/update-application-status.dto");
let ApplicationStatusTrackingController = class ApplicationStatusTrackingController {
    statusTrackingService;
    documentsService;
    constructor(statusTrackingService, documentsService) {
        this.statusTrackingService = statusTrackingService;
        this.documentsService = documentsService;
    }
    async updateApplicationStatus(applicationId, updateStatusDto, files, req) {
        const userId = req.user?.user_id || req.user?.id;
        if (!userId) {
            throw new Error('User ID not found in request');
        }
        const uploadedDocuments = [];
        if (files && files.length > 0) {
            for (const file of files) {
                try {
                    const document = await this.documentsService.create({
                        document_type: 'OTHER',
                        file_name: file.originalname,
                        entity_type: 'evaluation',
                        entity_id: applicationId,
                        file_path: file.path,
                        file_size: file.size,
                        mime_type: file.mimetype,
                        is_required: false,
                    }, userId);
                    uploadedDocuments.push(document);
                }
                catch (error) {
                    console.error(`Failed to save document ${file.originalname}:`, error);
                }
            }
        }
        const result = await this.statusTrackingService.updateApplicationStatus(applicationId, updateStatusDto, userId);
        return {
            success: true,
            message: 'Application status updated successfully',
            data: result,
            meta: {
                application_id: applicationId,
                updated_by: userId,
                previous_status: result.status_history[0]?.previous_status,
                new_status: result.current_status,
                uploaded_documents: uploadedDocuments.length,
                documents: uploadedDocuments.map(doc => ({
                    document_id: doc.document_id,
                    file_name: doc.file_name,
                    file_size: doc.file_size
                }))
            },
            timestamp: new Date().toISOString()
        };
    }
    async getApplicationStatusTracking(applicationId) {
        const result = await this.statusTrackingService.getApplicationStatusTracking(applicationId);
        return {
            success: true,
            message: 'Application status tracking retrieved successfully',
            data: result,
            meta: {
                application_id: applicationId,
                total_status_changes: result.status_history.length,
                current_status: result.current_status,
                progress_percentage: result.progress_percentage
            },
            timestamp: new Date().toISOString()
        };
    }
    async getApplicationStatusHistory(applicationId) {
        const result = await this.statusTrackingService.getStatusHistory(applicationId);
        return {
            success: true,
            message: 'Application status history retrieved successfully',
            data: result,
            meta: {
                application_id: applicationId,
                total_records: result.length,
                latest_status: result[0]?.status,
                oldest_status: result[result.length - 1]?.status
            },
            timestamp: new Date().toISOString()
        };
    }
    async getApplicationsByStatus(status) {
        const result = await this.statusTrackingService.getApplicationsByStatus(status);
        return {
            success: true,
            message: `Applications with status '${status}' retrieved successfully`,
            data: result,
            meta: {
                status: status,
                total_applications: result.length,
                applications_found: result.length > 0
            },
            timestamp: new Date().toISOString()
        };
    }
    async getAvailableStatuses() {
        const statuses = Object.values(applications_entity_1.ApplicationStatus).map(status => ({
            value: status,
            label: status.replace('_', ' ').toUpperCase(),
            description: this.getStatusDescription(status)
        }));
        return {
            success: true,
            message: 'Application statuses retrieved successfully',
            data: statuses,
            meta: {
                total_statuses: statuses.length
            },
            timestamp: new Date().toISOString()
        };
    }
    getStatusDescription(status) {
        const descriptions = {
            'draft': 'Application is being prepared and has not been submitted yet',
            'submitted': 'Application has been submitted and is awaiting review',
            'under_review': 'Application is being reviewed by MACRA staff',
            'evaluation': 'Application is undergoing technical evaluation',
            'approved': 'Application has been approved and license issued',
            'rejected': 'Application has been rejected',
            'withdrawn': 'Application has been withdrawn by the applicant'
        };
        return descriptions[status] || 'Unknown status';
    }
};
exports.ApplicationStatusTrackingController = ApplicationStatusTrackingController;
__decorate([
    (0, common_1.Put)(':applicationId/status'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('attachments', 10, {
        storage: (0, multer_1.diskStorage)({
            destination: './uploads/evaluations',
            filename: (req, file, callback) => {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
                const ext = (0, path_1.extname)(file.originalname);
                const filename = `evaluation-${uniqueSuffix}${ext}`;
                callback(null, filename);
            },
        }),
        fileFilter: (req, file, callback) => {
            const allowedMimes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'image/jpeg',
                'image/png',
                'image/jpg'
            ];
            if (allowedMimes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new Error('Invalid file type'), false);
            }
        },
        limits: {
            fileSize: 10 * 1024 * 1024,
        },
    })),
    (0, swagger_1.ApiOperation)({
        summary: 'Update application status with optional file attachments',
        description: 'Update the status of an application, create a status history record, and optionally send email notification with attachments'
    }),
    (0, swagger_1.ApiParam)({
        name: 'applicationId',
        description: 'Application ID',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    (0, swagger_1.ApiBody)({
        type: update_application_status_dto_1.UpdateApplicationStatusDto,
        description: 'Status update information'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Application status updated successfully',
        type: update_application_status_dto_1.ApplicationStatusTrackingResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Application not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid status transition'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'Internal server error'
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFiles)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_application_status_dto_1.UpdateApplicationStatusDto, Array, Object]),
    __metadata("design:returntype", Promise)
], ApplicationStatusTrackingController.prototype, "updateApplicationStatus", null);
__decorate([
    (0, common_1.Get)(':applicationId/tracking'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get application status tracking',
        description: 'Get detailed status tracking information for an application including history'
    }),
    (0, swagger_1.ApiParam)({
        name: 'applicationId',
        description: 'Application ID',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Application status tracking retrieved successfully',
        type: update_application_status_dto_1.ApplicationStatusTrackingResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Application not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationStatusTrackingController.prototype, "getApplicationStatusTracking", null);
__decorate([
    (0, common_1.Get)(':applicationId/history'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get application status history',
        description: 'Get the complete status change history for an application'
    }),
    (0, swagger_1.ApiParam)({
        name: 'applicationId',
        description: 'Application ID',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Application status history retrieved successfully',
        type: [update_application_status_dto_1.ApplicationStatusHistoryResponseDto]
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Application not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationStatusTrackingController.prototype, "getApplicationStatusHistory", null);
__decorate([
    (0, common_1.Get)('by-status/:status'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get applications by status',
        description: 'Get all applications with a specific status including their tracking information'
    }),
    (0, swagger_1.ApiParam)({
        name: 'status',
        description: 'Application status',
        enum: applications_entity_1.ApplicationStatus,
        example: applications_entity_1.ApplicationStatus.UNDER_REVIEW
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Applications retrieved successfully',
        type: [update_application_status_dto_1.ApplicationStatusTrackingResponseDto]
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid status value'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationStatusTrackingController.prototype, "getApplicationsByStatus", null);
__decorate([
    (0, common_1.Get)('statuses'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get available application statuses',
        description: 'Get all available application statuses and their descriptions'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Application statuses retrieved successfully'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ApplicationStatusTrackingController.prototype, "getAvailableStatuses", null);
exports.ApplicationStatusTrackingController = ApplicationStatusTrackingController = __decorate([
    (0, swagger_1.ApiTags)('Application Status Tracking'),
    (0, common_1.Controller)('application-status'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [application_status_tracking_service_1.ApplicationStatusTrackingService,
        documents_service_1.DocumentsService])
], ApplicationStatusTrackingController);
//# sourceMappingURL=application-status-tracking.controller.js.map