import * as fs from 'fs';
export declare class FileUploadUtils {
    private static readonly UPLOAD_DIR;
    private static readonly MAX_FILE_SIZE;
    private static readonly ALLOWED_MIME_TYPES;
    static validateFile(file: Express.Multer.File): void;
    static saveFile(file: Express.Multer.File): {
        filename: string;
        filepath: string;
    };
    static deleteFile(filepath: string): void;
    static fileExists(filepath: string): boolean;
    static getFileStats(filepath: string): fs.Stats | null;
    static createFileStream(filepath: string): fs.ReadStream;
    private static ensureUploadDirExists;
    static getMimeTypeFromExtension(filename: string): string;
    static sanitizeFilename(filename: string): string;
    static formatFileSize(bytes: number): string;
}
