import { User } from '../../entities/user.entity';
import { TwoFactorAction } from '../constants/auth.constants';
export interface JwtPayload {
    email: string;
    sub: string;
    roles?: string[];
    iat?: number;
    exp?: number;
}
export interface AuthResponse {
    access_token: string;
    user: AuthUserInfo;
    requiresTwoFactor?: boolean;
    requiresRecovery?: boolean;
    message?: string;
}
export interface AuthUserInfo {
    user_id: string;
    email: string;
    first_name: string;
    last_name: string;
    two_factor_enabled: boolean;
    roles?: string[];
    isStaff?: boolean;
}
export interface TwoFactorCodeResult {
    message: string;
    otpAuthUrl: string;
    hashedToken: string;
    secret: string;
}
export interface TwoFactorSetupResult {
    otpAuthUrl: string;
    qrCodeDataUrl: string;
    secret: string;
    message: string;
}
export interface DeviceInfo {
    ip: string;
    country: string;
    city: string;
    userAgent: string;
}
export interface EmailContext {
    [key: string]: any;
    year?: number;
}
export interface TwoFactorEmailContext extends EmailContext {
    name: string;
    message: string;
    verifyUrl: string;
}
export interface LoginAlertEmailContext extends EmailContext {
    userName: string;
    loginUrl: string;
    ip: string;
    country: string;
    city: string;
    userAgent: string;
    message: string;
}
export interface PasswordResetEmailContext extends EmailContext {
    userName: string;
    loginUrl: string;
}
export declare function isValidTwoFactorAction(action: string): action is TwoFactorAction;
export declare function isAuthResponse(obj: any): obj is AuthResponse;
export declare function isValidEmail(email: string): boolean;
export declare function isValidUserId(userId: string): boolean;
export type UserWithRoles = User & {
    roles: NonNullable<User['roles']>;
};
export type RequiredAuthFields = Required<Pick<User, 'user_id' | 'email' | 'first_name' | 'last_name' | 'two_factor_enabled'>>;
export type SafeUser = Omit<User, 'password' | 'two_factor_code' | 'two_factor_temp'>;
export type LoginResult = Promise<AuthResponse>;
export type RegisterResult = Promise<AuthResponse>;
export type TwoFactorResult = Promise<TwoFactorCodeResult>;
export type TwoFactorSetupPromise = Promise<TwoFactorSetupResult>;
export type PasswordResetResult = Promise<{
    message: string;
}>;
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
}
export interface UserValidationResult extends ValidationResult {
    user?: User;
}
export interface AuthError extends Error {
    code: string;
    context?: Record<string, any>;
}
export declare class AuthenticationError extends Error implements AuthError {
    code: string;
    context?: Record<string, any>;
    constructor(message: string, context?: Record<string, any>);
}
export declare class TwoFactorError extends Error implements AuthError {
    code: string;
    context?: Record<string, any>;
    constructor(message: string, context?: Record<string, any>);
}
