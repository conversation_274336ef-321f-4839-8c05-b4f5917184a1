(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/services/applicationService.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/services/applicationService.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/services/evaluationService.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_services_evaluationService_ts_f5fd223d._.js",
  "static/chunks/src_services_evaluationService_ts_ef66c6a0._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/services/evaluationService.ts [app-client] (ecmascript)");
    });
});
}}),
}]);