"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScopeOfServiceController = void 0;
const common_1 = require("@nestjs/common");
const scope_of_service_service_1 = require("./scope-of-service.service");
const create_scope_of_service_dto_1 = require("../dto/scope-of-service/create-scope-of-service.dto");
const update_scope_of_service_dto_1 = require("../dto/scope-of-service/update-scope-of-service.dto");
const swagger_1 = require("@nestjs/swagger");
const scope_of_service_entity_1 = require("../entities/scope-of-service.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let ScopeOfServiceController = class ScopeOfServiceController {
    scopeOfServiceService;
    constructor(scopeOfServiceService) {
        this.scopeOfServiceService = scopeOfServiceService;
    }
    create(createDto, req) {
        return this.scopeOfServiceService.create(createDto, req.user.userId);
    }
    findAll() {
        return this.scopeOfServiceService.findAll();
    }
    findByApplication(applicationId) {
        return this.scopeOfServiceService.findByApplication(applicationId);
    }
    createOrUpdateForApplication(applicationId, createDto, req) {
        return this.scopeOfServiceService.createOrUpdate(applicationId, createDto, req.user.userId);
    }
    findOne(id) {
        return this.scopeOfServiceService.findOne(id);
    }
    update(id, updateDto, req) {
        return this.scopeOfServiceService.update(id, updateDto, req.user.userId);
    }
    remove(id) {
        return this.scopeOfServiceService.softDelete(id);
    }
};
exports.ScopeOfServiceController = ScopeOfServiceController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new scope of service' }),
    (0, swagger_1.ApiBody)({ type: create_scope_of_service_dto_1.CreateScopeOfServiceDto, description: 'Create scope of service DTO' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Scope of service created successfully', type: scope_of_service_entity_1.ScopeOfService }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_scope_of_service_dto_1.CreateScopeOfServiceDto, Object]),
    __metadata("design:returntype", void 0)
], ScopeOfServiceController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all scope of services' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of scope of services', type: [scope_of_service_entity_1.ScopeOfService] }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ScopeOfServiceController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get scope of service by application ID' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', type: 'string', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Scope of service found', type: scope_of_service_entity_1.ScopeOfService }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('applicationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ScopeOfServiceController.prototype, "findByApplication", null);
__decorate([
    (0, common_1.Post)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Create or update scope of service for application' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', type: 'string', description: 'Application UUID' }),
    (0, swagger_1.ApiBody)({ type: create_scope_of_service_dto_1.CreateScopeOfServiceDto, description: 'Scope of service data (without application_id)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Scope of service created or updated', type: scope_of_service_entity_1.ScopeOfService }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('applicationId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], ScopeOfServiceController.prototype, "createOrUpdateForApplication", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get scope of service by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', description: 'Scope of service UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Scope of service found', type: scope_of_service_entity_1.ScopeOfService }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ScopeOfServiceController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update scope of service by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiBody)({ type: update_scope_of_service_dto_1.UpdateScopeOfServiceDto, description: 'Update scope of service DTO' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Scope of service updated', type: scope_of_service_entity_1.ScopeOfService }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_scope_of_service_dto_1.UpdateScopeOfServiceDto, Object]),
    __metadata("design:returntype", void 0)
], ScopeOfServiceController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete scope of service by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Scope of service deleted' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ScopeOfServiceController.prototype, "remove", null);
exports.ScopeOfServiceController = ScopeOfServiceController = __decorate([
    (0, swagger_1.ApiTags)('Scope of Service'),
    (0, common_1.Controller)('scope-of-service'),
    __metadata("design:paramtypes", [scope_of_service_service_1.ScopeOfServiceService])
], ScopeOfServiceController);
//# sourceMappingURL=scope-of-service.controller.js.map