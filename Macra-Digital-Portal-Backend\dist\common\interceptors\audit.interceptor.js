"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuditInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditInterceptor = exports.Audit = exports.AUDIT_METADATA_KEY = exports.mapApplicationTypeToAuditModule = void 0;
const common_1 = require("@nestjs/common");
require("reflect-metadata");
const operators_1 = require("rxjs/operators");
const audit_trail_service_1 = require("../../audit-trail/audit-trail.service");
const audit_trail_entity_1 = require("../../entities/audit-trail.entity");
const core_1 = require("@nestjs/core");
const mapApplicationTypeToAuditModule = (type) => {
    switch (type.toLowerCase()) {
        case 'postal':
            return audit_trail_entity_1.AuditModule.POSTAL_SERVICES;
        case 'type_approval':
            return audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES;
        case 'short_code':
            return audit_trail_entity_1.AuditModule.SHORT_CODE_SERVICES;
        case 'telecommunications':
            return audit_trail_entity_1.AuditModule.TELECOMMUNICATION_SERVICES;
        case 'broadcasting':
            return audit_trail_entity_1.AuditModule.BROADCASTING_SERVICES;
        default:
            return audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT;
    }
};
exports.mapApplicationTypeToAuditModule = mapApplicationTypeToAuditModule;
exports.AUDIT_METADATA_KEY = 'audit_metadata';
const Audit = (metadata) => {
    return (target, propertyKey, descriptor) => {
        Reflect.defineMetadata(exports.AUDIT_METADATA_KEY, metadata, descriptor.value);
        return descriptor;
    };
};
exports.Audit = Audit;
let AuditInterceptor = AuditInterceptor_1 = class AuditInterceptor {
    auditTrailService;
    moduleRef;
    logger = new common_1.Logger(AuditInterceptor_1.name);
    constructor(auditTrailService, moduleRef) {
        this.auditTrailService = auditTrailService;
        this.moduleRef = moduleRef;
    }
    intercept(context, next) {
        const auditMetadata = Reflect.getMetadata(exports.AUDIT_METADATA_KEY, context.getHandler());
        if (!auditMetadata) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const ipAddress = this.getClientIp(request);
        const userAgent = request.headers['user-agent'];
        const sessionId = request.sessionID || request.headers['x-session-id'];
        const startTime = Date.now();
        let oldValuesPromise = Promise.resolve(null);
        if (auditMetadata.action === audit_trail_entity_1.AuditAction.UPDATE && request.params?.id) {
            oldValuesPromise = this.captureOldValues(auditMetadata.resourceType, request.params.id);
        }
        return next.handle().pipe((0, operators_1.tap)(async (response) => {
            setImmediate(async () => {
                try {
                    const oldValues = await oldValuesPromise;
                    const newValues = this.extractNewValues(auditMetadata.action, request, response);
                    let module = auditMetadata.module;
                    if (!module && request.body?.application_type) {
                        module = (0, exports.mapApplicationTypeToAuditModule)(request.body.application_type);
                    }
                    await this.logAudit({
                        ...auditMetadata,
                        module,
                        status: audit_trail_entity_1.AuditStatus.SUCCESS,
                        userId: request.user?.user_id,
                        ipAddress: request.ip,
                        userAgent: request.headers['user-agent'],
                        sessionId: request.sessionID || request.headers['x-session-id'],
                        oldValues,
                        newValues,
                        metadata: {
                            responseTime: Date.now() - startTime,
                            method: request.method,
                            url: request.url,
                            params: request.params,
                            query: request.query,
                        },
                        resourceId: this.extractResourceId(request, response),
                    });
                }
                catch (auditError) {
                    this.logger.error('Failed to log audit trail for success case:', auditError);
                }
            });
        }), (0, operators_1.catchError)((error) => {
            setImmediate(async () => {
                try {
                    const oldValues = await oldValuesPromise;
                    let module = auditMetadata.module;
                    if (!module && request.body?.application_type) {
                        module = (0, exports.mapApplicationTypeToAuditModule)(request.body.application_type);
                    }
                    await this.logAudit({
                        ...auditMetadata,
                        module,
                        status: audit_trail_entity_1.AuditStatus.FAILURE,
                        userId: request.user?.user_id || undefined,
                        ipAddress: request.ip,
                        userAgent: request.headers['user-agent'] || undefined,
                        sessionId: request.sessionID || request.headers['x-session-id'] || undefined,
                        oldValues,
                        errorMessage: error.message,
                        metadata: {
                            responseTime: Date.now() - startTime,
                            method: request.method,
                            url: request.url,
                            params: request.params,
                            query: request.query,
                            errorStack: error.stack,
                        },
                    });
                }
                catch (auditError) {
                    this.logger.error('Failed to log audit trail for error case:', auditError);
                }
            });
            throw error;
        }));
    }
    async logAudit(data) {
        try {
            await this.auditTrailService.create({
                action: data.action,
                module: data.module,
                status: data.status,
                resourceType: data.resourceType,
                resourceId: data.resourceId,
                description: data.description,
                oldValues: data.oldValues,
                newValues: data.newValues,
                metadata: data.metadata,
                ipAddress: data.ipAddress,
                userAgent: data.userAgent,
                sessionId: data.sessionId,
                errorMessage: data.errorMessage,
                userId: data.userId,
            });
        }
        catch (error) {
            this.logger.error('Failed to log audit trail:', error);
        }
    }
    async captureOldValues(resourceType, resourceId) {
        try {
            if (resourceType === 'User') {
                try {
                    const usersService = await this.moduleRef.resolve('UsersService');
                    if (usersService && typeof usersService.findById === 'function') {
                        const oldUser = await usersService.findById(resourceId);
                        if (oldUser) {
                            const { password, two_factor_code, two_factor_temp, two_factor_next_verification, ...safeOldValues } = oldUser;
                            return safeOldValues;
                        }
                    }
                }
                catch (serviceError) {
                    this.logger.debug('UsersService not available for old values capture', serviceError.message);
                }
            }
            return null;
        }
        catch (error) {
            this.logger.warn('Failed to capture old values:', error);
            return null;
        }
    }
    extractNewValues(action, request, response) {
        try {
            if (action === audit_trail_entity_1.AuditAction.CREATE) {
                if (response && typeof response === 'object') {
                    return this.sanitizeValues(response);
                }
            }
            else if (action === audit_trail_entity_1.AuditAction.UPDATE) {
                if (request.body && typeof request.body === 'object') {
                    return this.sanitizeValues(request.body);
                }
            }
            return null;
        }
        catch (error) {
            this.logger.warn('Failed to extract new values:', error);
            return null;
        }
    }
    sanitizeValues(values) {
        if (!values || typeof values !== 'object') {
            return values;
        }
        const sensitiveFields = [
            'password',
            'two_factor_code',
            'two_factor_temp',
            'two_factor_next_verification',
            'secret',
            'token',
            'refresh_token',
            'access_token',
        ];
        const sanitized = { ...values };
        sensitiveFields.forEach(field => {
            if (field in sanitized) {
                delete sanitized[field];
            }
        });
        Object.keys(sanitized).forEach(key => {
            if (sanitized[key] && typeof sanitized[key] === 'object' && !Array.isArray(sanitized[key])) {
                sanitized[key] = this.sanitizeValues(sanitized[key]);
            }
        });
        return sanitized;
    }
    getClientIp(request) {
        return (request.headers['x-forwarded-for'] ||
            request.headers['x-real-ip'] ||
            request.connection?.remoteAddress ||
            request.socket?.remoteAddress ||
            request.ip ||
            'unknown');
    }
    extractResourceId(request, response) {
        if (request.params?.id) {
            return request.params.id;
        }
        if (response && typeof response === 'object') {
            return response.user_id || response.role_id || response.permission_id || response.id;
        }
        return undefined;
    }
};
exports.AuditInterceptor = AuditInterceptor;
exports.AuditInterceptor = AuditInterceptor = AuditInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [audit_trail_service_1.AuditTrailService,
        core_1.ModuleRef])
], AuditInterceptor);
//# sourceMappingURL=audit.interceptor.js.map