"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeApprovedDevice = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const applications_entity_1 = require("./applications.entity");
const class_validator_1 = require("class-validator");
const type_approved_manufacturer_entity_1 = require("./type_approved_manufacturer.entity");
let TypeApprovedDevice = class TypeApprovedDevice {
    device_id;
    application_id;
    manufacturer_id;
    device_type;
    model_name;
    device_serial_number;
    device_manufacturer;
    device_approval_number;
    device_approval_date;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    application;
    manufacturer;
    creator;
    updater;
    generateId() {
        if (!this.device_id) {
            this.device_id = (0, uuid_1.v4)();
        }
    }
};
exports.TypeApprovedDevice = TypeApprovedDevice;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "device_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "application_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "manufacturer_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "device_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, unique: true }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "model_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, unique: true }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "device_serial_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "device_manufacturer", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "device_approval_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "device_approval_date", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], TypeApprovedDevice.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], TypeApprovedDevice.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], TypeApprovedDevice.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], TypeApprovedDevice.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applications_entity_1.Applications, { onDelete: 'SET NULL', nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'application_id' }),
    __metadata("design:type", applications_entity_1.Applications)
], TypeApprovedDevice.prototype, "application", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => type_approved_manufacturer_entity_1.TypeApprovedManufacturer, { nullable: false }),
    (0, typeorm_1.JoinColumn)({ name: 'manufacturer_id' }),
    __metadata("design:type", type_approved_manufacturer_entity_1.TypeApprovedManufacturer)
], TypeApprovedDevice.prototype, "manufacturer", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], TypeApprovedDevice.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], TypeApprovedDevice.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TypeApprovedDevice.prototype, "generateId", null);
exports.TypeApprovedDevice = TypeApprovedDevice = __decorate([
    (0, typeorm_1.Entity)('type_approved_devices')
], TypeApprovedDevice);
//# sourceMappingURL=type_approved_device.entity.js.map