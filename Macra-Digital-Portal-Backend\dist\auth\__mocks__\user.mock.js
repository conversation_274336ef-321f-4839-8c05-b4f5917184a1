"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockJwtPayload = exports.mockUser = void 0;
exports.mockUser = {
    user_id: '1',
    email: '<EMAIL>',
    password: '$2a$10$hashedpassword',
    first_name: 'Test',
    last_name: 'User',
    roles: [{ name: 'admin' }],
    two_factor_enabled: true,
    status: 'active',
};
exports.mockJwtPayload = {
    sub: '1',
    email: '<EMAIL>',
    roles: ['admin'],
};
//# sourceMappingURL=user.mock.js.map