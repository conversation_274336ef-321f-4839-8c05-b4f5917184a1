import { Repository } from 'typeorm';
import { Applicants } from '../entities/applicant.entity';
import { CreateApplicantDto } from '../dto/applicant/create-applicant.dto';
import { UpdateApplicantDto } from '../dto/applicant/update-applicant.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { PolymorphicService } from '../common/services/polymorphic.service';
export declare class ApplicantsService {
    private applicantsRepository;
    private polymorphicService;
    constructor(applicantsRepository: Repository<Applicants>, polymorphicService: PolymorphicService);
    private readonly paginateConfig;
    create(createApplicantDto: CreateApplicantDto, createdBy: string): Promise<Applicants>;
    findAll(query: PaginateQuery): Promise<Paginated<Applicants>>;
    findOne(id: string): Promise<Applicants>;
    findByBusinessRegistrationNumber(businessRegistrationNumber: string): Promise<Applicants | null>;
    findByTpin(tpin: string): Promise<Applicants | null>;
    update(id: string, updateApplicantDto: UpdateApplicantDto, updatedBy: string): Promise<Applicants>;
    remove(id: string): Promise<void>;
    search(searchTerm: string): Promise<Applicants[]>;
    findOneWithRelatedData(id: string): Promise<{
        applicant: Applicants;
        addresses: any[];
        contacts: any[];
        primaryAddress: any;
        primaryContact: any;
    }>;
    createAddressForApplicant(applicantId: string, addressData: any, createdBy: string): Promise<any>;
    createContactPersonForApplicant(applicantId: string, contactData: any, createdBy: string): Promise<any>;
}
