"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationStatusTrackingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const applications_entity_1 = require("../entities/applications.entity");
const application_status_history_entity_1 = require("../entities/application-status-history.entity");
const user_entity_1 = require("../entities/user.entity");
const application_task_helper_service_1 = require("../applications/application-task-helper.service");
let ApplicationStatusTrackingService = class ApplicationStatusTrackingService {
    applicationsRepository;
    statusHistoryRepository;
    userRepository;
    applicationTaskHelper;
    constructor(applicationsRepository, statusHistoryRepository, userRepository, applicationTaskHelper) {
        this.applicationsRepository = applicationsRepository;
        this.statusHistoryRepository = statusHistoryRepository;
        this.userRepository = userRepository;
        this.applicationTaskHelper = applicationTaskHelper;
    }
    async updateApplicationStatus(applicationId, updateStatusDto, userId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId },
            relations: ['applicant', 'license_category']
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        this.validateStatusTransition(application.status, updateStatusDto.status);
        const previousStatus = application.status;
        const { newStep, newProgress } = this.calculateStepAndProgress(updateStatusDto.status);
        application.status = updateStatusDto.status;
        application.current_step = newStep;
        application.progress_percentage = newProgress;
        application.updated_by = userId;
        if (updateStatusDto.status === 'submitted' && !application.submitted_at) {
            application.submitted_at = new Date();
        }
        await this.applicationsRepository.save(application);
        await this.applicationTaskHelper.handleApplicationSubmission(applicationId, previousStatus, updateStatusDto.status, userId);
        const statusHistory = new application_status_history_entity_1.ApplicationStatusHistory();
        statusHistory.application_id = applicationId;
        statusHistory.status = updateStatusDto.status;
        statusHistory.previous_status = previousStatus;
        statusHistory.comments = updateStatusDto.comments;
        statusHistory.reason = updateStatusDto.reason;
        statusHistory.changed_by = updateStatusDto.changed_by || userId;
        if (updateStatusDto.estimated_completion_date) {
            statusHistory.estimated_completion_date = new Date(updateStatusDto.estimated_completion_date);
        }
        await this.statusHistoryRepository.save(statusHistory);
        return this.getApplicationStatusTracking(applicationId);
    }
    async getApplicationStatusTracking(applicationId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId },
            relations: ['applicant', 'license_category']
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        const statusHistory = await this.statusHistoryRepository.find({
            where: { application_id: applicationId },
            relations: ['user'],
            order: { changed_at: 'DESC' }
        });
        const transformedHistory = statusHistory.map(history => ({
            history_id: history.history_id,
            application_id: history.application_id,
            status: history.status,
            previous_status: history.previous_status,
            comments: history.comments,
            reason: history.reason,
            changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
            changed_at: history.changed_at,
            estimated_completion_date: history.estimated_completion_date
        }));
        return {
            application_id: application.application_id,
            application_number: application.application_number,
            current_status: application.status,
            current_step: application.current_step,
            progress_percentage: application.progress_percentage,
            submitted_at: application.submitted_at,
            created_at: application.created_at,
            updated_at: application.updated_at,
            status_history: transformedHistory,
            applicant: {
                name: application.applicant.name,
                email: application.applicant.email,
                business_registration_number: application.applicant.business_registration_number
            },
            license_category: {
                name: application.license_category.name,
                description: application.license_category.description
            }
        };
    }
    async getApplicationsByStatus(status) {
        const applications = await this.applicationsRepository.find({
            where: { status },
            relations: ['applicant', 'license_category', 'license_category.license_type'],
            order: { updated_at: 'DESC' }
        });
        if (applications.length === 0) {
            return [];
        }
        const applicationIds = applications.map(app => app.application_id);
        const statusHistories = await this.statusHistoryRepository.find({
            where: { application_id: (0, typeorm_2.In)(applicationIds) },
            relations: ['user'],
            order: { changed_at: 'DESC' }
        });
        const historiesByAppId = statusHistories.reduce((acc, history) => {
            if (!acc[history.application_id]) {
                acc[history.application_id] = [];
            }
            acc[history.application_id].push(history);
            return acc;
        }, {});
        return applications.map(application => {
            const appHistories = historiesByAppId[application.application_id] || [];
            const transformedHistory = appHistories.map(history => ({
                history_id: history.history_id,
                application_id: history.application_id,
                status: history.status,
                previous_status: history.previous_status,
                comments: history.comments,
                reason: history.reason,
                changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
                changed_at: history.changed_at,
                estimated_completion_date: history.estimated_completion_date
            }));
            return {
                application_id: application.application_id,
                application_number: application.application_number,
                current_status: application.status,
                current_step: application.current_step,
                progress_percentage: application.progress_percentage,
                submitted_at: application.submitted_at,
                created_at: application.created_at,
                updated_at: application.updated_at,
                status_history: transformedHistory,
                applicant: {
                    name: application.applicant.name,
                    email: application.applicant.email,
                    business_registration_number: application.applicant.business_registration_number
                },
                license_category: {
                    name: application.license_category.name,
                    description: application.license_category.description
                }
            };
        });
    }
    validateStatusTransition(currentStatus, newStatus) {
        const validTransitions = {
            'draft': ['submitted'],
            'submitted': ['under_review'],
            'under_review': ['evaluation', 'rejected'],
            'evaluation': ['approved', 'rejected'],
            'approved': [],
            'rejected': [],
            'withdrawn': []
        };
        if (!validTransitions[currentStatus] || !validTransitions[currentStatus].includes(newStatus)) {
            throw new common_1.BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
        }
    }
    calculateStepAndProgress(status) {
        const statusStepMap = {
            'draft': { step: 1, progress: 14 },
            'submitted': { step: 2, progress: 25 },
            'under_review': { step: 3, progress: 50 },
            'evaluation': { step: 4, progress: 75 },
            'approved': { step: 5, progress: 100 },
            'rejected': { step: 1, progress: 0 },
            'withdrawn': { step: 1, progress: 0 }
        };
        const mapping = statusStepMap[status];
        if (!mapping) {
            throw new common_1.BadRequestException(`Unknown status: ${status}`);
        }
        return {
            newStep: mapping.step,
            newProgress: mapping.progress
        };
    }
    async getStatusHistory(applicationId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId }
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        const statusHistory = await this.statusHistoryRepository.find({
            where: { application_id: applicationId },
            relations: ['user'],
            order: { changed_at: 'DESC' }
        });
        return statusHistory.map(history => ({
            history_id: history.history_id,
            application_id: history.application_id,
            status: history.status,
            previous_status: history.previous_status,
            comments: history.comments,
            reason: history.reason,
            changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
            changed_at: history.changed_at,
            estimated_completion_date: history.estimated_completion_date
        }));
    }
};
exports.ApplicationStatusTrackingService = ApplicationStatusTrackingService;
exports.ApplicationStatusTrackingService = ApplicationStatusTrackingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(applications_entity_1.Applications)),
    __param(1, (0, typeorm_1.InjectRepository)(application_status_history_entity_1.ApplicationStatusHistory)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        application_task_helper_service_1.ApplicationTaskHelperService])
], ApplicationStatusTrackingService);
//# sourceMappingURL=application-status-tracking.service.js.map