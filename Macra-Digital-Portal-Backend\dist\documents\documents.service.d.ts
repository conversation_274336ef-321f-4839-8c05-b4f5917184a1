import { Repository } from 'typeorm';
import { Documents } from '../entities/documents.entity';
import { CreateDocumentDto } from '../dto/document/create-document.dto';
import { UpdateDocumentDto } from '../dto/document/update-document.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
export declare class DocumentsService {
    private documentsRepository;
    constructor(documentsRepository: Repository<Documents>);
    private readonly paginateConfig;
    create(createDocumentDto: CreateDocumentDto, createdBy: string): Promise<Documents>;
    findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Documents>>;
    findOne(id: string): Promise<Documents>;
    findByApplication(applicationId: string): Promise<Documents[]>;
    findByEntity(entityType: string, entityId: string): Promise<Documents[]>;
    findByDocumentType(documentType: string): Promise<Documents[]>;
    findRequiredDocuments(): Promise<Documents[]>;
    update(id: string, updateDocumentDto: UpdateDocumentDto, updatedBy: string): Promise<Documents>;
    remove(id: string): Promise<void>;
    getDocumentStats(): Promise<any>;
    getDocumentsByMimeType(mimeType: string): Promise<Documents[]>;
    getTotalFileSize(): Promise<number>;
    getFileStream(filePath: string): Promise<import("fs").ReadStream>;
}
