import { Repository } from 'typeorm';
import { Department } from '../entities/department.entity';
import { UpdateDepartmentDto } from 'src/dto/department/update-department.dto';
import { CreateDepartmentDto } from 'src/dto/department/create-department.dto';
export declare class DepartmentService {
    private readonly departmentRepository;
    constructor(departmentRepository: Repository<Department>);
    create(createDto: CreateDepartmentDto): Promise<Department>;
    findAll(): Promise<Department[]>;
    findOne(id: string): Promise<Department>;
    update(id: string, updateDto: UpdateDepartmentDto): Promise<Department>;
    remove(id: string): Promise<void>;
}
