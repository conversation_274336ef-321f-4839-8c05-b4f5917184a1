import { NotificationType, NotificationStatus, NotificationPriority, RecipientType } from '../../entities/notifications.entity';
export declare class CreateNotificationDto {
    type: NotificationType;
    status?: NotificationStatus;
    priority?: NotificationPriority;
    recipient_type: RecipientType;
    recipient_id: string;
    recipient_email?: string;
    recipient_phone?: string;
    subject: string;
    message: string;
    html_content?: string;
    entity_type?: string;
    entity_id?: string;
    metadata?: any;
    action_url?: string;
    expires_at?: Date;
}
