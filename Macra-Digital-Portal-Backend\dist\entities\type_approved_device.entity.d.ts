import { User } from "./user.entity";
import { Applications } from "./applications.entity";
import { TypeApprovedManufacturer } from "./type_approved_manufacturer.entity";
export declare class TypeApprovedDevice {
    device_id: string;
    application_id?: string;
    manufacturer_id: string;
    device_type: string;
    model_name: string;
    device_serial_number: string;
    device_manufacturer: string;
    device_approval_number: string;
    device_approval_date: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    application?: Applications;
    manufacturer: TypeApprovedManufacturer;
    creator: User;
    updater?: User;
    generateId(): void;
}
