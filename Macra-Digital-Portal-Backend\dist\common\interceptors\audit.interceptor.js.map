{"version": 3, "file": "audit.interceptor.js", "sourceRoot": "", "sources": ["../../../src/common/interceptors/audit.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAQwB;AACxB,4BAA0B;AAE1B,8CAAiD;AACjD,+EAA0E;AAC1E,0EAA0F;AAC1F,uCAAyC;AAUlC,MAAM,+BAA+B,GAAG,CAAC,IAAW,EAAe,EAAE;IAC1E,QAAQ,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;QAC3B,KAAK,QAAQ;YACX,OAAO,gCAAW,CAAC,eAAe,CAAC;QACrC,KAAK,eAAe;YAClB,OAAO,gCAAW,CAAC,sBAAsB,CAAC;QAC5C,KAAK,YAAY;YACf,OAAO,gCAAW,CAAC,mBAAmB,CAAC;QACzC,KAAK,oBAAoB;YACvB,OAAO,gCAAW,CAAC,0BAA0B,CAAC;QAChD,KAAK,cAAc;YACjB,OAAO,gCAAW,CAAC,qBAAqB,CAAC;QAC3C;YACE,OAAO,gCAAW,CAAC,kBAAkB,CAAC;IAC1C,CAAC;AACH,CAAC,CAAA;AAfY,QAAA,+BAA+B,mCAe3C;AAEY,QAAA,kBAAkB,GAAG,gBAAgB,CAAC;AAG5C,MAAM,KAAK,GAAG,CAAC,QAAuB,EAAE,EAAE;IAC/C,OAAO,CAAC,MAAW,EAAE,WAAmB,EAAE,UAA8B,EAAE,EAAE;QAC1E,OAAO,CAAC,cAAc,CAAC,0BAAkB,EAAE,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;QACvE,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AALW,QAAA,KAAK,SAKhB;AAGK,IAAM,gBAAgB,wBAAtB,MAAM,gBAAgB;IAIR;IACA;IAJF,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAgB,CAAC,IAAI,CAAC,CAAC;IAE5D,YACmB,iBAAoC,EACpC,SAAoB;QADpB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,cAAS,GAAT,SAAS,CAAW;IACpC,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,aAAa,GAAG,OAAO,CAAC,WAAW,CACvC,0BAAkB,EAClB,OAAO,CAAC,UAAU,EAAE,CACrB,CAAC;QAEF,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAEvE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAG7B,IAAI,gBAAgB,GAAiB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,aAAa,CAAC,MAAM,KAAK,gCAAW,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC;YACtE,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;YAErB,YAAY,CAAC,KAAK,IAAI,EAAE;gBACtB,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC;oBACzC,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAEjF,IAAI,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;oBAElC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC;wBAC9C,MAAM,GAAG,IAAA,uCAA+B,EAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAC1E,CAAC;oBACD,MAAM,IAAI,CAAC,QAAQ,CAAC;wBAClB,GAAG,aAAa;wBAChB,MAAM;wBACN,MAAM,EAAE,gCAAW,CAAC,OAAO;wBAC3B,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO;wBAC7B,SAAS,EAAE,OAAO,CAAC,EAAE;wBACrB,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;wBACxC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC;wBAC/D,SAAS;wBACT,SAAS;wBACT,QAAQ,EAAE;4BACR,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;4BACpC,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,GAAG,EAAE,OAAO,CAAC,GAAG;4BAChB,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,KAAK,EAAE,OAAO,CAAC,KAAK;yBACrB;wBACD,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC;qBACtD,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,UAAU,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,EACF,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;YAEnB,YAAY,CAAC,KAAK,IAAI,EAAE;gBACtB,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC;oBAEzC,IAAI,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;oBAElC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC;wBAC9C,MAAM,GAAG,IAAA,uCAA+B,EAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAC1E,CAAC;oBACD,MAAM,IAAI,CAAC,QAAQ,CAAC;wBAClB,GAAG,aAAa;wBAChB,MAAM;wBACN,MAAM,EAAE,gCAAW,CAAC,OAAO;wBAC3B,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,IAAI,SAAS;wBAC1C,SAAS,EAAE,OAAO,CAAC,EAAE;wBACrB,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,SAAS;wBACrD,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,SAAS;wBAC5E,SAAS;wBACT,YAAY,EAAE,KAAK,CAAC,OAAO;wBAC3B,QAAQ,EAAE;4BACR,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;4BACpC,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,GAAG,EAAE,OAAO,CAAC,GAAG;4BAChB,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,KAAK,EAAE,OAAO,CAAC,KAAK;4BACpB,UAAU,EAAE,KAAK,CAAC,KAAK;yBACxB;qBACF,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,UAAU,CAAC,CAAC;gBAE7E,CAAC;YACH,CAAC,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAS;QAC9B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBAClC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QAEzD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,YAAoB,EAAE,UAAkB;QACrE,IAAI,CAAC;YACH,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;gBAE5B,IAAI,CAAC;oBACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;oBAClE,IAAI,YAAY,IAAI,OAAO,YAAY,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;wBAChE,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;wBACxD,IAAI,OAAO,EAAE,CAAC;4BAEZ,MAAM,EACJ,QAAQ,EACR,eAAe,EACf,eAAe,EACf,4BAA4B,EAC5B,GAAG,aAAa,EACjB,GAAG,OAAO,CAAC;4BACZ,OAAO,aAAa,CAAC;wBACvB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,YAAY,EAAE,CAAC;oBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;gBAC/F,CAAC;YACH,CAAC;YAMD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,MAAmB,EAAE,OAAY,EAAE,QAAa;QACvE,IAAI,CAAC;YACH,IAAI,MAAM,KAAK,gCAAW,CAAC,MAAM,EAAE,CAAC;gBAElC,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBAC7C,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;iBAAM,IAAI,MAAM,KAAK,gCAAW,CAAC,MAAM,EAAE,CAAC;gBAEzC,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACrD,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,MAAW;QAChC,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC1C,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,eAAe,GAAG;YACtB,UAAU;YACV,iBAAiB;YACjB,iBAAiB;YACjB,8BAA8B;YAC9B,QAAQ;YACR,OAAO;YACP,eAAe;YACf,cAAc;SACf,CAAC;QAEF,MAAM,SAAS,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAGhC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9B,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;gBACvB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACnC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC3F,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YACvD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,WAAW,CAAC,OAAY;QAC9B,OAAO,CACL,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAClC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;YAC5B,OAAO,CAAC,UAAU,EAAE,aAAa;YACjC,OAAO,CAAC,MAAM,EAAE,aAAa;YAC7B,OAAO,CAAC,EAAE;YACV,SAAS,CACV,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,OAAY,EAAE,QAAa;QAEnD,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC;YACvB,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC3B,CAAC;QAGD,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC7C,OAAO,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,EAAE,CAAC;QACvF,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AAzPY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAK2B,uCAAiB;QACzB,gBAAS;GAL5B,gBAAgB,CAyP5B"}