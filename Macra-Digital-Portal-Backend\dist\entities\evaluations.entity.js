"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Evaluations = exports.EvaluationRecommendation = exports.EvaluationStatus = exports.EvaluationType = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const applications_entity_1 = require("./applications.entity");
var EvaluationType;
(function (EvaluationType) {
    EvaluationType["INDIVIDUAL_LICENSE_A"] = "individual_license_a";
    EvaluationType["CLASS_LICENSE_B"] = "class_license_b";
    EvaluationType["NETWORK_SERVICE"] = "network_service";
    EvaluationType["POSTAL_SERVICE"] = "postal_service";
    EvaluationType["RADIO_COMMUNICATION"] = "radio_communication";
    EvaluationType["SATELLITE_SERVICE"] = "satellite_service";
    EvaluationType["TV_BROADCASTING"] = "tv_broadcasting";
    EvaluationType["UNIVERSITY_RADIO"] = "university_radio";
})(EvaluationType || (exports.EvaluationType = EvaluationType = {}));
var EvaluationStatus;
(function (EvaluationStatus) {
    EvaluationStatus["DRAFT"] = "draft";
    EvaluationStatus["COMPLETED"] = "completed";
    EvaluationStatus["APPROVED"] = "approved";
    EvaluationStatus["REJECTED"] = "rejected";
})(EvaluationStatus || (exports.EvaluationStatus = EvaluationStatus = {}));
var EvaluationRecommendation;
(function (EvaluationRecommendation) {
    EvaluationRecommendation["APPROVE"] = "approve";
    EvaluationRecommendation["CONDITIONAL_APPROVE"] = "conditional_approve";
    EvaluationRecommendation["REJECT"] = "reject";
})(EvaluationRecommendation || (exports.EvaluationRecommendation = EvaluationRecommendation = {}));
let Evaluations = class Evaluations {
    evaluation_id;
    application_id;
    evaluator_id;
    evaluation_type;
    status;
    total_score;
    recommendation;
    evaluators_notes;
    shareholding_compliance;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    completed_at;
    application;
    evaluator;
    creator;
    updater;
    generateId() {
        if (!this.evaluation_id) {
            this.evaluation_id = (0, uuid_1.v4)();
        }
    }
};
exports.Evaluations = Evaluations;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], Evaluations.prototype, "evaluation_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Evaluations.prototype, "application_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Evaluations.prototype, "evaluator_id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EvaluationType,
    }),
    __metadata("design:type", String)
], Evaluations.prototype, "evaluation_type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EvaluationStatus,
        default: EvaluationStatus.DRAFT,
    }),
    __metadata("design:type", String)
], Evaluations.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2 }),
    __metadata("design:type", Number)
], Evaluations.prototype, "total_score", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EvaluationRecommendation,
    }),
    __metadata("design:type", String)
], Evaluations.prototype, "recommendation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Evaluations.prototype, "evaluators_notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', nullable: true }),
    __metadata("design:type", Boolean)
], Evaluations.prototype, "shareholding_compliance", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Evaluations.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Evaluations.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Evaluations.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Evaluations.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], Evaluations.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Evaluations.prototype, "completed_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applications_entity_1.Applications),
    (0, typeorm_1.JoinColumn)({ name: 'application_id' }),
    __metadata("design:type", applications_entity_1.Applications)
], Evaluations.prototype, "application", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'evaluator_id' }),
    __metadata("design:type", user_entity_1.User)
], Evaluations.prototype, "evaluator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], Evaluations.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], Evaluations.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Evaluations.prototype, "generateId", null);
exports.Evaluations = Evaluations = __decorate([
    (0, typeorm_1.Entity)('evaluations')
], Evaluations);
//# sourceMappingURL=evaluations.entity.js.map