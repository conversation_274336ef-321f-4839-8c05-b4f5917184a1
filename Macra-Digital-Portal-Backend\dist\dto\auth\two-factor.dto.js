"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestTwoFactorDto = exports.TwoFactorDto = void 0;
const class_validator_1 = require("class-validator");
class TwoFactorDto {
    user_id;
    code;
    unique;
}
exports.TwoFactorDto = TwoFactorDto;
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TwoFactorDto.prototype, "user_id", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Matches)(/^\d{6}$/, {
        message: 'Two-factor code must be 6 digits',
    }),
    __metadata("design:type", String)
], TwoFactorDto.prototype, "code", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TwoFactorDto.prototype, "unique", void 0);
class RequestTwoFactorDto {
    user_id;
    access_token;
}
exports.RequestTwoFactorDto = RequestTwoFactorDto;
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], RequestTwoFactorDto.prototype, "user_id", void 0);
__decorate([
    (0, class_validator_1.IsJWT)(),
    __metadata("design:type", String)
], RequestTwoFactorDto.prototype, "access_token", void 0);
//# sourceMappingURL=two-factor.dto.js.map