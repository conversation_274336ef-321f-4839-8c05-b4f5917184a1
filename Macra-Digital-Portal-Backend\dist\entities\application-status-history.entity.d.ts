import { Applications } from './applications.entity';
import { User } from './user.entity';
export declare class ApplicationStatusHistory {
    history_id: string;
    application_id: string;
    status: string;
    previous_status?: string;
    comments?: string;
    reason?: string;
    changed_by: string;
    changed_at: Date;
    estimated_completion_date?: Date;
    application: Applications;
    user: User;
    generateId(): void;
}
