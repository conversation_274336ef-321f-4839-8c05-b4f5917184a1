"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StandardsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const type_approved_manufacturer_entity_1 = require("../entities/type_approved_manufacturer.entity");
const typeorm_2 = require("@nestjs/typeorm");
const type_approved_device_entity_1 = require("../entities/type_approved_device.entity");
let StandardsService = class StandardsService {
    manufacturerRepo;
    deviceRepo;
    constructor(manufacturerRepo, deviceRepo) {
        this.manufacturerRepo = manufacturerRepo;
        this.deviceRepo = deviceRepo;
    }
    async createManufacturer(dto) {
        const entity = this.manufacturerRepo.create(dto);
        return this.manufacturerRepo.save(entity);
    }
    async findAllManufacturers() {
        return this.manufacturerRepo.find({ order: { created_at: 'DESC' } });
    }
    async findOneManufacturer(id) {
        const entity = await this.manufacturerRepo.findOne({ where: { manufacturer_id: id } });
        if (!entity)
            throw new common_1.NotFoundException(`Manufacturer with id ${id} not found`);
        return entity;
    }
    async updateManufacturer(id, dto) {
        const entity = await this.findOneManufacturer(id);
        const updated = this.manufacturerRepo.merge(entity, dto);
        return this.manufacturerRepo.save(updated);
    }
    async removeManufacturer(id) {
        const entity = await this.findOneManufacturer(id);
        await this.manufacturerRepo.softRemove(entity);
    }
};
exports.StandardsService = StandardsService;
exports.StandardsService = StandardsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectRepository)(type_approved_manufacturer_entity_1.TypeApprovedManufacturer)),
    __param(1, (0, typeorm_2.InjectRepository)(type_approved_device_entity_1.TypeApprovedDevice)),
    __metadata("design:paramtypes", [typeorm_1.Repository,
        typeorm_1.Repository])
], StandardsService);
//# sourceMappingURL=standards.service.js.map