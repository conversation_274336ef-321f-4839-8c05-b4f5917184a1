{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/evaluationService.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\nexport interface EvaluationCriteria {\r\n  criteria_id?: string;\r\n  category: string;\r\n  subcategory: string;\r\n  score: number;\r\n  weight: number;\r\n  max_marks?: number;\r\n  awarded_marks?: number;\r\n}\r\n\r\nexport interface Evaluation {\r\n  evaluation_id: string;\r\n  application_id: string;\r\n  evaluator_id: string;\r\n  evaluation_type: string;\r\n  status: 'draft' | 'completed' | 'approved' | 'rejected';\r\n  total_score: number;\r\n  recommendation: 'approve' | 'conditional_approve' | 'reject';\r\n  evaluators_notes?: string;\r\n  shareholding_compliance?: boolean;\r\n  completed_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  application?: any;\r\n  evaluator?: any;\r\n  criteria?: EvaluationCriteria[];\r\n}\r\n\r\nexport interface CreateEvaluationData {\r\n  application_id: string;\r\n  evaluator_id: string;\r\n  evaluation_type: string;\r\n  total_score: number;\r\n  recommendation: 'approve' | 'conditional_approve' | 'reject';\r\n  evaluators_notes?: string;\r\n  shareholding_compliance?: boolean;\r\n  criteria?: Omit<EvaluationCriteria, 'criteria_id'>[];\r\n}\r\n\r\nexport interface UpdateEvaluationData extends Partial<CreateEvaluationData> {\r\n  status?: 'draft' | 'completed' | 'approved' | 'rejected';\r\n}\r\n\r\nexport interface EvaluationStats {\r\n  total: number;\r\n  draft: number;\r\n  completed: number;\r\n  approved: number;\r\n  rejected: number;\r\n  averageScore: number;\r\n}\r\n\r\nexport const evaluationService = {\r\n  // Get all evaluations with pagination\r\n  async getEvaluations(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }) {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n\r\n    const response = await apiClient.get(`/evaluations?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get evaluation by ID\r\n  async getEvaluation(id: string): Promise<Evaluation> {\r\n    const response = await apiClient.get(`/evaluations/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get evaluation by application ID\r\n  async getEvaluationByApplication(applicationId: string): Promise<Evaluation | null> {\r\n    try {\r\n      const response = await apiClient.get(`/evaluations/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get evaluation criteria\r\n  async getEvaluationCriteria(evaluationId: string): Promise<EvaluationCriteria[]> {\r\n    const response = await apiClient.get(`/evaluations/${evaluationId}/criteria`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new evaluation\r\n  async createEvaluation(data: CreateEvaluationData): Promise<Evaluation> {\r\n    const response = await apiClient.post('/evaluations', data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update evaluation\r\n  async updateEvaluation(id: string, data: UpdateEvaluationData): Promise<Evaluation> {\r\n    const response = await apiClient.patch(`/evaluations/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete evaluation\r\n  async deleteEvaluation(id: string): Promise<void> {\r\n    await apiClient.delete(`/evaluations/${id}`);\r\n  },\r\n\r\n  // Get evaluation statistics\r\n  async getEvaluationStats(): Promise<EvaluationStats> {\r\n    const response = await apiClient.get('/evaluations/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Submit evaluation (mark as completed)\r\n  async submitEvaluation(id: string, data: {\r\n    total_score: number;\r\n    recommendation: 'approve' | 'conditional_approve' | 'reject';\r\n    evaluators_notes?: string;\r\n    criteria?: Omit<EvaluationCriteria, 'criteria_id'>[];\r\n  }): Promise<Evaluation> {\r\n    return this.updateEvaluation(id, {\r\n      ...data,\r\n      status: 'completed',\r\n    });\r\n  },\r\n\r\n  // Calculate total score from criteria\r\n  calculateTotalScore(criteria: EvaluationCriteria[]): number {\r\n    if (!criteria || criteria.length === 0) return 0;\r\n\r\n    const weightedSum = criteria.reduce((sum, criterion) => {\r\n      return sum + (criterion.score * criterion.weight);\r\n    }, 0);\r\n\r\n    const totalWeight = criteria.reduce((sum, criterion) => sum + criterion.weight, 0);\r\n\r\n    return totalWeight > 0 ? (weightedSum / totalWeight) : 0;\r\n  },\r\n\r\n  // Update application status with evaluation comments and email applicant\r\n  async updateApplicationStatusWithEmail(applicationId: string, data: {\r\n    status: string;\r\n    comment: string;\r\n    attachments?: File[];\r\n    step?: string;\r\n  }): Promise<any> {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('status', data.status);\r\n      formData.append('comment', data.comment);\r\n      formData.append('send_email', 'true');\r\n\r\n      if (data.step) {\r\n        formData.append('step', data.step);\r\n      }\r\n\r\n      // Add attachments if provided\r\n      if (data.attachments && data.attachments.length > 0) {\r\n        data.attachments.forEach((file, index) => {\r\n          formData.append(`attachments`, file);\r\n        });\r\n      }\r\n\r\n      const response = await apiClient.patch(\r\n        `/application-status/${applicationId}/status`,\r\n        formData,\r\n        {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data',\r\n          },\r\n        }\r\n      );\r\n\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating application status with email:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Upload evaluation attachment\r\n  async uploadEvaluationAttachment(applicationId: string, step: string, file: File): Promise<any> {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('step', step);\r\n      formData.append('type', 'evaluation');\r\n\r\n      const response = await apiClient.post(\r\n        `/applications/${applicationId}/attachments`,\r\n        formData,\r\n        {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data',\r\n          },\r\n        }\r\n      );\r\n\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error uploading evaluation attachment:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save evaluation comment\r\n  async saveEvaluationComment(applicationId: string, step: string, comment: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.post(`/applications/${applicationId}/evaluation-comments`, {\r\n        step,\r\n        comment,\r\n      });\r\n\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving evaluation comment:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get evaluation template based on license type\r\n  getEvaluationTemplate(licenseType: string): EvaluationCriteria[] {\r\n    const templates: Record<string, EvaluationCriteria[]> = {\r\n      postal_service: [\r\n        { category: 'financial_capacity', subcategory: 'financial_documents', score: 0, weight: 0.15, max_marks: 15 },\r\n        { category: 'financial_capacity', subcategory: 'capital_adequacy', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'financial_capacity', subcategory: 'financial_projections', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'financial_capacity', subcategory: 'credit_worthiness', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'business_plan', subcategory: 'market_analysis', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'business_plan', subcategory: 'business_model', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'business_plan', subcategory: 'revenue_projections', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'business_plan', subcategory: 'growth_strategy', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'technical_expertise', subcategory: 'technical_capacity', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'technical_expertise', subcategory: 'operational_plan', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'technical_expertise', subcategory: 'implementation_timeline', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'organizational_structure', subcategory: 'management_structure', score: 0, weight: 0.05, max_marks: 5 },\r\n      ],\r\n      telecommunications: [\r\n        { category: 'financial_capacity', subcategory: 'financial_documents', score: 0, weight: 0.20, max_marks: 20 },\r\n        { category: 'financial_capacity', subcategory: 'capital_adequacy', score: 0, weight: 0.15, max_marks: 15 },\r\n        { category: 'technical_expertise', subcategory: 'network_design', score: 0, weight: 0.20, max_marks: 20 },\r\n        { category: 'technical_expertise', subcategory: 'technical_capacity', score: 0, weight: 0.15, max_marks: 15 },\r\n        { category: 'business_plan', subcategory: 'market_analysis', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'business_plan', subcategory: 'business_model', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'organizational_structure', subcategory: 'management_structure', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'organizational_structure', subcategory: 'compliance_framework', score: 0, weight: 0.05, max_marks: 5 },\r\n      ],\r\n    };\r\n\r\n    return templates[licenseType] || templates.postal_service;\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAyDO,MAAM,oBAAoB;IAC/B,sCAAsC;IACtC,MAAM,gBAAe,MAMpB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,QAAQ,IAAI;QAC7E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,eAAc,EAAU;QAC5B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,4BAA2B,aAAqB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,eAAe;YAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,YAAoB;QAC9C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,aAAa,SAAS,CAAC;QAC5E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,kBAAiB,IAA0B;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB;QACtD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,kBAAiB,EAAU,EAAE,IAA0B;QAC3D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,kBAAiB,EAAU;QAC/B,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;IAC7C;IAEA,4BAA4B;IAC5B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wCAAwC;IACxC,MAAM,kBAAiB,EAAU,EAAE,IAKlC;QACC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC/B,GAAG,IAAI;YACP,QAAQ;QACV;IACF;IAEA,sCAAsC;IACtC,qBAAoB,QAA8B;QAChD,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;QAE/C,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK;YACxC,OAAO,MAAO,UAAU,KAAK,GAAG,UAAU,MAAM;QAClD,GAAG;QAEH,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,YAAc,MAAM,UAAU,MAAM,EAAE;QAEhF,OAAO,cAAc,IAAK,cAAc,cAAe;IACzD;IAEA,yEAAyE;IACzE,MAAM,kCAAiC,aAAqB,EAAE,IAK7D;QACC,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,UAAU,KAAK,MAAM;YACrC,SAAS,MAAM,CAAC,WAAW,KAAK,OAAO;YACvC,SAAS,MAAM,CAAC,cAAc;YAE9B,IAAI,KAAK,IAAI,EAAE;gBACb,SAAS,MAAM,CAAC,QAAQ,KAAK,IAAI;YACnC;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;oBAC9B,SAAS,MAAM,CAAC,CAAC,WAAW,CAAC,EAAE;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CACpC,CAAC,oBAAoB,EAAE,cAAc,OAAO,CAAC,EAC7C,UACA;gBACE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAGF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,4BAA2B,aAAqB,EAAE,IAAY,EAAE,IAAU;QAC9E,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CACnC,CAAC,cAAc,EAAE,cAAc,YAAY,CAAC,EAC5C,UACA;gBACE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAGF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,aAAqB,EAAE,IAAY,EAAE,OAAe;QAC9E,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,cAAc,oBAAoB,CAAC,EAAE;gBAC1F;gBACA;YACF;YAEA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA,gDAAgD;IAChD,uBAAsB,WAAmB;QACvC,MAAM,YAAkD;YACtD,gBAAgB;gBACd;oBAAE,UAAU;oBAAsB,aAAa;oBAAuB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC5G;oBAAE,UAAU;oBAAsB,aAAa;oBAAoB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACzG;oBAAE,UAAU;oBAAsB,aAAa;oBAAyB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC9G;oBAAE,UAAU;oBAAsB,aAAa;oBAAqB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBACzG;oBAAE,UAAU;oBAAiB,aAAa;oBAAmB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACnG;oBAAE,UAAU;oBAAiB,aAAa;oBAAkB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAClG;oBAAE,UAAU;oBAAiB,aAAa;oBAAuB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBACtG;oBAAE,UAAU;oBAAiB,aAAa;oBAAmB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBAClG;oBAAE,UAAU;oBAAuB,aAAa;oBAAsB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC5G;oBAAE,UAAU;oBAAuB,aAAa;oBAAoB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC1G;oBAAE,UAAU;oBAAuB,aAAa;oBAA2B,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBAChH;oBAAE,UAAU;oBAA4B,aAAa;oBAAwB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;aACnH;YACD,oBAAoB;gBAClB;oBAAE,UAAU;oBAAsB,aAAa;oBAAuB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC5G;oBAAE,UAAU;oBAAsB,aAAa;oBAAoB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACzG;oBAAE,UAAU;oBAAuB,aAAa;oBAAkB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACxG;oBAAE,UAAU;oBAAuB,aAAa;oBAAsB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC5G;oBAAE,UAAU;oBAAiB,aAAa;oBAAmB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACnG;oBAAE,UAAU;oBAAiB,aAAa;oBAAkB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAClG;oBAAE,UAAU;oBAA4B,aAAa;oBAAwB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBAClH;oBAAE,UAAU;oBAA4B,aAAa;oBAAwB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;aACnH;QACH;QAEA,OAAO,SAAS,CAAC,YAAY,IAAI,UAAU,cAAc;IAC3D;AACF", "debugId": null}}]}