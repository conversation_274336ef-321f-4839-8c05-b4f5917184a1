import { Repository } from 'typeorm';
import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { Evaluations } from '../entities/evaluations.entity';
import { EvaluationCriteria } from '../entities/evaluation-criteria.entity';
import { Applications } from '../entities/applications.entity';
import { CreateEvaluationDto } from '../dto/evaluations/create-evaluation.dto';
import { UpdateEvaluationDto } from '../dto/evaluations/update-evaluation.dto';
export declare class EvaluationsService {
    private evaluationsRepository;
    private evaluationCriteriaRepository;
    private applicationsRepository;
    constructor(evaluationsRepository: Repository<Evaluations>, evaluationCriteriaRepository: Repository<EvaluationCriteria>, applicationsRepository: Repository<Applications>);
    private readonly paginateConfig;
    create(createEvaluationDto: CreateEvaluationDto, createdBy: string): Promise<Evaluations>;
    findAll(query: PaginateQuery): Promise<Paginated<Evaluations>>;
    findOne(id: string): Promise<Evaluations>;
    findByApplication(applicationId: string): Promise<Evaluations | null>;
    findCriteria(evaluationId: string): Promise<EvaluationCriteria[]>;
    update(id: string, updateEvaluationDto: UpdateEvaluationDto, updatedBy: string): Promise<Evaluations>;
    remove(id: string): Promise<void>;
    getEvaluationStats(): Promise<{
        total: number;
        draft: number;
        completed: number;
        approved: number;
        rejected: number;
        averageScore: number;
    }>;
}
