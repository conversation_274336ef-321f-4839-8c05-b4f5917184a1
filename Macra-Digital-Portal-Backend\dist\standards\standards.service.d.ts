import { Repository } from 'typeorm';
import { TypeApprovedManufacturer } from 'src/entities/type_approved_manufacturer.entity';
import { CreateTypeApprovedManufacturerDto } from 'src/dto/type_approval/create.dto';
import { UpdateTypeApprovedManufacturerDto } from 'src/dto/type_approval/update.dto';
import { TypeApprovedDevice } from 'src/entities/type_approved_device.entity';
export declare class StandardsService {
    private readonly manufacturerRepo;
    private readonly deviceRepo;
    constructor(manufacturerRepo: Repository<TypeApprovedManufacturer>, deviceRepo: Repository<TypeApprovedDevice>);
    createManufacturer(dto: CreateTypeApprovedManufacturerDto): Promise<TypeApprovedManufacturer>;
    findAllManufacturers(): Promise<TypeApprovedManufacturer[]>;
    findOneManufacturer(id: string): Promise<TypeApprovedManufacturer>;
    updateManufacturer(id: string, dto: UpdateTypeApprovedManufacturerDto): Promise<TypeApprovedManufacturer>;
    removeManufacturer(id: string): Promise<void>;
}
