import { User, UserStatus } from '../../entities/user.entity';
export type SafeUser = Omit<User, 'password' | 'two_factor_code' | 'two_factor_temp'> & {
    generateId: () => void;
};
export type UserWithRoles = User & {
    roles: NonNullable<User['roles']>;
};
export type UserProfile = Pick<User, 'user_id' | 'email' | 'first_name' | 'last_name' | 'middle_name' | 'phone' | 'profile_image' | 'status' | 'created_at' | 'updated_at'> & {
    roles?: string[];
};
export interface CreateUserData {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    middle_name?: string;
    phone: string;
    department_id?: string;
    organization_id?: string;
    status?: UserStatus;
    role_ids?: string[];
}
export interface UpdateUserData {
    email?: string;
    password?: string;
    first_name?: string;
    last_name?: string;
    middle_name?: string;
    phone?: string;
    department_id?: string;
    organization_id?: string;
    status?: UserStatus;
    role_ids?: string[];
}
export interface UpdateProfileData {
    email?: string;
    first_name?: string;
    last_name?: string;
    middle_name?: string;
    phone?: string;
}
export interface ChangePasswordData {
    current_password: string;
    new_password: string;
    confirm_password: string;
}
export interface UserQueryFilters {
    status?: UserStatus;
    department_id?: string;
    role?: string;
    search?: string;
}
export interface UserPaginationOptions {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    filters?: UserQueryFilters;
}
export interface PaginatedUsers {
    data: SafeUser[];
    meta: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
    };
}
export interface UserValidationRules {
    email: {
        required: boolean;
        format: RegExp;
        maxLength: number;
    };
    password: {
        required: boolean;
        minLength: number;
        maxLength: number;
        requireSpecialChar: boolean;
        requireNumber: boolean;
        requireUppercase: boolean;
    };
    name: {
        required: boolean;
        minLength: number;
        maxLength: number;
        allowedChars: RegExp;
    };
    phone: {
        required: boolean;
        format: RegExp;
    };
}
export interface UserValidationResult {
    isValid: boolean;
    errors: Record<string, string[]>;
    warnings?: Record<string, string[]>;
}
export interface UserRole {
    role_id: string;
    name: string;
    description?: string;
}
export interface RoleAssignment {
    user_id: string;
    role_ids: string[];
}
export interface AvatarUpload {
    file: Express.Multer.File;
    userId: string;
}
export interface AvatarResult {
    user: SafeUser;
    avatarUrl?: string;
}
export type UserCreationResult = Promise<User>;
export type UserUpdateResult = Promise<User>;
export type UserDeletionResult = Promise<void>;
export type UserQueryResult = Promise<PaginatedUsers>;
export type PasswordChangeResult = Promise<{
    message: string;
}>;
export type AvatarUploadResult = Promise<User>;
export declare function isValidUserStatus(status: string): status is UserStatus;
export declare function isValidUserId(userId: string): boolean;
export declare function isValidEmail(email: string): boolean;
export declare function isValidPhone(phone: string): boolean;
export declare function hasRequiredUserFields(data: any): data is CreateUserData;
export declare function isUserWithRoles(user: User): user is UserWithRoles;
export declare class UserTypeUtils {
    static createSafeUser(user: User): SafeUser;
    static createUserProfile(user: User): UserProfile;
    static extractRoleNames(user: User): string[];
    static getFullName(user: Pick<User, 'first_name' | 'last_name' | 'middle_name'>): string;
}
