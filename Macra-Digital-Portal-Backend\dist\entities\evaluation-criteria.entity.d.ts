import { User } from './user.entity';
import { Evaluations } from './evaluations.entity';
export declare class EvaluationCriteria {
    criteria_id: string;
    evaluation_id: string;
    category: string;
    subcategory: string;
    score: number;
    weight: number;
    max_marks?: number;
    awarded_marks?: number;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    evaluation: Evaluations;
    creator: User;
    updater?: User;
    generateId(): void;
}
