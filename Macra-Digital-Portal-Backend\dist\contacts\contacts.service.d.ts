import { Repository } from 'typeorm';
import { Contacts } from '../entities/contacts.entity';
import { CreateContactDto } from '../dto/contact/create-contact.dto';
import { UpdateContactDto } from '../dto/contact/update-contact.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
export declare class ContactsService {
    private contactsRepository;
    constructor(contactsRepository: Repository<Contacts>);
    private readonly paginateConfig;
    create(createContactDto: CreateContactDto, createdBy: string): Promise<Contacts>;
    findAll(query: PaginateQuery): Promise<Paginated<Contacts>>;
    findOne(id: string): Promise<Contacts>;
    findByTelephone(telephone: string): Promise<Contacts | null>;
    findByEmail(email: string): Promise<Contacts | null>;
    update(id: string, updateContactDto: UpdateContactDto, updatedBy: string): Promise<Contacts>;
    remove(id: string): Promise<void>;
    search(searchTerm: string): Promise<Contacts[]>;
    getContactsWithEmail(): Promise<Contacts[]>;
    getContactsWithoutEmail(): Promise<Contacts[]>;
}
