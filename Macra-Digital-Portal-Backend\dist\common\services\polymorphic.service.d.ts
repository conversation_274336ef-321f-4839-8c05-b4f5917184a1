import { DataSource } from 'typeorm';
import { Address } from '../../entities/address.entity';
import { ContactPersons } from '../../entities/contact-persons.entity';
export declare class PolymorphicService {
    private dataSource;
    constructor(dataSource: DataSource);
    getAddressesForEntity(entityType: string, entityId: string): Promise<Address[]>;
    getPrimaryAddressForEntity(entityType: string, entityId: string): Promise<Address | null>;
    createAddressForEntity(entityType: string, entityId: string, addressData: Partial<Address>, createdBy: string): Promise<Address>;
    updateAddressForEntity(addressId: string, addressData: Partial<Address>, updatedBy: string): Promise<Address>;
    getContactPersonsForEntity(entityType: string, entityId: string): Promise<ContactPersons[]>;
    getPrimaryContactForEntity(entityType: string, entityId: string): Promise<ContactPersons | null>;
    createContactPersonForEntity(entityType: string, entityId: string, contactData: Partial<ContactPersons>, createdBy: string): Promise<ContactPersons>;
    updateContactPersonForEntity(contactId: string, contactData: Partial<ContactPersons>, updatedBy: string): Promise<ContactPersons>;
    deleteAddressForEntity(addressId: string): Promise<void>;
    deleteContactPersonForEntity(contactId: string): Promise<void>;
    getEntityRelatedData(entityType: string, entityId: string): Promise<{
        addresses: Address[];
        contacts: ContactPersons[];
        primaryAddress: Address | null;
        primaryContact: ContactPersons | null;
    }>;
}
