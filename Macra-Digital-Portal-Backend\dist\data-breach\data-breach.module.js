"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const data_breach_report_controller_1 = require("./data-breach-report.controller");
const data_breach_report_service_1 = require("./data-breach-report.service");
const data_breach_report_entity_1 = require("./data-breach-report.entity");
let DataBreachModule = class DataBreachModule {
};
exports.DataBreachModule = DataBreachModule;
exports.DataBreachModule = DataBreachModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                data_breach_report_entity_1.DataBreachReport,
                data_breach_report_entity_1.DataBreachReportAttachment,
                data_breach_report_entity_1.DataBreachReportStatusHistory,
            ]),
        ],
        controllers: [data_breach_report_controller_1.DataBreachReportController],
        providers: [data_breach_report_service_1.DataBreachReportService],
        exports: [data_breach_report_service_1.DataBreachReportService],
    })
], DataBreachModule);
//# sourceMappingURL=data-breach.module.js.map