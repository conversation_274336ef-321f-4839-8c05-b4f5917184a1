"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateIdentificationTypeDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_identification_type_dto_1 = require("./create-identification-type.dto");
class UpdateIdentificationTypeDto extends (0, swagger_1.PartialType)(create_identification_type_dto_1.CreateIdentificationTypeDto) {
}
exports.UpdateIdentificationTypeDto = UpdateIdentificationTypeDto;
//# sourceMappingURL=update-identification-type.dto.js.map