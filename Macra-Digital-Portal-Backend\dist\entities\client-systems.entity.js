"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientSystems = exports.ClientSystemType = exports.ClientSystemStatus = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
var ClientSystemStatus;
(function (ClientSystemStatus) {
    ClientSystemStatus["ACTIVE"] = "active";
    ClientSystemStatus["INACTIVE"] = "inactive";
    ClientSystemStatus["MAINTENANCE"] = "maintenance";
    ClientSystemStatus["DEPRECATED"] = "deprecated";
})(ClientSystemStatus || (exports.ClientSystemStatus = ClientSystemStatus = {}));
var ClientSystemType;
(function (ClientSystemType) {
    ClientSystemType["WEB_APPLICATION"] = "web_application";
    ClientSystemType["MOBILE_APP"] = "mobile_app";
    ClientSystemType["API_CLIENT"] = "api_client";
    ClientSystemType["THIRD_PARTY_INTEGRATION"] = "third_party_integration";
    ClientSystemType["INTERNAL_SYSTEM"] = "internal_system";
})(ClientSystemType || (exports.ClientSystemType = ClientSystemType = {}));
let ClientSystems = class ClientSystems {
    client_system_id;
    name;
    system_code;
    description;
    system_type;
    status;
    api_endpoint;
    callback_url;
    contact_email;
    contact_phone;
    organization;
    access_permissions;
    last_accessed_at;
    version;
    notes;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    creator;
    updater;
    generateId() {
        this.client_system_id = this.client_system_id || (0, uuid_1.v4)();
    }
};
exports.ClientSystems = ClientSystems;
__decorate([
    (0, typeorm_1.PrimaryColumn)('uuid'),
    __metadata("design:type", String)
], ClientSystems.prototype, "client_system_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], ClientSystems.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, unique: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "system_code", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ClientSystemType,
        default: ClientSystemType.WEB_APPLICATION,
    }),
    __metadata("design:type", String)
], ClientSystems.prototype, "system_type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ClientSystemStatus,
        default: ClientSystemStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], ClientSystems.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "api_endpoint", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "callback_url", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "contact_email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "contact_phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "organization", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "access_permissions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ClientSystems.prototype, "last_accessed_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ClientSystems.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ClientSystems.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ClientSystems.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ClientSystems.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], ClientSystems.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: false }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ClientSystems.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: false }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], ClientSystems.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ClientSystems.prototype, "generateId", null);
exports.ClientSystems = ClientSystems = __decorate([
    (0, typeorm_1.Entity)('client_systems')
], ClientSystems);
//# sourceMappingURL=client-systems.entity.js.map