export declare const UserConstants: {
    readonly PASSWORD_HASH_ROUNDS: 12;
    readonly PAGINATION: {
        readonly DEFAULT_LIMIT: 10;
        readonly MAX_LIMIT: 100;
        readonly DEFAULT_SORT_BY: readonly [readonly ["created_at", "DESC"]];
    };
    readonly RELATIONS: {
        readonly BASIC: readonly ["roles"];
        readonly FULL: readonly ["roles", "department", "organization", "creator", "updater"];
        readonly WITH_DEPARTMENT: readonly ["roles", "department"];
    };
    readonly SORTABLE_COLUMNS: readonly ["first_name", "last_name", "email", "created_at", "status"];
    readonly SEARCHABLE_COLUMNS: readonly ["first_name", "last_name", "email"];
    readonly FILTERABLE_COLUMNS: {
        readonly status: true;
        readonly department_id: true;
    };
};
export declare const UserMessages: {
    readonly USER_NOT_FOUND: "User not found";
    readonly USER_ALREADY_EXISTS: "User with this email already exists";
    readonly EMAIL_ALREADY_TAKEN: "Email is already taken";
    readonly ROLES_NOT_FOUND: "One or more roles not found";
    readonly CURRENT_PASSWORD_INCORRECT: "Current password is incorrect";
    readonly PASSWORD_MISMATCH: "New password and confirmation do not match";
    readonly PASSWORD_CHANGED_SUCCESS: "Password changed successfully";
    readonly USER_NOT_FOUND_AFTER_UPDATE: "User not found after update";
    readonly NO_FILE_UPLOADED: "No file uploaded";
    readonly AVATAR_UPLOAD_FAILED: "Failed to upload avatar";
    readonly EMAIL_SENT_SUCCESS: "Email sent! Please check inbox";
};
export declare class UserUtils {
    static createBase64Image(file: Express.Multer.File): string;
    static getFullName(user: {
        first_name: string;
        last_name: string;
        middle_name?: string;
    }): string;
    static sanitizeUpdateData<T extends Record<string, any>>(data: T): Omit<T, 'password' | 'two_factor_code' | 'two_factor_next_verification'>;
    static isEmailChanged(currentEmail: string, newEmail?: string): boolean;
    static validateRoleIds(roleIds: string[] | undefined): boolean;
    static createPaginationConfig(): {
        sortableColumns: ("created_at" | "first_name" | "last_name" | "email" | "status")[];
        searchableColumns: ("first_name" | "last_name" | "email")[];
        defaultSortBy: ["created_at", "DESC"][];
        defaultLimit: 10;
        maxLimit: 100;
        filterableColumns: {
            readonly status: true;
            readonly department_id: true;
        };
        relations: ("roles" | "department")[];
    };
}
