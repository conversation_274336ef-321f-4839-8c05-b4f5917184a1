"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoFactorError = exports.AuthenticationError = void 0;
exports.isValidTwoFactorAction = isValidTwoFactorAction;
exports.isAuthResponse = isAuthResponse;
exports.isValidEmail = isValidEmail;
exports.isValidUserId = isValidUserId;
const auth_constants_1 = require("../constants/auth.constants");
function isValidTwoFactorAction(action) {
    return Object.values(auth_constants_1.TwoFactorAction).includes(action);
}
function isAuthResponse(obj) {
    return obj &&
        typeof obj.access_token === 'string' &&
        obj.user &&
        typeof obj.user.user_id === 'string' &&
        typeof obj.user.email === 'string';
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidUserId(userId) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(userId);
}
class AuthenticationError extends Error {
    code = 'AUTH_ERROR';
    context;
    constructor(message, context) {
        super(message);
        this.name = 'AuthenticationError';
        this.context = context;
    }
}
exports.AuthenticationError = AuthenticationError;
class TwoFactorError extends Error {
    code = 'TWO_FACTOR_ERROR';
    context;
    constructor(message, context) {
        super(message);
        this.name = 'TwoFactorError';
        this.context = context;
    }
}
exports.TwoFactorError = TwoFactorError;
//# sourceMappingURL=auth.types.js.map