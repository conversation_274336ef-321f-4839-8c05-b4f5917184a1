import {
  Controller,
  Get,
  Put,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
  ParseEnumPipe,
  UseInterceptors,
  UploadedFiles
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApplicationStatusTrackingService } from '../services/application-status-tracking.service';
import { DocumentsService } from '../documents/documents.service';
import { ApplicationStatus } from '../entities/applications.entity';
import {
  UpdateApplicationStatusDto,
  ApplicationStatusTrackingResponseDto,
  ApplicationStatusHistoryResponseDto
} from '../dto/application-status/update-application-status.dto';

@ApiTags('Application Status Tracking')
@Controller('application-status')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ApplicationStatusTrackingController {
  constructor(
    private readonly statusTrackingService: ApplicationStatusTrackingService,
    private readonly documentsService: DocumentsService
  ) {}

  @Put(':applicationId/status')
  @UseInterceptors(FilesInterceptor('attachments', 10, {
    storage: diskStorage({
      destination: './uploads/evaluations',
      filename: (req, file, callback) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
        const ext = extname(file.originalname);
        const filename = `evaluation-${uniqueSuffix}${ext}`;
        callback(null, filename);
      },
    }),
    fileFilter: (req, file, callback) => {
      // Allow common document types
      const allowedMimes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/png',
        'image/jpg'
      ];
      if (allowedMimes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new Error('Invalid file type'), false);
      }
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  }))
  @ApiOperation({
    summary: 'Update application status with optional file attachments',
    description: 'Update the status of an application, create a status history record, and optionally send email notification with attachments'
  })
  @ApiParam({
    name: 'applicationId',
    description: 'Application ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: UpdateApplicationStatusDto,
    description: 'Status update information'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application status updated successfully',
    type: ApplicationStatusTrackingResponseDto
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Application not found'
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid status transition'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access'
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error'
  })
  async updateApplicationStatus(
    @Param('applicationId', ParseUUIDPipe) applicationId: string,
    @Body() updateStatusDto: UpdateApplicationStatusDto,
    @UploadedFiles() files: Express.Multer.File[],
    @Request() req: any
  ): Promise<{
    success: boolean;
    message: string;
    data: ApplicationStatusTrackingResponseDto;
    meta: any;
    timestamp: string;
  }> {
    const userId = req.user?.user_id || req.user?.id;
    
    if (!userId) {
      throw new Error('User ID not found in request');
    }
    
    // Handle file uploads if any
    const uploadedDocuments = [];
    if (files && files.length > 0) {
      for (const file of files) {
        try {
          const document = await this.documentsService.create({
            document_type: 'OTHER',
            file_name: file.originalname,
            entity_type: 'evaluation',
            entity_id: applicationId,
            file_path: file.path,
            file_size: file.size,
            mime_type: file.mimetype,
            is_required: false,
          }, userId);
          uploadedDocuments.push(document);
        } catch (error) {
          console.error(`Failed to save document ${file.originalname}:`, error);
        }
      }
    }

    const result = await this.statusTrackingService.updateApplicationStatus(
      applicationId,
      updateStatusDto,
      userId
    );

    return {
      success: true,
      message: 'Application status updated successfully',
      data: result,
      meta: {
        application_id: applicationId,
        updated_by: userId,
        previous_status: result.status_history[0]?.previous_status,
        new_status: result.current_status,
        uploaded_documents: uploadedDocuments.length,
        documents: uploadedDocuments.map(doc => ({
          document_id: doc.document_id,
          file_name: doc.file_name,
          file_size: doc.file_size
        }))
      },
      timestamp: new Date().toISOString()
    };
  }

  @Get(':applicationId/tracking')
  @ApiOperation({
    summary: 'Get application status tracking',
    description: 'Get detailed status tracking information for an application including history'
  })
  @ApiParam({
    name: 'applicationId',
    description: 'Application ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application status tracking retrieved successfully',
    type: ApplicationStatusTrackingResponseDto
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Application not found'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access'
  })
  async getApplicationStatusTracking(
    @Param('applicationId', ParseUUIDPipe) applicationId: string
  ): Promise<{
    success: boolean;
    message: string;
    data: ApplicationStatusTrackingResponseDto;
    meta: any;
    timestamp: string;
  }> {
    const result = await this.statusTrackingService.getApplicationStatusTracking(applicationId);

    return {
      success: true,
      message: 'Application status tracking retrieved successfully',
      data: result,
      meta: {
        application_id: applicationId,
        total_status_changes: result.status_history.length,
        current_status: result.current_status,
        progress_percentage: result.progress_percentage
      },
      timestamp: new Date().toISOString()
    };
  }

  @Get(':applicationId/history')
  @ApiOperation({
    summary: 'Get application status history',
    description: 'Get the complete status change history for an application'
  })
  @ApiParam({
    name: 'applicationId',
    description: 'Application ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application status history retrieved successfully',
    type: [ApplicationStatusHistoryResponseDto]
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Application not found'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access'
  })
  async getApplicationStatusHistory(
    @Param('applicationId', ParseUUIDPipe) applicationId: string
  ): Promise<{
    success: boolean;
    message: string;
    data: ApplicationStatusHistoryResponseDto[];
    meta: any;
    timestamp: string;
  }> {
    const result = await this.statusTrackingService.getStatusHistory(applicationId);

    return {
      success: true,
      message: 'Application status history retrieved successfully',
      data: result,
      meta: {
        application_id: applicationId,
        total_records: result.length,
        latest_status: result[0]?.status,
        oldest_status: result[result.length - 1]?.status
      },
      timestamp: new Date().toISOString()
    };
  }

  @Get('by-status/:status')
  @ApiOperation({
    summary: 'Get applications by status',
    description: 'Get all applications with a specific status including their tracking information'
  })
  @ApiParam({
    name: 'status',
    description: 'Application status',
    enum: ApplicationStatus,
    example: ApplicationStatus.UNDER_REVIEW
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Applications retrieved successfully',
    type: [ApplicationStatusTrackingResponseDto]
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid status value'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized access'
  })
  async getApplicationsByStatus(
    @Param('status') status: string
  ): Promise<{
    success: boolean;
    message: string;
    data: ApplicationStatusTrackingResponseDto[];
    meta: any;
    timestamp: string;
  }> {
    const result = await this.statusTrackingService.getApplicationsByStatus(status);

    return {
      success: true,
      message: `Applications with status '${status}' retrieved successfully`,
      data: result,
      meta: {
        status: status,
        total_applications: result.length,
        applications_found: result.length > 0
      },
      timestamp: new Date().toISOString()
    };
  }

  @Get('statuses')
  @ApiOperation({
    summary: 'Get available application statuses',
    description: 'Get all available application statuses and their descriptions'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application statuses retrieved successfully'
  })
  async getAvailableStatuses(): Promise<{
    success: boolean;
    message: string;
    data: any[];
    meta: any;
    timestamp: string;
  }> {
    const statuses = Object.values(ApplicationStatus).map(status => ({
      value: status,
      label: status.replace('_', ' ').toUpperCase(),
      description: this.getStatusDescription(status)
    }));

    return {
      success: true,
      message: 'Application statuses retrieved successfully',
      data: statuses,
      meta: {
        total_statuses: statuses.length
      },
      timestamp: new Date().toISOString()
    };
  }

  private getStatusDescription(status: string): string {
    const descriptions: Record<string, string> = {
      'draft': 'Application is being prepared and has not been submitted yet',
      'submitted': 'Application has been submitted and is awaiting review',
      'under_review': 'Application is being reviewed by MACRA staff',
      'evaluation': 'Application is undergoing technical evaluation',
      'approved': 'Application has been approved and license issued',
      'rejected': 'Application has been rejected',
      'withdrawn': 'Application has been withdrawn by the applicant'
    };

    return descriptions[status] || 'Unknown status';
  }
}
