import { Role } from './role.entity';
import { UserIdentification } from './user-identification.entity';
import { Employee } from './employee.entity';
import { Organization } from './organization.entity';
import { Department } from './department.entity';
export declare enum UserStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended"
}
export declare class User {
    user_id: string;
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    middle_name?: string;
    phone: string;
    department_id?: string;
    organization_id?: string;
    status: UserStatus;
    profile_image?: string;
    two_factor_next_verification?: Date;
    two_factor_code?: string;
    two_factor_enabled: boolean;
    two_factor_temp?: string;
    email_verified_at?: Date;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    created_by?: string;
    updated_by?: string;
    last_login?: Date;
    roles?: Role[];
    creator?: User;
    updater?: User;
    organization?: Organization;
    department?: Department;
    identifications: UserIdentification[];
    employee_records: Employee[];
    generateId(): void;
}
