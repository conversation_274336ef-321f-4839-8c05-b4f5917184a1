"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseTypeByCodeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const license_types_service_1 = require("./license-types.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const license_types_entity_1 = require("../entities/license-types.entity");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let LicenseTypeByCodeController = class LicenseTypeByCodeController {
    licenseTypesService;
    constructor(licenseTypesService) {
        this.licenseTypesService = licenseTypesService;
    }
    async findByCode(code) {
        return this.licenseTypesService.findByCode(code);
    }
};
exports.LicenseTypeByCodeController = LicenseTypeByCodeController;
__decorate([
    (0, common_1.Get)(':code'),
    (0, swagger_1.ApiOperation)({ summary: 'Get license type by code' }),
    (0, swagger_1.ApiParam)({ name: 'code', description: 'License type code' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License type retrieved successfully',
        type: license_types_entity_1.LicenseTypes,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'License type not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseType',
        description: 'Viewed license type details by code',
    }),
    __param(0, (0, common_1.Param)('code')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseTypeByCodeController.prototype, "findByCode", null);
exports.LicenseTypeByCodeController = LicenseTypeByCodeController = __decorate([
    (0, swagger_1.ApiTags)('License Types'),
    (0, common_1.Controller)('license-type-by-code'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [license_types_service_1.LicenseTypesService])
], LicenseTypeByCodeController);
//# sourceMappingURL=license-type-by-code.controller.js.map