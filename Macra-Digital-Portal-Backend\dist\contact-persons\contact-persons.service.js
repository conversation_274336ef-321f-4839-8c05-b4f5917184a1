"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactPersonsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const contact_persons_entity_1 = require("../entities/contact-persons.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
let ContactPersonsService = class ContactPersonsService {
    contactPersonsRepository;
    constructor(contactPersonsRepository) {
        this.contactPersonsRepository = contactPersonsRepository;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'updated_at', 'first_name', 'last_name', 'email'],
        searchableColumns: ['first_name', 'last_name', 'email', 'phone', 'designation'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 10,
        maxLimit: 100,
        relations: ['application', 'creator', 'updater'],
    };
    async create(createContactPersonDto, createdBy) {
        const contactPerson = this.contactPersonsRepository.create({
            ...createContactPersonDto,
            created_by: createdBy,
        });
        return this.contactPersonsRepository.save(contactPerson);
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.contactPersonsRepository, this.paginateConfig);
    }
    async findOne(id) {
        const contactPerson = await this.contactPersonsRepository.findOne({
            where: { contact_id: id },
            relations: ['application', 'creator', 'updater'],
        });
        if (!contactPerson) {
            throw new common_1.NotFoundException(`Contact person with ID ${id} not found`);
        }
        return contactPerson;
    }
    async findByApplicationId(applicationId) {
        return this.contactPersonsRepository.find({
            where: { application_id: applicationId },
            relations: ['application', 'creator', 'updater'],
            order: { is_primary: 'DESC', created_at: 'ASC' },
        });
    }
    async findPrimaryByApplicationId(applicationId) {
        return this.contactPersonsRepository.findOne({
            where: { application_id: applicationId, is_primary: true },
            relations: ['application', 'creator', 'updater'],
        });
    }
    async findByEmail(email) {
        return this.contactPersonsRepository.findOne({
            where: { email },
            relations: ['application', 'creator', 'updater'],
        });
    }
    async findByPhone(phone) {
        return this.contactPersonsRepository.findOne({
            where: { phone },
            relations: ['application', 'creator', 'updater'],
        });
    }
    async update(id, updateContactPersonDto, updatedBy) {
        const contactPerson = await this.findOne(id);
        Object.assign(contactPerson, updateContactPersonDto, { updated_by: updatedBy });
        return this.contactPersonsRepository.save(contactPerson);
    }
    async remove(id) {
        const contactPerson = await this.findOne(id);
        await this.contactPersonsRepository.softDelete(contactPerson.contact_id);
    }
    async search(searchTerm) {
        return this.contactPersonsRepository
            .createQueryBuilder('contact_person')
            .leftJoinAndSelect('contact_person.application', 'application')
            .leftJoinAndSelect('contact_person.creator', 'creator')
            .leftJoinAndSelect('contact_person.updater', 'updater')
            .where('contact_person.first_name LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('contact_person.last_name LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('contact_person.email LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('contact_person.phone LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('contact_person.designation LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('contact_person.created_at', 'DESC')
            .limit(20)
            .getMany();
    }
    async setPrimaryContact(applicationId, contactPersonId, updatedBy) {
        await this.contactPersonsRepository.update({ application_id: applicationId, is_primary: true }, { is_primary: false, updated_by: updatedBy });
        const contactPerson = await this.findOne(contactPersonId);
        if (contactPerson.application_id !== applicationId) {
            throw new common_1.NotFoundException(`Contact person does not belong to applicant ${applicationId}`);
        }
        contactPerson.is_primary = true;
        contactPerson.updated_by = updatedBy;
        return this.contactPersonsRepository.save(contactPerson);
    }
    async getContactPersonsByApplicant(applicationId) {
        const allContacts = await this.findByApplicationId(applicationId);
        const primary = allContacts.find(contact => contact.is_primary) || null;
        const secondary = allContacts.filter(contact => !contact.is_primary);
        return { primary, secondary };
    }
};
exports.ContactPersonsService = ContactPersonsService;
exports.ContactPersonsService = ContactPersonsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contact_persons_entity_1.ContactPersons)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ContactPersonsService);
//# sourceMappingURL=contact-persons.service.js.map