"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const license_category_document_entity_1 = require("../../entities/license-category-document.entity");
const license_categories_entity_1 = require("../../entities/license-categories.entity");
class LicenseCategoryDocumentsSeeder {
    async run(dataSource) {
        const documentRepository = dataSource.getRepository(license_category_document_entity_1.LicenseCategoryDocument);
        const categoryRepository = dataSource.getRepository(license_categories_entity_1.LicenseCategories);
        const existingCount = await documentRepository.count();
        if (existingCount > 0) {
            console.log('License category documents already exist, skipping seeder...');
            return;
        }
        const categories = await categoryRepository.find();
        if (categories.length === 0) {
            console.error('No license categories found. Please run license categories seeder first.');
            return;
        }
        const standardDocuments = [
            'Business Plan',
            'Project proposal',
            'Stakeholder CVs',
            'Market analysis and projections',
            'Particulars of financial resources to be applied to project',
            'Tariff proposals',
            'Cash flow projections for 3 years',
            'Experience in the provision of similar services',
            'Business registration or incorporation certificate',
            'Valid tax compliance certificate',
            'Business plan (including service model, financials, and coverage)',
            'Proof of premises (lease/title deed)',
            'Goods in transit insurance',
            'Inventory of fleet/equipment',
            'Customer service policy',
            'IT/tracking system description',
            'Three months of bank statements',
            'Proof of payment (application fee of USD 100)'
        ];
        console.log('Seeding license category documents...');
        for (const category of categories) {
            console.log(`Creating documents for category: ${category.name}`);
            for (const documentName of standardDocuments) {
                const document = documentRepository.create({
                    license_category_id: category.license_category_id,
                    name: documentName,
                    is_required: true,
                });
                await documentRepository.save(document);
                console.log(`  ✅ Created document: ${documentName}`);
            }
        }
        console.log('License category documents seeding completed!');
    }
}
exports.default = LicenseCategoryDocumentsSeeder;
//# sourceMappingURL=license-category-documents.seeder.js.map