import { User } from './user.entity';
import { LicenseCategories } from './license-categories.entity';
export declare class LicenseCategoryDocument {
    license_category_document_id: string;
    license_category_id: string;
    name: string;
    is_required: boolean;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    created_by?: string;
    updated_by?: string;
    license_category: LicenseCategories;
    creator?: User;
    updater?: User;
    generateId(): void;
}
