"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const address_entity_1 = require("../entities/address.entity");
const typeorm_2 = require("typeorm");
let AddressService = class AddressService {
    addressRepository;
    dataSource;
    constructor(addressRepository, dataSource) {
        this.addressRepository = addressRepository;
        this.dataSource = dataSource;
    }
    async createAddress(createAddressDto, createdBy) {
        try {
            console.log('Creating address with data:', createAddressDto);
            console.log('Created by:', createdBy);
            const { entity_type, address_type, address_line_1 } = createAddressDto;
            return this.dataSource.transaction(async (manager) => {
                const repo = manager.getRepository(address_entity_1.Address);
                if (entity_type && address_type && address_line_1) {
                    console.log('Checking for existing address...');
                    const existing = await repo
                        .createQueryBuilder('address')
                        .where('address.entity_type = :entity_type', { entity_type })
                        .andWhere('address.address_type = :address_type', { address_type })
                        .andWhere('address.address_line_1 = :address_line_1', { address_line_1 })
                        .andWhere('address.created_by = :created_by', { created_by: createdBy })
                        .getOne();
                    if (existing) {
                        console.log('Existing address found:', existing);
                        throw new common_1.ConflictException('Address already exists');
                    }
                    console.log('No existing address found, proceeding...');
                }
                console.log('Creating address entity...');
                const address = repo.create({
                    ...createAddressDto,
                    created_by: createdBy,
                });
                console.log('Address entity created:', address);
                try {
                    console.log('Saving address to database...');
                    const savedAddress = await repo.save(address);
                    console.log('Address saved successfully:', savedAddress);
                    return savedAddress;
                }
                catch (error) {
                    console.error('Error saving address:', error);
                    console.error('Error details:', {
                        code: error.code,
                        message: error.message,
                        detail: error.detail,
                        constraint: error.constraint
                    });
                    if (error.code === '23505') {
                        throw new common_1.ConflictException('Duplicate address not allowed');
                    }
                    throw error;
                }
            });
        }
        catch (error) {
            console.error('Error in createAddress method:', error);
            throw error;
        }
    }
    async editAddress(addressId, updateAddressDto, updatedBy) {
        const address = await this.addressRepository.findOne({
            where: { address_id: addressId },
        });
        if (!address) {
            throw new common_1.NotFoundException('Address not found!');
        }
        Object.assign(address, updateAddressDto, { updated_by: updatedBy });
        return this.addressRepository.save(address);
    }
    async findAll(filter) {
        const query = this.addressRepository.createQueryBuilder('address');
        if (filter?.entity_type) {
            query.andWhere('address.entity_type = :entity_type', { entity_type: filter.entity_type });
        }
        if (filter?.entity_id) {
            query.andWhere('address.entity_id = :entity_id', { entity_id: filter.entity_id });
        }
        if (filter?.address_type) {
            query.andWhere('address.address_type = :address_type', { address_type: filter.address_type });
        }
        return query.getMany();
    }
    async findOneById(id) {
        const address = await this.addressRepository.findOne({
            where: { address_id: id },
        });
        if (!address) {
            throw new common_1.NotFoundException(`Address with ID ${id} not found`);
        }
        return address;
    }
    async softDelete(id, deletedBy) {
        const address = await this.findOneById(id);
        address.updated_by = deletedBy;
        await this.addressRepository.save(address);
        await this.addressRepository.softDelete(id);
    }
    async restore(id) {
        const address = await this.addressRepository
            .createQueryBuilder('address')
            .withDeleted()
            .where('address.address_id = :id', { id })
            .getOne();
        if (!address) {
            throw new common_1.NotFoundException(`Deleted address with ID ${id} not found`);
        }
        await this.addressRepository.restore(id);
    }
    async hardDelete(id) {
        const address = await this.findOneById(id);
        await this.addressRepository.remove(address);
    }
};
exports.AddressService = AddressService;
exports.AddressService = AddressService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(address_entity_1.Address)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], AddressService);
//# sourceMappingURL=address.service.js.map