import { AuditAction, AuditModule, AuditStatus } from '../../entities/audit-trail.entity';
export declare class CreateAuditTrailDto {
    action: AuditAction;
    module: AuditModule;
    status: AuditStatus;
    resourceType: string;
    resourceId?: string;
    description?: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    metadata?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
    errorMessage?: string;
    userId?: string;
}
