"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostalCodeSeederService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const postal_code_entity_1 = require("../../entities/postal-code.entity");
const postal_codes_json_1 = __importDefault(require("./data/postal-codes.json"));
let PostalCodeSeederService = class PostalCodeSeederService {
    postalCodeRepository;
    constructor(postalCodeRepository) {
        this.postalCodeRepository = postalCodeRepository;
    }
    async seedPostalCodes() {
        console.log('🌱 Seeding postal codes...');
        const existing = await this.postalCodeRepository.count();
        if (existing > 0) {
            console.log('✅ Postal codes already exist, skipping...');
            return;
        }
        const inserts = postal_codes_json_1.default.map((entry) => this.postalCodeRepository.create({
            region: entry.region,
            district: entry.district,
            location: entry.location,
            postal_code: entry.postal_code,
        }));
        await this.postalCodeRepository.save(inserts);
        console.log(`✅ Seeded ${inserts.length} postal codes.`);
    }
    async seedAll() {
        await this.seedPostalCodes();
    }
    async clearAll() {
        await this.postalCodeRepository.clear();
        console.log('🗑️ Cleared postal codes');
    }
};
exports.PostalCodeSeederService = PostalCodeSeederService;
exports.PostalCodeSeederService = PostalCodeSeederService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(postal_code_entity_1.PostalCode)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PostalCodeSeederService);
//# sourceMappingURL=postal-code.seeder.js.map