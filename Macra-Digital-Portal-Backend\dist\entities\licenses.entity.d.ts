import { User } from './user.entity';
import { Applications } from './applications.entity';
import { Applicants } from './applicant.entity';
import { LicenseTypes } from './license-types.entity';
export declare enum LicenseStatus {
    ACTIVE = "active",
    EXPIRED = "expired",
    SUSPENDED = "suspended",
    REVOKED = "revoked",
    UNDER_REVIEW = "under_review"
}
export declare class Licenses {
    license_id: string;
    license_number: string;
    application_id: string;
    applicant_id: string;
    license_type_id: string;
    status: LicenseStatus;
    issue_date: Date;
    expiry_date: Date;
    issued_by: string;
    code: string;
    conditions?: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    application: Applications;
    applicant: Applicants;
    license_type: LicenseTypes;
    issuer: User;
    creator: User;
    updater?: User;
    generateId(): void;
}
