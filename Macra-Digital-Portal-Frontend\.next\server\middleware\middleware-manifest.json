{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|images).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pkAVxNdFHZNDgRfuvZvV1IWMSbz21AN1Qva+PakX6OI=", "__NEXT_PREVIEW_MODE_ID": "a5a6170f3c8e5abc039ba7a975ca33d4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f6cfe3509b16bb518a6f14801a18a15a3eff24b80bcfd7afae26f43707817f00", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e4d7f784b2cbd154c931c13a8e897a57b78a8183d16582e09f865a531aec263a"}}}, "instrumentation": null, "functions": {}}