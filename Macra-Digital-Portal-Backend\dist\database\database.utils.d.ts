import { DataSource, DataSourceOptions } from 'typeorm';
import { ConfigService } from '@nestjs/config';
export type DatabaseType = 'postgres' | 'mysql' | 'mariadb' | 'mssql';
export declare function getDatabaseType(driver: string): DatabaseType;
export declare function getUuidGenerationStrategy(dbType: DatabaseType): string;
export declare function getDatabaseSpecificOptions(dbType: DatabaseType, config: ConfigService): Partial<DataSourceOptions>;
export declare function initializeDatabase(dataSource: DataSource): Promise<void>;
export declare function getUuidColumnDefinition(dbType: DatabaseType): any;
