"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDatabaseType = getDatabaseType;
exports.getUuidGenerationStrategy = getUuidGenerationStrategy;
exports.getDatabaseSpecificOptions = getDatabaseSpecificOptions;
exports.initializeDatabase = initializeDatabase;
exports.getUuidColumnDefinition = getUuidColumnDefinition;
function getDatabaseType(driver) {
    switch (driver?.toLowerCase()) {
        case 'mysql':
            return 'mysql';
        case 'mariadb':
            return 'mariadb';
        case 'postgres':
        case 'postgresql':
            return 'postgres';
        case 'mssql':
        case 'sqlserver':
            return 'mssql';
        default:
            return 'postgres';
    }
}
function getUuidGenerationStrategy(dbType) {
    switch (dbType) {
        case 'postgres':
            return 'gen_random_uuid()';
        case 'mysql':
        case 'mariadb':
            return 'UUID()';
        case 'mssql':
            return 'NEWID()';
        default:
            return 'gen_random_uuid()';
    }
}
function getDatabaseSpecificOptions(dbType, config) {
    const baseOptions = {
        host: config.get('DB_HOST'),
        port: config.get('DB_PORT'),
        username: config.get('DB_USERNAME'),
        password: config.get('DB_PASSWORD'),
        database: config.get('DB_NAME'),
        synchronize: config.get('NODE_ENV') !== 'production',
        logging: config.get('NODE_ENV') === 'development',
        retryAttempts: 3,
        retryDelay: 3000,
    };
    switch (dbType) {
        case 'postgres':
            return {
                ...baseOptions,
                type: 'postgres',
                ssl: config.get('DB_SSL', 'false') === 'true'
                    ? { rejectUnauthorized: false }
                    : false,
                extra: {
                    installExtensions: false,
                },
            };
        case 'mysql':
            return {
                ...baseOptions,
                type: 'mysql',
                charset: 'utf8mb4',
                timezone: '+00:00',
                ssl: config.get('DB_SSL', 'false') === 'true'
                    ? { rejectUnauthorized: false }
                    : false,
            };
        case 'mariadb':
            return {
                ...baseOptions,
                type: 'mariadb',
                charset: 'utf8mb4',
                timezone: '+00:00',
                ssl: config.get('DB_SSL', 'false') === 'true'
                    ? { rejectUnauthorized: false }
                    : false,
            };
        case 'mssql':
            return {
                ...baseOptions,
                type: 'mssql',
                options: {
                    encrypt: config.get('DB_SSL', 'false') === 'true',
                    trustServerCertificate: true,
                },
            };
        default:
            return baseOptions;
    }
}
async function initializeDatabase(dataSource) {
    const dbType = dataSource.options.type;
    try {
        switch (dbType) {
            case 'postgres':
                try {
                    await dataSource.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
                    console.log('✅ PostgreSQL uuid-ossp extension enabled');
                }
                catch (error) {
                    console.log('⚠️  uuid-ossp extension not available, using gen_random_uuid() instead');
                    try {
                        await dataSource.query('SELECT gen_random_uuid();');
                        console.log('✅ PostgreSQL gen_random_uuid() function available');
                    }
                    catch (genRandomError) {
                        console.warn('⚠️  Neither uuid-ossp nor gen_random_uuid() available. UUID generation may fail.');
                    }
                }
                break;
            case 'mysql':
            case 'mariadb':
                try {
                    await dataSource.query('SELECT UUID();');
                    console.log('✅ MySQL/MariaDB UUID() function available');
                }
                catch (error) {
                    console.warn('⚠️  MySQL/MariaDB UUID() function not available');
                }
                break;
            case 'mssql':
                try {
                    await dataSource.query('SELECT NEWID();');
                    console.log('✅ SQL Server NEWID() function available');
                }
                catch (error) {
                    console.warn('⚠️  SQL Server NEWID() function not available');
                }
                break;
        }
    }
    catch (error) {
        console.warn('Database initialization warning:', error.message);
    }
}
function getUuidColumnDefinition(dbType) {
    switch (dbType) {
        case 'postgres':
            return {
                type: 'uuid',
                generatedType: 'uuid',
                default: () => 'gen_random_uuid()',
            };
        case 'mysql':
        case 'mariadb':
            return {
                type: 'varchar',
                length: 36,
                default: () => 'UUID()',
            };
        case 'mssql':
            return {
                type: 'uniqueidentifier',
                default: () => 'NEWID()',
            };
        default:
            return {
                type: 'uuid',
                generatedType: 'uuid',
                default: () => 'gen_random_uuid()',
            };
    }
}
//# sourceMappingURL=database.utils.js.map