{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/styles/dashboard.css"], "sourcesContent": ["/* Dashboard-specific styles */\r\n\r\n/* Progress bar utility classes */\r\n.progress-bar-72 {\r\n  width: 72%;\r\n}\r\n\r\n.progress-bar-45 {\r\n  width: 45%;\r\n}\r\n\r\n/* Generic progress bar width classes */\r\n.progress-bar-10 { width: 10%; }\r\n.progress-bar-20 { width: 20%; }\r\n.progress-bar-25 { width: 25%; }\r\n.progress-bar-30 { width: 30%; }\r\n.progress-bar-40 { width: 40%; }\r\n.progress-bar-50 { width: 50%; }\r\n.progress-bar-60 { width: 60%; }\r\n.progress-bar-70 { width: 70%; }\r\n.progress-bar-75 { width: 75%; }\r\n.progress-bar-80 { width: 80%; }\r\n.progress-bar-90 { width: 90%; }\r\n.progress-bar-100 { width: 100%; }\r\n\r\n/* Progress bar container */\r\n.progress-container {\r\n  margin-top: 0.25rem;\r\n  height: 0.5rem;\r\n  width: 100%;\r\n  border-radius: 9999px;\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\r\n}\r\n:is(:where(.dark) .progress-container) {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity));\r\n}\r\n\r\n/* Progress bar fill */\r\n.progress-fill {\r\n  height: 0.5rem;\r\n  border-radius: 9999px;\r\n}\r\n\r\n/* Progress bar color variants */\r\n.progress-green {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity));\r\n}\r\n:is(:where(.dark) .progress-green) {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity));\r\n}\r\n\r\n.progress-yellow {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity));\r\n}\r\n\r\n:is(:where(.dark) .progress-yellow) {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity));\r\n}\r\n\r\n.progress-red {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity));\r\n}\r\n\r\n:is(:where(.dark) .progress-red) {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\r\n}\r\n\r\n.progress-blue {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\r\n}\r\n\r\n:is(:where(.dark) .progress-blue) {\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity));\r\n}\r\n\r\n/* Spectrum utilization cards */\r\n.spectrum-utilization-card {\r\n  border-radius: 16px;\r\n  border-width: 1px;\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(229 231 235 / var(--tw-border-opacity));\r\n  padding: 1rem;\r\n}\r\n:is(:where(.dark) .spectrum-utilization-card) {\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(55 65 81 / var(--tw-border-opacity));\r\n}\r\n\r\n.spectrum-band-title {\r\n  font-weight: 500;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(17 24 39 / var(--tw-text-opacity));\r\n}\r\n\r\n:is(:where(.dark) .spectrum-band-title) {\r\n  --tw-text-opacity: 1;\r\n  color: rgb(243 244 246 / var(--tw-text-opacity));\r\n}\r\n\r\n.spectrum-band-range {\r\n  font-size: 0.875rem;\r\n  line-height: 1.25rem;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(75 85 99 / var(--tw-text-opacity));\r\n}\r\n\r\n:is(:where(.dark) .spectrum-band-range) {\r\n  --tw-text-opacity: 1;\r\n  color: rgb(156 163 175 / var(--tw-text-opacity));\r\n}\r\n\r\n.spectrum-utilization-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 0.875rem;\r\n  line-height: 1.25rem;\r\n}\r\n\r\n.spectrum-utilization-label {\r\n  --tw-text-opacity: 1;\r\n  color: rgb(75 85 99 / var(--tw-text-opacity));\r\n}\r\n\r\n:is(:where(.dark) .spectrum-utilization-label) {\r\n  --tw-text-opacity: 1;\r\n  color: rgb(156 163 175 / var(--tw-text-opacity));\r\n}\r\n\r\n.spectrum-utilization-value {\r\n  --tw-text-opacity: 1;\r\n  color: rgb(17 24 39 / var(--tw-text-opacity));\r\n}\r\n\r\n:is(:where(.dark) .spectrum-utilization-value) {\r\n  --tw-text-opacity: 1;\r\n  color: rgb(243 244 246 / var(--tw-text-opacity));\r\n}"], "names": [], "mappings": "AAGA;;;;AAIA;;;;AAKA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;;;;;;;;;AAQA;;;;;AAMA;;;;;AAMA;;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAKA", "debugId": null}}]}