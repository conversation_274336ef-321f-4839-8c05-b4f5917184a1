import { Repository } from 'typeorm';
import { Organization } from '../entities/organization.entity';
import { UpdateOrganizationDto } from 'src/dto/organizations/update-organization.dto';
import { CreateOrganizationDto } from 'src/dto/organizations/create-organization.dto';
export declare class OrganizationService {
    private readonly orgRepo;
    private readonly logger;
    constructor(orgRepo: Repository<Organization>);
    create(dto: CreateOrganizationDto): Promise<Organization>;
    findAll(): Promise<Organization[]>;
    findOne(id: string): Promise<Organization>;
    update(id: string, dto: UpdateOrganizationDto): Promise<Organization>;
    remove(id: string): Promise<void>;
    hardDelete(id: string): Promise<void>;
}
