"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateApplicationDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class CreateApplicationDto {
    application_number;
    applicant_id;
    license_category_id;
    status;
    current_step;
    progress_percentage;
    submitted_at;
}
exports.CreateApplicationDto = CreateApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique application number',
        example: 'APP-20230101-123456',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "application_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID of the applicant',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "applicant_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID of the license category',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "license_category_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Application status',
        enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn'],
        default: 'draft',
        example: 'draft',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (!value || value === '') {
            return 'draft';
        }
        const validStatuses = ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn'];
        return validStatuses.includes(value) ? value : 'draft';
    }),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Current step in the application process (1)',
        minimum: 1,
        example: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateApplicationDto.prototype, "current_step", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Application progress percentage (0-100)',
        minimum: 0,
        maximum: 100,
        example: 25,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateApplicationDto.prototype, "progress_percentage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Date when the application was submitted',
        example: '2023-01-01T12:00:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateApplicationDto.prototype, "submitted_at", void 0);
//# sourceMappingURL=create-application.dto.js.map