{"version": 3, "file": "license-category-documents.service.js", "sourceRoot": "", "sources": ["../../src/license-category-documents/license-category-documents.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAkF;AAClF,6CAAmD;AACnD,qCAAqC;AACrC,qDAAqE;AACrE,mGAAuF;AAKhF,IAAM,+BAA+B,GAArC,MAAM,+BAA+B;IAGvB;IAFnB,YAEmB,iCAAsE;QAAtE,sCAAiC,GAAjC,iCAAiC,CAAqC;IACtF,CAAC;IAEJ,KAAK,CAAC,MAAM,CACV,gCAAkE,EAClE,MAAc;QAGd,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC;YAC5E,KAAK,EAAE;gBACL,mBAAmB,EAAE,gCAAgC,CAAC,mBAAmB;gBACzE,IAAI,EAAE,gCAAgC,CAAC,IAAI;aAC5C;SACF,CAAC,CAAC;QAEH,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,gCAAgC,CAAC,IAAI,4CAA4C,CACzG,CAAC;QACJ,CAAC;QAED,MAAM,uBAAuB,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC;YAC5E,GAAG,gCAAgC;YACnC,WAAW,EAAE,gCAAgC,CAAC,WAAW,IAAI,IAAI;YACjE,UAAU,EAAE,MAAM;SACnB,CAAC,CAAC;QAEH,OAAO,MAAM,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACpF,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,KAAoB;QAChC,OAAO,IAAA,0BAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,iCAAiC,EAAE;YAC7D,eAAe,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,CAAC;YACpE,iBAAiB,EAAE,CAAC,MAAM,CAAC;YAC3B,aAAa,EAAE,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACvC,SAAS,EAAE,CAAC,kBAAkB,EAAE,SAAS,EAAE,SAAS,CAAC;YACrD,MAAM,EAAE;gBACN,8BAA8B;gBAC9B,qBAAqB;gBACrB,MAAM;gBACN,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,sCAAsC;gBACtC,uBAAuB;gBACvB,iBAAiB;gBACjB,oBAAoB;gBACpB,mBAAmB;gBACnB,eAAe;gBACf,iBAAiB;gBACjB,oBAAoB;gBACpB,mBAAmB;gBACnB,eAAe;aAChB;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC;YACnF,KAAK,EAAE,EAAE,4BAA4B,EAAE,EAAE,EAAE;YAC3C,SAAS,EAAE,CAAC,kBAAkB,EAAE,SAAS,EAAE,SAAS,CAAC;SACtD,CAAC,CAAC;QAEH,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC7B,MAAM,IAAI,0BAAiB,CAAC,qCAAqC,EAAE,YAAY,CAAC,CAAC;QACnF,CAAC;QAED,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,iBAAyB;QACnD,OAAO,MAAM,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC;YACvD,KAAK,EAAE,EAAE,mBAAmB,EAAE,iBAAiB,EAAE;YACjD,SAAS,EAAE,CAAC,kBAAkB,EAAE,SAAS,EAAE,SAAS,CAAC;YACrD,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CACV,EAAU,EACV,gCAAkE,EAClE,MAAc;QAEd,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGvD,IAAI,gCAAgC,CAAC,IAAI,IAAI,gCAAgC,CAAC,IAAI,KAAK,uBAAuB,CAAC,IAAI,EAAE,CAAC;YACpH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC;gBAC5E,KAAK,EAAE;oBACL,mBAAmB,EAAE,gCAAgC,CAAC,mBAAmB,IAAI,uBAAuB,CAAC,mBAAmB;oBACxH,IAAI,EAAE,gCAAgC,CAAC,IAAI;iBAC5C;aACF,CAAC,CAAC;YAEH,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,4BAA4B,KAAK,EAAE,EAAE,CAAC;gBAC7E,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,gCAAgC,CAAC,IAAI,4CAA4C,CACzG,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,gCAAgC,CAAC,CAAC;QACzE,uBAAuB,CAAC,UAAU,GAAG,MAAM,CAAC;QAE5C,OAAO,MAAM,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACpF,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACvD,MAAM,IAAI,CAAC,iCAAiC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,8BAA8B,uBAAuB,CAAC,IAAI,iCAAiC,EAAE,CAAC;IAClH,CAAC;CACF,CAAA;AAnHY,0EAA+B;0CAA/B,+BAA+B;IAD3C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0DAAuB,CAAC,CAAA;qCACU,oBAAU;GAHrD,+BAA+B,CAmH3C"}