import { Repository } from 'typeorm';
import { Task } from '../entities/tasks.entity';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { UpdateTaskDto } from '../dto/tasks/update-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { ApplicationsService } from '../applications/applications.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { UsersService } from '../users/users.service';
export declare class TasksService {
    private readonly tasksRepository;
    private readonly applicationsService;
    private readonly notificationHelper;
    private readonly usersService;
    constructor(tasksRepository: Repository<Task>, applicationsService: ApplicationsService, notificationHelper: NotificationHelperService, usersService: UsersService);
    private readonly paginateConfig;
    private updateApplicationAssignment;
    private sendTaskAssignmentNotification;
    create(createTaskDto: CreateTaskDto, creatorId: string): Promise<Task>;
    findAll(query: PaginateQuery): Promise<Paginated<Task>>;
    findUnassigned(query: PaginateQuery): Promise<Paginated<Task>>;
    findAssigned(query: PaginateQuery): Promise<Paginated<Task>>;
    findAssignedToUser(userId: string, query: PaginateQuery): Promise<Paginated<Task>>;
    findOne(id: string): Promise<Task>;
    findOneWithNavigationInfo(id: string): Promise<{
        task: Task;
        canNavigateToEntity: boolean;
    }>;
    update(id: string, updateTaskDto: UpdateTaskDto): Promise<Task>;
    assign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task>;
    reassign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task>;
    assignOrReassign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task>;
    remove(id: string): Promise<void>;
    getTaskStats(): Promise<{
        total: number;
        unassigned: number;
        assigned: number;
        completed: number;
        overdue: number;
    }>;
}
