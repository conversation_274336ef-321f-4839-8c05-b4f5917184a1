{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { LicenseType } from './licenseTypeService';\r\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\r\n\r\n// Utility functions for category codes\r\nexport const generateCategoryCode = (name: string): string => {\r\n  return name\r\n    .toLowerCase()\r\n    .replace(/[^a-z0-9\\s]/g, '') // Remove special characters\r\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\r\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\r\n    .substring(0, 50); // Limit length\r\n};\r\n\r\nexport const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {\r\n  return categories.map(category => ({\r\n    ...category,\r\n    code: generateCategoryCode(category.name),\r\n    children: category.children ? addCodesToCategories(category.children) : undefined\r\n  }));\r\n};\r\n\r\nexport const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {\r\n  for (const category of categories) {\r\n    if (category.code === code) {\r\n      return category;\r\n    }\r\n    if (category.children) {\r\n      const found = findCategoryByCode(category.children, code);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {\r\n  for (const category of categories) {\r\n    if (category.license_category_id === id) {\r\n      return category;\r\n    }\r\n    if (category.children) {\r\n      const found = findCategoryById(category.children, id);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\n// Types\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  license_type_id: string;\r\n  parent_id?: string;\r\n  name: string;\r\n  fee: string;\r\n  description: string;\r\n  authorizes: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n  license_type?: LicenseType;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  creator?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  updater?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  // Generated code for URL-friendly routing\r\n  code?: string;\r\n}\r\n\r\nexport interface CreateLicenseCategoryDto {\r\n  license_type_id: string;\r\n  parent_id?: string;\r\n  name: string;\r\n  fee: string;\r\n  description: string;\r\n  authorizes: string;\r\n}\r\n\r\nexport interface UpdateLicenseCategoryDto {\r\n  license_type_id?: string;\r\n  parent_id?: string;\r\n  name?: string;\r\n  fee?: string;\r\n  description?: string;\r\n  authorizes?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\nexport type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport const licenseCategoryService = {\r\n  // Get all license categories with pagination\r\n  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-categories?${params.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get license category by ID with timeout and retry handling\r\n  async getLicenseCategory(id: string): Promise<LicenseCategory> {\r\n    try {\r\n      const response = await apiClient.get(`/license-categories/${id}`, {\r\n        timeout: 30000, // 30 second timeout for individual requests\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error fetching license category:', error);\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get license categories by license type with improved error handling\r\n  async getLicenseCategoriesByType(licenseTypeId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error fetching license categories by type:', error);\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create new license category\r\n  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {\r\n    const response = await apiClient.post('/license-categories', licenseCategoryData);\r\n    return response.data;\r\n  },\r\n\r\n  // Update license category\r\n  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {\r\n    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete license category\r\n  async deleteLicenseCategory(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-categories/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all license categories (simple list for dropdowns) with caching\r\n  async getAllLicenseCategories(): Promise<any> {\r\n    return cacheService.getOrSet(\r\n      CACHE_KEYS.LICENSE_CATEGORIES,\r\n      async () => {\r\n        console.log('Fetching license categories from API...');\r\n        // Reduce limit to avoid rate limiting\r\n        const response = await this.getLicenseCategories({ limit: 100 });\r\n        return addCodesToCategories(response.data);\r\n      },\r\n      CACHE_TTL.LONG // Cache for 15 minutes\r\n    );\r\n  },\r\n\r\n  // Get hierarchical tree of categories for a license type with caching\r\n  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {\r\n    return cacheService.getOrSet(\r\n      `category-tree-${licenseTypeId}`,\r\n      async () => {\r\n        console.log(`Fetching category tree for license type: ${licenseTypeId}`);\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n        return addCodesToCategories(response.data);\r\n      },\r\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\r\n    );\r\n  },\r\n\r\n  // Get root categories (no parent) for a license type with caching\r\n  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {\r\n    return cacheService.getOrSet(\r\n      `root-categories-${licenseTypeId}`,\r\n      async () => {\r\n        console.log(`Fetching root categories for license type: ${licenseTypeId}`);\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);\r\n        return response.data;\r\n      },\r\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\r\n    );\r\n  },\r\n\r\n  // Get license categories for parent selection dropdown\r\n  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\r\n    try {\r\n      const params = excludeId ? { excludeId } : {};\r\n      console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);\r\n\r\n      // Try the new endpoint first\r\n      try {\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });\r\n\r\n\r\n        if (response.data && Array.isArray(response.data.data)) {\r\n          console.log('✅ Valid array response with', response.data.data.length, 'items')\r\n          return response.data.data;\r\n        } else {\r\n          console.warn('⚠️ API returned non-array data:', response.data);\r\n          return [];\r\n        }\r\n      } catch (newEndpointError) {\r\n        console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);\r\n\r\n        // Fallback to existing endpoint\r\n        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n        console.log('🔄 Fallback response:', response.data);\r\n\r\n        if (response.data && Array.isArray(response.data)) {\r\n          // Filter out the excluded category if specified\r\n          let categories = response.data;\r\n          if (excludeId) {\r\n            categories = categories.filter(cat => cat.license_category_id !== excludeId);\r\n          }\r\n          console.log('✅ Fallback successful with', categories.length, 'items');\r\n          return categories;\r\n        } else {\r\n          console.warn('⚠️ Fallback also returned non-array data:', response.data);\r\n          return [];\r\n        }\r\n      }\r\n    } catch (error) {\r\n\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Get potential parent categories for a license type\r\n  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\r\n    const params = excludeId ? { excludeId } : {};\r\n    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });\r\n    return response.data;\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,SAAS,CAAC,GAAG,KAAK,eAAe;AACtC;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;YACjC,GAAG,QAAQ;YACX,MAAM,qBAAqB,SAAS,IAAI;YACxC,UAAU,SAAS,QAAQ,GAAG,qBAAqB,SAAS,QAAQ,IAAI;QAC1E,CAAC;AACH;AAEO,MAAM,qBAAqB,CAAC,YAA+B;IAChE,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,IAAI,KAAK,MAAM;YAC1B,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,mBAAmB,SAAS,QAAQ,EAAE;YACpD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAC,YAA+B;IAC9D,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,mBAAmB,KAAK,IAAI;YACvC,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,iBAAiB,SAAS,QAAQ,EAAE;YAClD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAqFO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,MAAM,sBAAqB,QAAuB,CAAC,CAAC;QAClD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,6DAA6D;IAC7D,MAAM,oBAAmB,EAAU;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;gBAChE,SAAS;YACX;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM;QACR;IACF;IAEA,sEAAsE;IACtE,MAAM,4BAA2B,aAAqB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe,EAAE;gBAC3F,SAAS;YACX;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,uBAAsB,mBAA6C;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU,EAAE,mBAA6C;QACnF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,sEAAsE;IACtE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,kBAAkB,EAC7B;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAAE,OAAO;YAAI;YAC9D,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,sEAAsE;IACtE,MAAM,iBAAgB,aAAqB;QACzC,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,cAAc,EAAE,eAAe,EAChC;YACE,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,eAAe;YACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,kIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,kEAAkE;IAClE,MAAM,mBAAkB,aAAqB;QAC3C,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,gBAAgB,EAAE,eAAe,EAClC;YACE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,eAAe;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,uDAAuD;IACvD,MAAM,iCAAgC,aAAqB,EAAE,SAAkB;QAC7E,IAAI;YACF,MAAM,SAAS,YAAY;gBAAE;YAAU,IAAI,CAAC;YAC5C,QAAQ,GAAG,CAAC,mDAAmD,eAAe,gBAAgB;YAE9F,6BAA6B;YAC7B,IAAI;gBACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,qBAAqB,CAAC,EAAE;oBAAE;gBAAO;gBAGxH,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACtD,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACtE,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;oBAC7D,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,IAAI,CAAC,4CAA4C;gBAEzD,gCAAgC;gBAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;gBAC3F,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;gBAElD,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACjD,gDAAgD;oBAChD,IAAI,aAAa,SAAS,IAAI;oBAC9B,IAAI,WAAW;wBACb,aAAa,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,mBAAmB,KAAK;oBACpE;oBACA,QAAQ,GAAG,CAAC,8BAA8B,WAAW,MAAM,EAAE;oBAC7D,OAAO;gBACT,OAAO;oBACL,QAAQ,IAAI,CAAC,6CAA6C,SAAS,IAAI;oBACvE,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YAEd,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAoB,aAAqB,EAAE,SAAkB;QACjE,MAAM,SAAS,YAAY;YAAE;QAAU,IAAI,CAAC;QAC5C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,kBAAkB,CAAC,EAAE;YAAE;QAAO;QACrH,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/config/licenseTypeStepConfig.ts"], "sourcesContent": ["/**\r\n * License Type Step Configuration System\r\n *\r\n * SINGLE SOURCE OF TRUTH for all license type step configurations\r\n *\r\n * This is the consolidated configuration system that defines:\r\n * - Which form steps are required for each license type\r\n * - Step order and navigation flow\r\n * - Validation requirements and estimated times\r\n * - Fallback configurations for unknown license types\r\n *\r\n * Supports the 5 specified license type codes:\r\n * - telecommunications\r\n * - postal_services\r\n * - standards_compliance\r\n * - broadcasting\r\n * - spectrum_management\r\n *\r\n * Features:\r\n * - Optimized step loading based on license type codes\r\n * - Automatic fallback for unsupported types\r\n * - Smart license type resolution (UUID, code, name mapping)\r\n * - Comprehensive helper functions for navigation and progress tracking\r\n */\r\n\r\nexport interface StepConfig {\r\n  id: string;\r\n  name: string;\r\n  component: string;\r\n  route: string;\r\n  required: boolean;\r\n  description: string;\r\n  estimatedTime: string; // in minutes\r\n}\r\n\r\nexport interface LicenseTypeStepConfig {\r\n  licenseTypeId: string;\r\n  name: string;\r\n  description: string;\r\n  steps: StepConfig[];\r\n  estimatedTotalTime: string;\r\n  requirements: string[];\r\n}\r\n\r\n// Base steps that can be used across license types\r\nconst BASE_STEPS: Record<string, StepConfig> = {\r\n  applicantInfo: {\r\n    id: 'applicant-info',\r\n    name: 'Applicant Information',\r\n    component: 'ApplicantInfo',\r\n    route: 'applicant-info',\r\n    required: true,\r\n    description: 'Personal or company information of the applicant',\r\n    estimatedTime: '5'\r\n  },\r\n  addressInfo: {\r\n    id: 'address-info',\r\n    name: 'Address Information',\r\n    component: 'AddressInfo',\r\n    route: 'address-info',\r\n    required: true,\r\n    description: 'Physical and postal address details',\r\n    estimatedTime: '3'\r\n  },\r\n  contactInfo: {\r\n    id: 'contact-info',\r\n    name: 'Contact Information',\r\n    component: 'ContactInfo',\r\n    route: 'contact-info',\r\n    required: true,\r\n    description: 'Contact details and communication preferences',\r\n    estimatedTime: '5'\r\n  },\r\n\r\n  management: {\r\n    id: 'management',\r\n    name: 'Management Structure',\r\n    component: 'Management',\r\n    route: 'management',\r\n    required: false,\r\n    description: 'Management team and organizational structure',\r\n    estimatedTime: '8'\r\n  },\r\n  professionalServices: {\r\n    id: 'professional-services',\r\n    name: 'Professional Services',\r\n    component: 'ProfessionalServices',\r\n    route: 'professional-services',\r\n    required: false,\r\n    description: 'External consultants and service providers',\r\n    estimatedTime: '6'\r\n  },\r\n  serviceScope: {\r\n    id: 'service-scope',\r\n    name: 'Service Scope',\r\n    component: 'ServiceScope',\r\n    route: 'service-scope',\r\n    required: true,\r\n    description: 'Services offered and geographic coverage',\r\n    estimatedTime: '8'\r\n  },\r\n\r\n  legalHistory: {\r\n    id: 'legal-history',\r\n    name: 'Legal History',\r\n    component: 'LegalHistory',\r\n    route: 'legal-history',\r\n    required: true,\r\n    description: 'Legal compliance and regulatory history',\r\n    estimatedTime: '5'\r\n  },\r\n  documents: {\r\n    id: 'documents',\r\n    name: 'Required Documents',\r\n    component: 'Documents',\r\n    route: 'documents',\r\n    required: true,\r\n    description: 'Upload required documents for license application',\r\n    estimatedTime: '10'\r\n  },\r\n  submit: {\r\n    id: 'submit',\r\n    name: 'Finalise Application',\r\n    component: 'Submit',\r\n    route: 'submit',\r\n    required: true,\r\n    description: 'Final Review and finalise and submission of application',\r\n    estimatedTime: '5'\r\n  }\r\n};\r\n\r\n// License type specific configurations\r\nexport const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {\r\n  telecommunications: {\r\n    licenseTypeId: 'telecommunications',\r\n    name: 'Telecommunications License',\r\n    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '97 minutes',\r\n    requirements: [\r\n      'Business registration certificate',\r\n      'Tax compliance certificate',\r\n      'Technical specifications',\r\n      'Financial statements',\r\n      'Management CVs',\r\n      'Network coverage plans'\r\n    ]\r\n  },\r\n\r\n  postal_services: {\r\n    licenseTypeId: 'postal_services',\r\n    name: 'Postal Services License',\r\n    description: 'License for postal and courier service providers',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '65 minutes',\r\n    requirements: [\r\n      'Business registration certificate',\r\n      'Fleet inventory',\r\n      'Service coverage map',\r\n      'Insurance certificates',\r\n      'Premises documentation'\r\n    ]\r\n  },\r\n\r\n  standards_compliance: {\r\n    licenseTypeId: 'standards_compliance',\r\n    name: 'Standards Compliance License',\r\n    description: 'License for standards compliance and certification services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '82 minutes',\r\n    requirements: [\r\n      'Accreditation certificates',\r\n      'Technical competency proof',\r\n      'Quality management system',\r\n      'Laboratory facilities documentation',\r\n      'Staff qualifications'\r\n    ]\r\n  },\r\n\r\n  broadcasting: {\r\n    licenseTypeId: 'broadcasting',\r\n    name: 'Broadcasting License',\r\n    description: 'License for radio and television broadcasting services',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '86 minutes',\r\n    requirements: [\r\n      'Broadcasting equipment specifications',\r\n      'Content programming plan',\r\n      'Studio facility documentation',\r\n      'Transmission coverage maps',\r\n      'Local content compliance plan'\r\n    ]\r\n  },\r\n\r\n  spectrum_management: {\r\n    licenseTypeId: 'spectrum_management',\r\n    name: 'Spectrum Management License',\r\n    description: 'License for radio frequency spectrum management and allocation',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.serviceScope,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '89 minutes',\r\n    requirements: [\r\n      'Spectrum usage plan',\r\n      'Technical interference analysis',\r\n      'Equipment type approval',\r\n      'Frequency coordination agreements',\r\n      'Monitoring capabilities documentation'\r\n    ]\r\n  },\r\n\r\n  clf: {\r\n    licenseTypeId: 'clf',\r\n    name: 'CLF License',\r\n    description: 'Consumer Lending and Finance license',\r\n    steps: [\r\n      BASE_STEPS.applicantInfo,\r\n      BASE_STEPS.addressInfo,\r\n      BASE_STEPS.contactInfo,\r\n      BASE_STEPS.management,\r\n      BASE_STEPS.professionalServices,\r\n      BASE_STEPS.legalHistory,\r\n      BASE_STEPS.documents,\r\n      BASE_STEPS.submit\r\n    ],\r\n    estimatedTotalTime: '51 minutes',\r\n    requirements: [\r\n      'Financial institution license',\r\n      'Capital adequacy documentation',\r\n      'Risk management framework',\r\n      'Consumer protection policies',\r\n      'Anti-money laundering procedures'\r\n    ]\r\n  }\r\n};\r\n\r\n// License type name to config key mapping\r\nconst LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {\r\n  'telecommunications': 'telecommunications',\r\n  'postal services': 'postal_services',\r\n  'postal_services': 'postal_services',\r\n  'standards compliance': 'standards_compliance',\r\n  'standards_compliance': 'standards_compliance',\r\n  'broadcasting': 'broadcasting',\r\n  'spectrum management': 'spectrum_management',\r\n  'spectrum_management': 'spectrum_management',\r\n  'clf': 'clf',\r\n  'consumer lending and finance': 'clf'\r\n};\r\n\r\n// Default fallback configuration for unknown license types\r\nconst DEFAULT_FALLBACK_CONFIG: LicenseTypeStepConfig = {\r\n  licenseTypeId: 'default',\r\n  name: 'Standard License Application',\r\n  description: 'Standard license application process with all required steps',\r\n  steps: [\r\n    BASE_STEPS.applicantInfo,\r\n    BASE_STEPS.addressInfo,\r\n    BASE_STEPS.contactInfo,\r\n    BASE_STEPS.management,\r\n    BASE_STEPS.professionalServices,\r\n    BASE_STEPS.serviceScope,\r\n    BASE_STEPS.legalHistory,\r\n    BASE_STEPS.documents,\r\n    BASE_STEPS.submit\r\n  ],\r\n  estimatedTotalTime: '120 minutes',\r\n  requirements: [\r\n    'Business registration certificate',\r\n    'Tax compliance certificate',\r\n    'Financial statements',\r\n    'Management CVs',\r\n    'Professional qualifications',\r\n    'Service documentation'\r\n  ]\r\n};\r\n\r\n// Helper functions\r\nexport const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig => {\r\n  // Check if licenseTypeId is valid\r\n  if (!licenseTypeId || typeof licenseTypeId !== 'string') {\r\n    return DEFAULT_FALLBACK_CONFIG;\r\n  }\r\n\r\n  // First try direct lookup with exact match\r\n  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];\r\n  if (config) {\r\n    return config;\r\n  }\r\n\r\n  // Try normalized lookup (lowercase with underscores)\r\n  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');\r\n  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];\r\n  if (config) {\r\n    return config;\r\n  }\r\n\r\n  // Try name mapping for common variations\r\n  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];\r\n  if (mappedKey) {\r\n    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];\r\n  }\r\n\r\n  // If licenseTypeId looks like a UUID, try to get the code from license types\r\n  if (isUUID(licenseTypeId)) {\r\n    const code = getLicenseTypeCodeFromUUID(licenseTypeId);\r\n    if (code) {\r\n      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];\r\n      if (foundConfig) {\r\n        return foundConfig;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Try partial matching for known license type codes\r\n  const knownCodes = Object.keys(LICENSE_TYPE_STEP_CONFIGS);\r\n  const partialMatch = knownCodes.find(code =>\r\n    licenseTypeId.toLowerCase().includes(code) ||\r\n    code.includes(licenseTypeId.toLowerCase())\r\n  );\r\n\r\n  if (partialMatch) {\r\n    return LICENSE_TYPE_STEP_CONFIGS[partialMatch];\r\n  }\r\n  return DEFAULT_FALLBACK_CONFIG;\r\n};\r\n\r\n// Helper function to check if a string is a UUID\r\nconst isUUID = (str: string): boolean => {\r\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n  return uuidRegex.test(str);\r\n};\r\n\r\n// Helper function to get license type code from UUID\r\n// This will be populated by the license type service\r\nlet licenseTypeUUIDToCodeMap: Record<string, string> = {};\r\n\r\nexport const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {\r\n  licenseTypeUUIDToCodeMap = map;\r\n};\r\n\r\nconst getLicenseTypeCodeFromUUID = (uuid: string): string | null => {\r\n  return licenseTypeUUIDToCodeMap[uuid] || null;\r\n};\r\n\r\n// Optimized function to get steps by license type code\r\nexport const getStepsByLicenseTypeCode = (licenseTypeCode: string): StepConfig[] => {\r\n  // Validate known license type codes\r\n  const validCodes = ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n\r\n  if (validCodes.includes(licenseTypeCode)) {\r\n    const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];\r\n    if (config) {\r\n      return config.steps;\r\n    }\r\n  }\r\n  return DEFAULT_FALLBACK_CONFIG.steps;\r\n};\r\n\r\n// Enhanced function to check if a license type code is supported\r\nexport const isLicenseTypeCodeSupported = (licenseTypeCode: string): boolean => {\r\n  const validCodes = ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n  return validCodes.includes(licenseTypeCode);\r\n};\r\n\r\n// Function to get all supported license type codes\r\nexport const getSupportedLicenseTypeCodes = (): string[] => {\r\n  return ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];\r\n};\r\n\r\nexport const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  if (!config) return null;\r\n\r\n  return config.steps.find(step => step.route === stepRoute) || null;\r\n};\r\n\r\nexport const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  if (stepIndex < 0 || stepIndex >= config.steps.length) return null;\r\n\r\n  return config.steps[stepIndex];\r\n};\r\n\r\nexport const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.findIndex(step => step.route === stepRoute);\r\n};\r\n\r\nexport const getTotalSteps = (licenseTypeId: string): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.length;\r\n};\r\n\r\nexport const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.filter(step => step.required);\r\n};\r\n\r\nexport const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  return config.steps.filter(step => !step.required);\r\n};\r\n\r\nexport const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const totalSteps = config.steps.length;\r\n  const completed = completedSteps.length;\r\n\r\n  return Math.round((completed / totalSteps) * 100);\r\n};\r\n\r\nexport const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\r\n\r\n  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;\r\n\r\n  return config.steps[currentIndex + 1];\r\n};\r\n\r\nexport const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\r\n  const config = getLicenseTypeStepConfig(licenseTypeId);\r\n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\r\n\r\n  if (currentIndex <= 0) return null;\r\n\r\n  return config.steps[currentIndex - 1];\r\n};\r\n\r\n// Enhanced function to get step configuration with license type code validation\r\nexport const getOptimizedStepConfig = (licenseTypeCode: string): LicenseTypeStepConfig => {\r\n  // Check if it's a supported license type code\r\n  if (isLicenseTypeCodeSupported(licenseTypeCode)) {\r\n    const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];\r\n    return config;\r\n  }\r\n  return DEFAULT_FALLBACK_CONFIG;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;CAuBC;;;;;;;;;;;;;;;;;;AAqBD,mDAAmD;AACnD,MAAM,aAAyC;IAC7C,eAAe;QACb,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,sBAAsB;QACpB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IAEA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,4BAAmE;IAC9E,oBAAoB;QAClB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,iBAAiB;QACf,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sBAAsB;QACpB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,cAAc;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBAAqB;QACnB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,KAAK;QACH,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,WAAW;YACtB,WAAW,WAAW;YACtB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,SAAS;YACpB,WAAW,MAAM;SAClB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA,0CAA0C;AAC1C,MAAM,4BAAoD;IACxD,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,uBAAuB;IACvB,uBAAuB;IACvB,OAAO;IACP,gCAAgC;AAClC;AAEA,2DAA2D;AAC3D,MAAM,0BAAiD;IACrD,eAAe;IACf,MAAM;IACN,aAAa;IACb,OAAO;QACL,WAAW,aAAa;QACxB,WAAW,WAAW;QACtB,WAAW,WAAW;QACtB,WAAW,UAAU;QACrB,WAAW,oBAAoB;QAC/B,WAAW,YAAY;QACvB,WAAW,YAAY;QACvB,WAAW,SAAS;QACpB,WAAW,MAAM;KAClB;IACD,oBAAoB;IACpB,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,MAAM,2BAA2B,CAAC;IACvC,kCAAkC;IAClC,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;QACvD,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,SAAS,yBAAyB,CAAC,cAAc;IACrD,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,eAAe,cAAc,WAAW,GAAG,OAAO,CAAC,cAAc;IACvE,SAAS,yBAAyB,CAAC,aAAa;IAChD,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,yCAAyC;IACzC,MAAM,YAAY,yBAAyB,CAAC,aAAa;IACzD,IAAI,WAAW;QACb,OAAO,yBAAyB,CAAC,UAAU;IAC7C;IAEA,6EAA6E;IAC7E,IAAI,OAAO,gBAAgB;QACzB,MAAM,OAAO,2BAA2B;QACxC,IAAI,MAAM;YACR,MAAM,cAAc,yBAAyB,CAAC,KAAK;YACnD,IAAI,aAAa;gBACf,OAAO;YACT;QACF;IACF;IAEA,oDAAoD;IACpD,MAAM,aAAa,OAAO,IAAI,CAAC;IAC/B,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,OACnC,cAAc,WAAW,GAAG,QAAQ,CAAC,SACrC,KAAK,QAAQ,CAAC,cAAc,WAAW;IAGzC,IAAI,cAAc;QAChB,OAAO,yBAAyB,CAAC,aAAa;IAChD;IACA,OAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,SAAS,CAAC;IACd,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEA,qDAAqD;AACrD,qDAAqD;AACrD,IAAI,2BAAmD,CAAC;AAEjD,MAAM,8BAA8B,CAAC;IAC1C,2BAA2B;AAC7B;AAEA,MAAM,6BAA6B,CAAC;IAClC,OAAO,wBAAwB,CAAC,KAAK,IAAI;AAC3C;AAGO,MAAM,4BAA4B,CAAC;IACxC,oCAAoC;IACpC,MAAM,aAAa;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;IAE3H,IAAI,WAAW,QAAQ,CAAC,kBAAkB;QACxC,MAAM,SAAS,yBAAyB,CAAC,gBAAgB;QACzD,IAAI,QAAQ;YACV,OAAO,OAAO,KAAK;QACrB;IACF;IACA,OAAO,wBAAwB,KAAK;AACtC;AAGO,MAAM,6BAA6B,CAAC;IACzC,MAAM,aAAa;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;IAC3H,OAAO,WAAW,QAAQ,CAAC;AAC7B;AAGO,MAAM,+BAA+B;IAC1C,OAAO;QAAC;QAAsB;QAAmB;QAAwB;QAAgB;KAAsB;AACjH;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,cAAc;AAChE;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,YAAY,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO;IAE9D,OAAO,OAAO,KAAK,CAAC,UAAU;AAChC;AAEO,MAAM,eAAe,CAAC,eAAuB;IAClD,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AACvD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM;AAC5B;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAClD;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;AACnD;AAEO,MAAM,oBAAoB,CAAC,eAAuB;IACvD,MAAM,SAAS,yBAAyB;IACxC,MAAM,aAAa,OAAO,KAAK,CAAC,MAAM;IACtC,MAAM,YAAY,eAAe,MAAM;IAEvC,OAAO,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc;AAC/C;AAEO,MAAM,cAAc,CAAC,eAAuB;IACjD,MAAM,SAAS,yBAAyB;IACxC,MAAM,eAAe,aAAa,eAAe;IAEjD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO;IAE3E,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAEO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,MAAM,SAAS,yBAAyB;IACxC,MAAM,eAAe,aAAa,eAAe;IAEjD,IAAI,gBAAgB,GAAG,OAAO;IAE9B,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAGO,MAAM,yBAAyB,CAAC;IACrC,8CAA8C;IAC9C,IAAI,2BAA2B,kBAAkB;QAC/C,MAAM,SAAS,yBAAyB,CAAC,gBAAgB;QACzD,OAAO;IACT;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/EvaluationProgress.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useRouter, useSearchParams, usePathname } from 'next/navigation';\nimport { licenseCategoryService } from '@/services/licenseCategoryService';\nimport { \n  getLicenseTypeStepConfig,\n  isLicenseTypeCodeSupported,\n  getStepsByLicenseTypeCode,\n  type StepConfig \n} from '@/config/licenseTypeStepConfig';\n\n// Cache for license data\nconst licenseDataCache = new Map<string, {\n  data: any;\n  timestamp: number;\n}>();\n\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n\ninterface EvaluationProgressProps {\n  className?: string;\n}\n\nconst EvaluationProgress: React.FC<EvaluationProgressProps> = ({ className = '' }) => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const pathname = usePathname();\n\n  // State\n  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Get query parameters\n  const licenseCategoryId = searchParams.get('license_category_id');\n  const applicationId = searchParams.get('application_id');\n\n  // Debug query parameters\n  useEffect(() => {\n    console.log('🔍 EvaluationProgress Debug:', {\n      pathname,\n      licenseCategoryId,\n      applicationId,\n      allParams: Object.fromEntries(searchParams.entries())\n    });\n  }, [pathname, licenseCategoryId, applicationId, searchParams]);\n\n  // Get current step from pathname (memoized)\n  const currentStepIndex = useMemo(() => {\n    if (!applicationSteps.length) return -1;\n    const pathSegments = pathname.split('/');\n    const currentStepId = pathSegments[pathSegments.length - 1];\n    return applicationSteps.findIndex(step => step.id === currentStepId);\n  }, [pathname, applicationSteps]);\n\n  // Check cache for license data\n  const getCachedLicenseData = useCallback((licenseCategoryId: string) => {\n    const cached = licenseDataCache.get(licenseCategoryId);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n      return cached.data;\n    }\n    return null;\n  }, []);\n\n  // Cache license data\n  const cacheLicenseData = useCallback((licenseCategoryId: string, data: any) => {\n    licenseDataCache.set(licenseCategoryId, {\n      data,\n      timestamp: Date.now()\n    });\n  }, []);\n\n  // Load application steps\n  useEffect(() => {\n    const loadSteps = async () => {\n      // If no license_category_id, try to get it from application data\n      let resolvedLicenseCategoryId = licenseCategoryId;\n\n      if (!resolvedLicenseCategoryId && applicationId) {\n        try {\n          console.log('🔍 EvaluationProgress: Trying to load application to get license_category_id');\n          const { applicationService } = await import('@/services/applicationService');\n          const application = await applicationService.getApplication(applicationId);\n          resolvedLicenseCategoryId = application?.license_category_id;\n          console.log('🔍 EvaluationProgress: Got license_category_id from application:', resolvedLicenseCategoryId);\n        } catch (err) {\n          console.error('Error loading application for license_category_id:', err);\n        }\n      }\n\n      if (!resolvedLicenseCategoryId) {\n        setError('License category ID is required');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Check cache first\n        let licenseCategory = getCachedLicenseData(resolvedLicenseCategoryId);\n\n        if (!licenseCategory) {\n          // Load license category from API\n          licenseCategory = await licenseCategoryService.getLicenseCategory(resolvedLicenseCategoryId);\n\n          if (!licenseCategory) {\n            throw new Error('License category not found');\n          }\n\n          // Cache the data\n          cacheLicenseData(resolvedLicenseCategoryId, licenseCategory);\n        }\n\n        // Debug license category data\n        console.log('🔍 EvaluationProgress License Category Debug:', {\n          licenseCategory,\n          license_type: licenseCategory.license_type,\n          license_type_code: licenseCategory.license_type?.code,\n          category_code: licenseCategory.code\n        });\n\n        // Get license type code with fallback to URL path\n        let licenseTypeCode = licenseCategory.license_type?.code || licenseCategory.code;\n\n        // Fallback: extract license type from URL path\n        if (!licenseTypeCode) {\n          const pathSegments = pathname.split('/');\n          const licenseTypeIndex = pathSegments.findIndex(segment => segment === 'applications') + 1;\n          const urlLicenseType = pathSegments[licenseTypeIndex];\n\n          console.log('🔄 Using license type from URL as fallback:', urlLicenseType);\n          licenseTypeCode = urlLicenseType;\n        }\n\n        if (!licenseTypeCode) {\n          console.error('❌ License type code not found:', {\n            licenseCategory,\n            license_type: licenseCategory.license_type,\n            available_fields: Object.keys(licenseCategory),\n            pathname,\n            pathSegments: pathname.split('/')\n          });\n          throw new Error('License type code not found');\n        }\n\n        console.log('✅ Using license type code:', licenseTypeCode);\n\n        // Load steps based on license type\n        let steps: StepConfig[];\n        if (isLicenseTypeCodeSupported(licenseTypeCode)) {\n          steps = getStepsByLicenseTypeCode(licenseTypeCode);\n        } else {\n          const config = getLicenseTypeStepConfig(licenseTypeCode);\n          steps = config.steps;\n        }\n\n        setApplicationSteps(steps);\n      } catch (err: any) {\n        console.error('Error loading application steps:', err);\n        setError(err.message || 'Failed to load application steps');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadSteps();\n  }, [licenseCategoryId, getCachedLicenseData, cacheLicenseData]);\n\n  // Navigation handlers for evaluation\n  const handleStepClick = (stepIndex: number) => {\n    // Prevent navigation to future steps if not editing an existing application\n    if (!applicationId && stepIndex > currentStepIndex) {\n      return;\n    }\n\n    const step = applicationSteps[stepIndex];\n    \n    // Extract license type from current pathname\n    const pathSegments = pathname.split('/');\n    const licenseTypeIndex = pathSegments.findIndex(segment => segment === 'applications') + 1;\n    const licenseType = pathSegments[licenseTypeIndex];\n\n    const params = new URLSearchParams();\n    // Use the resolved license category ID or the original one\n    const categoryId = licenseCategoryId || searchParams.get('license_category_id');\n    if (categoryId) {\n      params.set('license_category_id', categoryId);\n    }\n    if (applicationId) {\n      params.set('application_id', applicationId);\n    }\n    \n    // Navigate to evaluation URL instead of apply URL\n    router.push(`/applications/${licenseType}/evaluate/${step.id}?${params.toString()}`);\n  };\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"></div>\n          <div className=\"space-y-3\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full\"></div>\n                <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-700 p-6 ${className}`}>\n        <div className=\"text-center\">\n          <i className=\"ri-error-warning-line text-2xl text-red-500 mb-2\"></i>\n          <p className=\"text-sm text-red-600 dark:text-red-400\">{error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  // No steps available\n  if (!applicationSteps.length) {\n    return (\n      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>\n        <div className=\"text-center\">\n          <i className=\"ri-list-check text-2xl text-gray-400 mb-2\"></i>\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">No evaluation steps available</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>\n      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4\">\n        Evaluation Progress\n      </h3>\n      \n      <div className=\"space-y-3\" style={{ maxHeight: 'calc(100vh - 17rem)', overflowY: 'auto'}}>\n        {applicationSteps.map((step, index) => {\n          const isCompleted = index < currentStepIndex;\n          const isCurrent = index === currentStepIndex;\n          const isClickable = applicationId || index <= currentStepIndex;\n\n          return (\n            <div\n              key={step.id}\n              className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${\n                isClickable \n                  ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700' \n                  : 'cursor-not-allowed opacity-50'\n              } ${\n                isCurrent \n                  ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' \n                  : ''\n              }`}\n              onClick={() => isClickable && handleStepClick(index)}\n            >\n              {/* Step indicator */}\n              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                isCompleted\n                  ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'\n                  : isCurrent\n                  ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'\n                  : 'bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-400'\n              }`}>\n                {isCompleted ? (\n                  <i className=\"ri-check-line\"></i>\n                ) : (\n                  index + 1\n                )}\n              </div>\n\n              {/* Step info */}\n              <div className=\"flex-1 min-w-0\">\n                <p className={`text-sm font-medium truncate ${\n                  isCurrent \n                    ? 'text-blue-900 dark:text-blue-100' \n                    : 'text-gray-900 dark:text-gray-100'\n                }`}>\n                  {step.name}\n                </p>\n                <p className={`text-xs truncate ${\n                  isCurrent \n                    ? 'text-blue-600 dark:text-blue-400' \n                    : 'text-gray-500 dark:text-gray-400'\n                }`}>\n                  {step.description}\n                </p>\n              </div>\n\n              {/* Status indicator */}\n              {isCurrent && (\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\n                </div>\n              )}\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Progress summary */}\n      <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"flex items-center justify-between text-sm\">\n          <span className=\"text-gray-500 dark:text-gray-400\">\n            Step {Math.max(currentStepIndex + 1, 1)} of {applicationSteps.length}\n          </span>\n          <span className=\"text-gray-500 dark:text-gray-400\">\n            {Math.round(((currentStepIndex + 1) / applicationSteps.length) * 100)}% Complete\n          </span>\n        </div>\n        <div className=\"mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n          <div \n            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n            style={{ width: `${((currentStepIndex + 1) / applicationSteps.length) * 100}%` }}\n          ></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EvaluationProgress;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYA,yBAAyB;AACzB,MAAM,mBAAmB,IAAI;AAK7B,MAAM,iBAAiB,IAAI,KAAK,MAAM,YAAY;AAMlD,MAAM,qBAAwD,CAAC,EAAE,YAAY,EAAE,EAAE;;IAC/E,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,QAAQ;IACR,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,QAAQ,GAAG,CAAC,gCAAgC;gBAC1C;gBACA;gBACA;gBACA,WAAW,OAAO,WAAW,CAAC,aAAa,OAAO;YACpD;QACF;uCAAG;QAAC;QAAU;QAAmB;QAAe;KAAa;IAE7D,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wDAAE;YAC/B,IAAI,CAAC,iBAAiB,MAAM,EAAE,OAAO,CAAC;YACtC,MAAM,eAAe,SAAS,KAAK,CAAC;YACpC,MAAM,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;YAC3D,OAAO,iBAAiB,SAAS;gEAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;QACxD;uDAAG;QAAC;QAAU;KAAiB;IAE/B,+BAA+B;IAC/B,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE,CAAC;YACxC,MAAM,SAAS,iBAAiB,GAAG,CAAC;YACpC,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;gBAC5D,OAAO,OAAO,IAAI;YACpB;YACA,OAAO;QACT;+DAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC,mBAA2B;YAC/D,iBAAiB,GAAG,CAAC,mBAAmB;gBACtC;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;2DAAG,EAAE;IAEL,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;0DAAY;oBAChB,iEAAiE;oBACjE,IAAI,4BAA4B;oBAEhC,IAAI,CAAC,6BAA6B,eAAe;wBAC/C,IAAI;4BACF,QAAQ,GAAG,CAAC;4BACZ,MAAM,EAAE,kBAAkB,EAAE,GAAG;4BAC/B,MAAM,cAAc,MAAM,mBAAmB,cAAc,CAAC;4BAC5D,4BAA4B,aAAa;4BACzC,QAAQ,GAAG,CAAC,oEAAoE;wBAClF,EAAE,OAAO,KAAK;4BACZ,QAAQ,KAAK,CAAC,sDAAsD;wBACtE;oBACF;oBAEA,IAAI,CAAC,2BAA2B;wBAC9B,SAAS;wBACT,WAAW;wBACX;oBACF;oBAEA,IAAI;wBACF,WAAW;wBACX,SAAS;wBAET,oBAAoB;wBACpB,IAAI,kBAAkB,qBAAqB;wBAE3C,IAAI,CAAC,iBAAiB;4BACpB,iCAAiC;4BACjC,kBAAkB,MAAM,4IAAA,CAAA,yBAAsB,CAAC,kBAAkB,CAAC;4BAElE,IAAI,CAAC,iBAAiB;gCACpB,MAAM,IAAI,MAAM;4BAClB;4BAEA,iBAAiB;4BACjB,iBAAiB,2BAA2B;wBAC9C;wBAEA,8BAA8B;wBAC9B,QAAQ,GAAG,CAAC,iDAAiD;4BAC3D;4BACA,cAAc,gBAAgB,YAAY;4BAC1C,mBAAmB,gBAAgB,YAAY,EAAE;4BACjD,eAAe,gBAAgB,IAAI;wBACrC;wBAEA,kDAAkD;wBAClD,IAAI,kBAAkB,gBAAgB,YAAY,EAAE,QAAQ,gBAAgB,IAAI;wBAEhF,+CAA+C;wBAC/C,IAAI,CAAC,iBAAiB;4BACpB,MAAM,eAAe,SAAS,KAAK,CAAC;4BACpC,MAAM,mBAAmB,aAAa,SAAS;0EAAC,CAAA,UAAW,YAAY;2EAAkB;4BACzF,MAAM,iBAAiB,YAAY,CAAC,iBAAiB;4BAErD,QAAQ,GAAG,CAAC,+CAA+C;4BAC3D,kBAAkB;wBACpB;wBAEA,IAAI,CAAC,iBAAiB;4BACpB,QAAQ,KAAK,CAAC,kCAAkC;gCAC9C;gCACA,cAAc,gBAAgB,YAAY;gCAC1C,kBAAkB,OAAO,IAAI,CAAC;gCAC9B;gCACA,cAAc,SAAS,KAAK,CAAC;4BAC/B;4BACA,MAAM,IAAI,MAAM;wBAClB;wBAEA,QAAQ,GAAG,CAAC,8BAA8B;wBAE1C,mCAAmC;wBACnC,IAAI;wBACJ,IAAI,CAAA,GAAA,yIAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;4BAC/C,QAAQ,CAAA,GAAA,yIAAA,CAAA,4BAAyB,AAAD,EAAE;wBACpC,OAAO;4BACL,MAAM,SAAS,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;4BACxC,QAAQ,OAAO,KAAK;wBACtB;wBAEA,oBAAoB;oBACtB,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,SAAS,IAAI,OAAO,IAAI;oBAC1B,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;uCAAG;QAAC;QAAmB;QAAsB;KAAiB;IAE9D,qCAAqC;IACrC,MAAM,kBAAkB,CAAC;QACvB,4EAA4E;QAC5E,IAAI,CAAC,iBAAiB,YAAY,kBAAkB;YAClD;QACF;QAEA,MAAM,OAAO,gBAAgB,CAAC,UAAU;QAExC,6CAA6C;QAC7C,MAAM,eAAe,SAAS,KAAK,CAAC;QACpC,MAAM,mBAAmB,aAAa,SAAS,CAAC,CAAA,UAAW,YAAY,kBAAkB;QACzF,MAAM,cAAc,YAAY,CAAC,iBAAiB;QAElD,MAAM,SAAS,IAAI;QACnB,2DAA2D;QAC3D,MAAM,aAAa,qBAAqB,aAAa,GAAG,CAAC;QACzD,IAAI,YAAY;YACd,OAAO,GAAG,CAAC,uBAAuB;QACpC;QACA,IAAI,eAAe;YACjB,OAAO,GAAG,CAAC,kBAAkB;QAC/B;QAEA,kDAAkD;QAClD,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IACrF;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAC,+FAA+F,EAAE,WAAW;sBAC3H,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;;;;;;;;;;;;IAStB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAW,CAAC,6FAA6F,EAAE,WAAW;sBACzH,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;;;;;;IAI/D;IAEA,qBAAqB;IACrB,IAAI,CAAC,iBAAiB,MAAM,EAAE;QAC5B,qBACE,6LAAC;YAAI,WAAW,CAAC,+FAA+F,EAAE,WAAW;sBAC3H,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBAAE,WAAU;kCAA2C;;;;;;;;;;;;;;;;;IAIhE;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,+FAA+F,EAAE,WAAW;;0BAC3H,6LAAC;gBAAG,WAAU;0BAA8D;;;;;;0BAI5E,6LAAC;gBAAI,WAAU;gBAAY,OAAO;oBAAE,WAAW;oBAAuB,WAAW;gBAAM;0BACpF,iBAAiB,GAAG,CAAC,CAAC,MAAM;oBAC3B,MAAM,cAAc,QAAQ;oBAC5B,MAAM,YAAY,UAAU;oBAC5B,MAAM,cAAc,iBAAiB,SAAS;oBAE9C,qBACE,6LAAC;wBAEC,WAAW,CAAC,6DAA6D,EACvE,cACI,2DACA,gCACL,CAAC,EACA,YACI,+EACA,IACJ;wBACF,SAAS,IAAM,eAAe,gBAAgB;;0CAG9C,6LAAC;gCAAI,WAAW,CAAC,wFAAwF,EACvG,cACI,sEACA,YACA,kEACA,iEACJ;0CACC,4BACC,6LAAC;oCAAE,WAAU;;;;;2CAEb,QAAQ;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAW,CAAC,6BAA6B,EAC1C,YACI,qCACA,oCACJ;kDACC,KAAK,IAAI;;;;;;kDAEZ,6LAAC;wCAAE,WAAW,CAAC,iBAAiB,EAC9B,YACI,qCACA,oCACJ;kDACC,KAAK,WAAW;;;;;;;;;;;;4BAKpB,2BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;uBAhDd,KAAK,EAAE;;;;;gBAqDlB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCAAmC;oCAC3C,KAAK,GAAG,CAAC,mBAAmB,GAAG;oCAAG;oCAAK,iBAAiB,MAAM;;;;;;;0CAEtE,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,AAAC,CAAC,mBAAmB,CAAC,IAAI,iBAAiB,MAAM,GAAI;oCAAK;;;;;;;;;;;;;kCAG1E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,AAAC,CAAC,mBAAmB,CAAC,IAAI,iBAAiB,MAAM,GAAI,IAAI,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAM3F;GAnTM;;QACW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QACnB,qIAAA,CAAA,cAAW;;;KAHxB;uCAqTS", "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/evaluation/EvaluationLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Suspense } from 'react';\nimport EvaluationProgress from './EvaluationProgress';\n\ninterface EvaluationLayoutProps {\n  children: React.ReactNode;\n  applicationId?: string;\n  licenseTypeCode?: string;\n  currentStepRoute?: string;\n  onSubmit?: () => void;\n  onSave?: () => void;\n  onNext?: () => void;\n  onPrevious?: () => void;\n  isSubmitting?: boolean;\n  isSaving?: boolean;\n  showNextButton?: boolean;\n  showPreviousButton?: boolean;\n  showSaveButton?: boolean;\n  showSubmitButton?: boolean;\n  nextButtonText?: string;\n  previousButtonText?: string;\n  saveButtonText?: string;\n  submitButtonText?: string;\n  nextButtonDisabled?: boolean;\n  previousButtonDisabled?: boolean;\n  saveButtonDisabled?: boolean;\n  submitButtonDisabled?: boolean;\n  className?: string;\n  showProgress?: boolean;\n  progressFallback?: React.ReactNode;\n  stepValidationErrors?: string[];\n  showStepInfo?: boolean;\n}\n\n// Progress loading fallback\nconst ProgressLoadingFallback: React.FC = () => (\n  <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n    <div className=\"animate-pulse\">\n      <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"></div>\n      <div className=\"space-y-3\">\n        {[...Array(5)].map((_, i) => (\n          <div key={i} className=\"flex items-center space-x-3\">\n            <div className=\"h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full\"></div>\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\n          </div>\n        ))}\n      </div>\n    </div>\n  </div>\n);\n\nconst EvaluationLayout: React.FC<EvaluationLayoutProps> = ({\n  children,\n  applicationId,\n  licenseTypeCode,\n  currentStepRoute,\n  onSubmit,\n  onSave,\n  onNext,\n  onPrevious,\n  isSubmitting = false,\n  isSaving = false,\n  showNextButton = true,\n  showPreviousButton = true,\n  showSaveButton = false,\n  showSubmitButton = false,\n  nextButtonText = 'Continue',\n  previousButtonText = 'Back',\n  saveButtonText = 'Save',\n  submitButtonText = 'Submit',\n  nextButtonDisabled = false,\n  previousButtonDisabled = false,\n  saveButtonDisabled = false,\n  submitButtonDisabled = false,\n  className = '',\n  showProgress = true,\n  progressFallback,\n  stepValidationErrors = [],\n  showStepInfo = true\n}) => {\n  return (\n    <div className={`min-h-screen bg-gray-50 overflow-y-auto  dark:bg-gray-900 ${className}`}>\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8 p-6  mb-20\">\n          {/* Progress Steps - Left Sidebar */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"sticky top-8\">\n              <Suspense fallback={<ProgressLoadingFallback />}>\n                <EvaluationProgress />\n              </Suspense>\n            </div>\n          </div>\n\n          {/* Main Content Area */}\n          <div className=\"lg:col-span-3\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n              {/* Step Information Banner */}\n              {showStepInfo && licenseTypeCode && currentStepRoute && (\n                <div className=\"border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">\n                        License Type: {licenseTypeCode.replace(/_/g, ' ').toUpperCase()}\n                      </h3>\n                      <p className=\"text-xs text-blue-700 dark:text-blue-300 mt-1\">\n                        Current Step: {currentStepRoute.replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                      </p>\n                    </div>\n                    {stepValidationErrors.length > 0 && (\n                      <div className=\"flex items-center text-red-600 dark:text-red-400\">\n                        <i className=\"ri-error-warning-line mr-1\"></i>\n                        <span className=\"text-xs\">{stepValidationErrors.length} validation error{stepValidationErrors.length !== 1 ? 's' : ''}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Main Content */}\n              <div className=\"p-6\">\n                {children}\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"border-t border-gray-200 dark:border-gray-700 px-6 py-4 bg-gray-50 dark:bg-gray-900/50 rounded-b-lg\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex space-x-3\">\n                    {showPreviousButton && (\n                      <button\n                        type=\"button\"\n                        onClick={onPrevious}\n                        disabled={previousButtonDisabled}\n                        className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        <i className=\"ri-arrow-left-line mr-2\"></i>\n                        {previousButtonText}\n                      </button>\n                    )}\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    {showSaveButton && (\n                      <button\n                        type=\"button\"\n                        onClick={onSave}\n                        disabled={saveButtonDisabled || isSaving}\n                        className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        {isSaving ? (\n                          <>\n                            <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                            Saving...\n                          </>\n                        ) : (\n                          <>\n                            <i className=\"ri-save-line mr-2\"></i>\n                            {saveButtonText}\n                          </>\n                        )}\n                      </button>\n                    )}\n\n                    {showSubmitButton && (\n                      <button\n                        type=\"button\"\n                        onClick={onSubmit}\n                        disabled={submitButtonDisabled || isSubmitting}\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        {isSubmitting ? (\n                          <>\n                            <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                            </svg>\n                            Submitting...\n                          </>\n                        ) : (\n                          <>\n                            <i className=\"ri-send-plane-line mr-2\"></i>\n                            {submitButtonText}\n                          </>\n                        )}\n                      </button>\n                    )}\n\n                    {showNextButton && (\n                      <button\n                        type=\"button\"\n                        onClick={onNext}\n                        disabled={nextButtonDisabled}\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        {nextButtonText}\n                        <i className=\"ri-arrow-right-line ml-2\"></i>\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n    </div>\n  );\n};\n\nexport default EvaluationLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAmCA,4BAA4B;AAC5B,MAAM,0BAAoC,kBACxC,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;2BAFP;;;;;;;;;;;;;;;;;;;;;KANd;AAgBN,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,eAAe,KAAK,EACpB,WAAW,KAAK,EAChB,iBAAiB,IAAI,EACrB,qBAAqB,IAAI,EACzB,iBAAiB,KAAK,EACtB,mBAAmB,KAAK,EACxB,iBAAiB,UAAU,EAC3B,qBAAqB,MAAM,EAC3B,iBAAiB,MAAM,EACvB,mBAAmB,QAAQ,EAC3B,qBAAqB,KAAK,EAC1B,yBAAyB,KAAK,EAC9B,qBAAqB,KAAK,EAC1B,uBAAuB,KAAK,EAC5B,YAAY,EAAE,EACd,eAAe,IAAI,EACnB,gBAAgB,EAChB,uBAAuB,EAAE,EACzB,eAAe,IAAI,EACpB;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,0DAA0D,EAAE,WAAW;kBACpF,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6JAAA,CAAA,WAAQ;4BAAC,wBAAU,6LAAC;;;;;sCACnB,cAAA,6LAAC,yJAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;;;;;8BAMzB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BAEZ,gBAAgB,mBAAmB,kCAClC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;;wDAAuD;wDACpD,gBAAgB,OAAO,CAAC,MAAM,KAAK,WAAW;;;;;;;8DAE/D,6LAAC;oDAAE,WAAU;;wDAAgD;wDAC5C,iBAAiB,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;;;;;;;;wCAGzF,qBAAqB,MAAM,GAAG,mBAC7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;;;;;8DACb,6LAAC;oDAAK,WAAU;;wDAAW,qBAAqB,MAAM;wDAAC;wDAAkB,qBAAqB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7H,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,oCACC,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,6LAAC;wDAAE,WAAU;;;;;;oDACZ;;;;;;;;;;;;sDAKP,6LAAC;4CAAI,WAAU;;gDACZ,gCACC,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,sBAAsB;oDAChC,WAAU;8DAET,yBACC;;0EACE,6LAAC;gEAAI,WAAU;gEAAgD,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;;kFACpH,6LAAC;wEAAO,WAAU;wEAAa,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;;;;;;kFACxF,6LAAC;wEAAK,WAAU;wEAAa,MAAK;wEAAe,GAAE;;;;;;;;;;;;4DAC/C;;qFAIR;;0EACE,6LAAC;gEAAE,WAAU;;;;;;4DACZ;;;;;;;;gDAMR,kCACC,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,wBAAwB;oDAClC,WAAU;8DAET,6BACC;;0EACE,6LAAC;gEAAI,WAAU;gEAA6C,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;;kFACjH,6LAAC;wEAAO,WAAU;wEAAa,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;;;;;;kFACxF,6LAAC;wEAAK,WAAU;wEAAa,MAAK;wEAAe,GAAE;;;;;;;;;;;;4DAC/C;;qFAIR;;0EACE,6LAAC;gEAAE,WAAU;;;;;;4DACZ;;;;;;;;gDAMR,gCACC,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;wDAET;sEACD,6LAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrC;MA5JM;uCA8JS", "debugId": null}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/customer-api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport { processApiResponse } from './authUtils';\r\n\r\n// API Configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n// Create axios instance for customer portal (same as staff portal)\r\nconst customerApiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 120000, // Increased timeout to match main API client (120 seconds)\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Create auth-specific client (same as staff portal)\r\nconst customerAuthApiClient: AxiosInstance = axios.create({\r\n  baseURL: `${API_BASE_URL}/auth`,\r\n  timeout: 120000, // Increased timeout to match main API client\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Add debug logging to auth client (only in development)\r\ncustomerAuthApiClient.interceptors.request.use(\r\n  (config) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Request:', {\r\n        url: `${config.baseURL}${config.url}`,\r\n        method: config.method,\r\n        headers: config.headers,\r\n        data: config.data\r\n      });\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('Customer Auth API Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\ncustomerAuthApiClient.interceptors.response.use(\r\n  (response) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Response Success:', {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        url: response.config.url\r\n      });\r\n    }\r\n    return response;\r\n  },\r\n  (error) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error('Customer Auth API Interceptor Error:', {\r\n        message: error?.message || 'Unknown error',\r\n        code: error?.code || 'NO_CODE',\r\n        status: error?.response?.status || 'NO_STATUS',\r\n        statusText: error?.response?.statusText || 'NO_STATUS_TEXT',\r\n        url: error?.config?.url || 'NO_URL',\r\n        method: error?.config?.method || 'NO_METHOD',\r\n        baseURL: error?.config?.baseURL || 'NO_BASE_URL',\r\n        isAxiosError: error?.isAxiosError || false,\r\n        responseData: error?.response?.data || 'NO_RESPONSE_DATA',\r\n        requestData: error?.config?.data || 'NO_REQUEST_DATA',\r\n        headers: error?.config?.headers || 'NO_HEADERS'\r\n      });\r\n    }\r\n\r\n    // Don't handle 401 here, let the login method handle it\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Request interceptor to add auth token\r\ncustomerApiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get('auth_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for error handling with retry logic\r\ncustomerApiClient.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    return response;\r\n  },\r\n  async (error: AxiosError) => {\r\n    const originalRequest = error.config as AxiosError['config'] & {\r\n      _retry?: boolean;\r\n      _retryCount?: number;\r\n    };\r\n\r\n    // Handle 429 Rate Limiting\r\n    if (error.response?.status === 429) {\r\n      if (!originalRequest._retry) {\r\n        originalRequest._retry = true;\r\n        \r\n        // Get retry delay from headers or use exponential backoff\r\n        const retryAfter = error.response.headers['retry-after'];\r\n        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n        \r\n        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n        \r\n        // Don't retry more than 3 times\r\n        if (originalRequest._retryCount <= 3) {\r\n          console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n          \r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return customerApiClient(originalRequest);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle 401 Unauthorized\r\n    if (error.response?.status === 401) {\r\n      // Clear auth token and redirect to login\r\n      Cookies.remove('auth_token');\r\n      Cookies.remove('auth_user');\r\n      window.location.href = '/auth/login';\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// API Service Class\r\nexport class CustomerApiService {\r\n  public api: AxiosInstance;\r\n  private pendingRequests: Map<string, Promise<unknown>> = new Map();\r\n\r\n  constructor() {\r\n    this.api = customerApiClient;\r\n  }\r\n\r\n  // Request deduplication helper\r\n  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {\r\n    if (this.pendingRequests.has(key)) {\r\n      return this.pendingRequests.get(key) as Promise<T>;\r\n    }\r\n\r\n    const promise = requestFn().finally(() => {\r\n      this.pendingRequests.delete(key);\r\n    });\r\n\r\n    this.pendingRequests.set(key, promise);\r\n    return promise;\r\n  }\r\n\r\n  // Set auth token\r\n  setAuthToken(token: string) {\r\n    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  // Remove auth token\r\n  removeAuthToken() {\r\n    delete this.api.defaults.headers.common['Authorization'];\r\n  }\r\n\r\n  async logout() {\r\n    const response = await customerAuthApiClient.post('/logout');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async refreshToken() {\r\n    const response = await customerAuthApiClient.post('/refresh');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // 2FA endpoints\r\n  async generateTwoFactorCode(userId: string, action: string) {\r\n    const response = await customerAuthApiClient.post('/generate-2fa', { user_id: userId, action });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async verify2FA(data: { user_id: string; code: string; unique: string }) {\r\n    const response = await customerAuthApiClient.post('/verify-2fa', data);\r\n\r\n    // Handle response structure consistently with login\r\n    if (processApiResponse(response)?.data) {\r\n      const authData = processApiResponse(response).data;\r\n      \r\n      // Map backend field names to frontend expected format\r\n      const mappedAuthData = {\r\n        access_token: authData.access_token,\r\n        user: {\r\n          id: authData.user.user_id,\r\n          firstName: authData.user.first_name,\r\n          lastName: authData.user.last_name,\r\n          email: authData.user.email,\r\n          roles: authData.user.roles || [],\r\n          isAdmin: (authData.user.roles || []).includes('administrator'),\r\n          profileImage: authData.user.profile_image,\r\n          createdAt: authData.user.created_at || new Date().toISOString(),\r\n          lastLogin: authData.user.last_login,\r\n          organizationName: authData.user.organization_name,\r\n          two_factor_enabled: authData.user.two_factor_enabled\r\n        }\r\n      };\r\n      \r\n      return mappedAuthData;\r\n    }\r\n\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async setupTwoFactorAuth(data: { access_token: string; user_id: string }) {\r\n    const response = await customerAuthApiClient.post('/setup-2fa', data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // User profile endpoints\r\n  async getProfile() {\r\n    return this.deduplicateRequest('getProfile', async () => {\r\n      const response = await this.api.get('/users/profile');\r\n      return processApiResponse(response);\r\n    });\r\n  }\r\n\r\n  async updateProfile(profileData: ProfileUpdateData) {\r\n    const response = await this.api.put('/users/profile', profileData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deactivateAccount(deactivationData: DeactivateAccountData) {\r\n    const response = await this.api.post('/users/deactivate', deactivationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Addressing endpoints\r\n  async getAddresses() {\r\n    const response = await this.api.get('/address/all');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createAddress(addressData: CreateAddressData) {\r\n    const response = await this.api.post('/address/create', addressData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddress(id: string) {\r\n    const response = await this.api.get(`/address/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async editAddress(addressData: EditAddressData) {\r\n    const { address_id, ...updateData } = addressData;\r\n    if (!address_id) {\r\n      throw new Error('Address ID is required for updating');\r\n    }\r\n    const response = await this.api.put(`/address/${address_id}`, updateData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddressesByEntity(entityType: string, entityId: string) {\r\n    const response = await this.api.get(`/address/all?entity_type=${encodeURIComponent(entityType)}&entity_id=${encodeURIComponent(entityId)}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deleteAddress(id: string) {\r\n    const response = await this.api.delete(`/address/soft/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async searchPostcodes(searchParams: SearchPostcodes) {\r\n    const response = await this.api.post('/postal-codes/search', searchParams);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License endpoints\r\n  async getLicenses(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/licenses', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicense(id: string) {\r\n    const response = await this.api.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createLicenseApplication(applicationData: LicenseApplicationData) {\r\n    const response = await this.api.post('/license-applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Types endpoints\r\n  async getLicenseTypes(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-types', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseType(id: string) {\r\n    const response = await this.api.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Categories endpoints\r\n  async getLicenseCategories(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-categories', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoriesByType(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoryTree(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategory(id: string) {\r\n    const response = await this.api.get(`/license-categories/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Application endpoints\r\n  async getApplications(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/applications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getApplication(id: string) {\r\n    const response = await this.api.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createApplication(applicationData: any) {\r\n    const response = await this.api.post('/applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async updateApplication(id: string, applicationData: Partial<LicenseApplicationData>) {\r\n    const response = await this.api.put(`/applications/${id}`, applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Payment endpoints\r\n  async getPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/payments', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPayment(id: string) {\r\n    const response = await this.api.get(`/payments/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createPayment(paymentData: PaymentCreateData) {\r\n    const response = await this.api.post('/payments', paymentData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadProofOfPayment(paymentId: string, formData: FormData) {\r\n    const response = await this.api.post(`/payments/${paymentId}/proof-of-payment`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPaymentStatistics() {\r\n    const response = await this.api.get('/payments/statistics');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Document endpoints\r\n  async getDocuments(params?: { type?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/documents', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadDocument(formData: FormData) {\r\n    const response = await this.api.post('/documents/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadDocument(id: string) {\r\n    const response = await this.api.get(`/documents/${id}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Dashboard statistics\r\n  async getDashboardStats() {\r\n    const response = await this.api.get('/dashboard/stats');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Notifications\r\n  async getNotifications(params?: { read?: boolean; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/notifications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async markNotificationAsRead(id: string) {\r\n    const response = await this.api.patch(`/notifications/${id}/read`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Procurement endpoints\r\n  async getTenders(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/tenders', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getTender(id: string) {\r\n    const response = await this.api.get(`/procurement/tenders/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async payForTenderAccess(tenderId: string, paymentData: TenderPaymentData) {\r\n    const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadTenderDocument(documentId: string) {\r\n    const response = await this.api.get(`/procurement/documents/${documentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getMyBids(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/my-bids', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getBid(id: string) {\r\n    const response = await this.api.get(`/procurement/bids/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitBid(formData: FormData) {\r\n    const response = await this.api.post('/procurement/bids', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateBid(id: string, formData: FormData) {\r\n    const response = await this.api.put(`/procurement/bids/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/payments', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getProcurementPayment(id: string) {\r\n    const response = await this.api.get(`/procurement/payments/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  // Consumer Affairs endpoints\r\n  async getComplaints(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/consumer-affairs/complaints', { params });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async getComplaint(id: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${id}`);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async submitComplaint(complaintData: ComplaintData) {\r\n    const formData = new FormData();\r\n    formData.append('title', complaintData.title);\r\n    formData.append('description', complaintData.description);\r\n    formData.append('category', complaintData.category);\r\n\r\n    if (complaintData.attachments) {\r\n      complaintData.attachments.forEach((file, index) => {\r\n        formData.append(`attachments[${index}]`, file);\r\n      });\r\n    }\r\n\r\n    const response = await this.api.post('/consumer-affairs/complaints', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async updateComplaint(id: string, updates: Partial<ComplaintData>) {\r\n    const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n\r\n  async downloadComplaintAttachment(complaintId: string, attachmentId: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(processApiResponse(response));\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const customerApi = new CustomerApiService();\r\n\r\n// Export axios instance for direct use if needed\r\nexport { customerApiClient };\r\n\r\n// Export types\r\nexport interface ApiResponse<T = unknown> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  errors?: string[] | { [key: string]: string[] } | string;\r\n}\r\n\r\nexport interface PaginatedResponse<T = unknown> {\r\n  data: T[];\r\n  total: number;\r\n  page: number;\r\n  limit: number;\r\n  totalPages: number;\r\n}\r\n\r\nexport interface License {\r\n  id: string;\r\n  licenseNumber: string;\r\n  type: string;\r\n  status: 'active' | 'expired' | 'suspended' | 'pending';\r\n  issueDate: string;\r\n  expirationDate: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Application {\r\n  id: string;\r\n  applicationNumber: string;\r\n  type: string;\r\n  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';\r\n  submittedDate: string;\r\n  lastUpdated: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Payment {\r\n  id: string;\r\n  invoiceNumber: string;\r\n  amount: number;\r\n  currency: string;\r\n  status: 'pending' | 'paid' | 'overdue' | 'cancelled';\r\n  dueDate: string;\r\n  paidDate?: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface User {\r\n  user_id: string;\r\n  email: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone?: string;\r\n  status?: string;\r\n  profile_image?: string;\r\n  roles: string[];\r\n  isAdmin: boolean;\r\n  isCustomer?: boolean;\r\n  organizationName?: string;\r\n  createdAt?: string;\r\n  lastLogin?: string;\r\n  address?: string;\r\n  city?: string;\r\n  country?: string;\r\n  two_factor_enabled?: boolean;\r\n}\r\n\r\nexport interface ProfileUpdateData {\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n  organizationName?: string;\r\n  profileImage?: string;\r\n}\r\n\r\nexport interface DeactivateAccountData {\r\n  reason: string;\r\n  feedback?: string;\r\n  user_id?: string;\r\n}\r\n\r\nexport interface CreateAddressData {\r\n  address_type: string;\r\n  entity_type?: string;\r\n  entity_id?:string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface EditAddressData {\r\n  address_id: string;\r\n  address_type?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\n\r\nexport interface LicenseApplicationData {\r\n  type: string;\r\n  organizationName: string;\r\n  description?: string;\r\n  contactEmail?: string;\r\n  contactPhone?: string;\r\n  businessAddress?: string;\r\n  businessType?: string;\r\n  requestedStartDate?: string;\r\n  additionalDocuments?: string[];\r\n  notes?: string;\r\n}\r\n\r\nexport interface PaymentCreateData {\r\n  amount: number;\r\n  currency: string;\r\n  dueDate: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface TenderPaymentData {\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethod: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface ComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  attachments?: File[];\r\n}\r\n"], "names": [], "mappings": ";;;;;AAKqB;AALrB;AACA;AACA;;;;AAEA,oBAAoB;AACpB,MAAM,eAAe,6DAAmC;AAExD,mEAAmE;AACnE,MAAM,oBAAmC,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACpD,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,qDAAqD;AACrD,MAAM,wBAAuC,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxD,SAAS,GAAG,aAAa,KAAK,CAAC;IAC/B,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,sBAAsB,YAAY,CAAC,OAAO,CAAC,GAAG,CAC5C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,8BAA8B;YACxC,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;YACrC,QAAQ,OAAO,MAAM;YACrB,SAAS,OAAO,OAAO;YACvB,MAAM,OAAO,IAAI;QACnB;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,oCAAoC;IAClD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,sBAAsB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC7C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,uCAAuC;YACjD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;QAC1B;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,wCAAwC;YACpD,SAAS,OAAO,WAAW;YAC3B,MAAM,OAAO,QAAQ;YACrB,QAAQ,OAAO,UAAU,UAAU;YACnC,YAAY,OAAO,UAAU,cAAc;YAC3C,KAAK,OAAO,QAAQ,OAAO;YAC3B,QAAQ,OAAO,QAAQ,UAAU;YACjC,SAAS,OAAO,QAAQ,WAAW;YACnC,cAAc,OAAO,gBAAgB;YACrC,cAAc,OAAO,UAAU,QAAQ;YACvC,aAAa,OAAO,QAAQ,QAAQ;YACpC,SAAS,OAAO,QAAQ,WAAW;QACrC;IACF;IAEA,wDAAwD;IACxD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wCAAwC;AACxC,kBAAkB,YAAY,CAAC,OAAO,CAAC,GAAG,CACxC,CAAC;IACC,MAAM,QAAQ,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,2DAA2D;AAC3D,kBAAkB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzC,CAAC;IACC,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAKpC,2BAA2B;IAC3B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B,gBAAgB,MAAM,GAAG;YAEzB,0DAA0D;YAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;YACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;YAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;YAEnE,gCAAgC;YAChC,IAAI,gBAAgB,WAAW,IAAI,GAAG;gBACpC,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;gBAE/F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,kBAAkB;YAC3B;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,yCAAyC;QACzC,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM;IACJ,IAAmB;IAClB,kBAAiD,IAAI,MAAM;IAEnE,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,+BAA+B;IAC/B,MAAc,mBAAsB,GAAW,EAAE,SAA2B,EAAc;QACxF,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAClC;QAEA,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC9B;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;IACT;IAEA,iBAAiB;IACjB,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvE;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAC1D;IAEA,MAAM,SAAS;QACb,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAE;QAC1D,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,IAAuD,EAAE;QACvE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,eAAe;QAEjE,oDAAoD;QACpD,IAAI,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,MAAM;YACtC,MAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAElD,sDAAsD;YACtD,MAAM,iBAAiB;gBACrB,cAAc,SAAS,YAAY;gBACnC,MAAM;oBACJ,IAAI,SAAS,IAAI,CAAC,OAAO;oBACzB,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,UAAU,SAAS,IAAI,CAAC,SAAS;oBACjC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,SAAS,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC;oBAC9C,cAAc,SAAS,IAAI,CAAC,aAAa;oBACzC,WAAW,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC7D,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,kBAAkB,SAAS,IAAI,CAAC,iBAAiB;oBACjD,oBAAoB,SAAS,IAAI,CAAC,kBAAkB;gBACtD;YACF;YAEA,OAAO;QACT;QAEA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,IAA+C,EAAE;QACxE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,cAAc;QAChE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc;YAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACpC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B;IACF;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;QACtD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,gBAAuC,EAAE;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;QACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QACpD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,YAAY,WAA4B,EAAE;QAC9C,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;QACtC,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE;QAC9D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAqB,UAAkB,EAAE,QAAgB,EAAE;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,mBAAmB,YAAY,WAAW,EAAE,mBAAmB,WAAW;QAC1I,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,YAA6B,EAAE;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,yBAAyB,eAAuC,EAAE;QACtE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB;QAC9D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAA0C,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,MAA0C,EAAE;QACrE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB;YAAE;QAAO;QACpE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,2BAA2B,aAAqB,EAAE;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC1F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,aAAqB,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;QAC5F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,EAAU,EAAE;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAC/D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,MAA2D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;YAAE;QAAO;QAC9D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,eAAoB,EAAE;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;QACtD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,EAAU,EAAE,eAAgD,EAAE;QACpF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAClD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAqB,SAAiB,EAAE,QAAkB,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,iBAAiB,CAAC,EAAE,UAAU;YACxF,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAyD,EAAE;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc;YAAE;QAAO;QAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,QAAkB,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,iBAAiB,EAAU,EAAE;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,EAAE;YAC/D,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,MAA0D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,EAAU,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;QACjE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,WAAW,MAA8E,EAAE;QAC/F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,EAAU,EAAE;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QAChE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,mBAAmB,QAAgB,EAAE,WAA8B,EAAE;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,WAAW,CAAC,EAAE;QACpF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,UAAkB,EAAE;QAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,SAAS,CAAC,EAAE;YACnF,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,MAA2D,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,OAAO,EAAU,EAAE;QACvB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,QAAkB,EAAE;QAClC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,UAAU,EAAU,EAAE,QAAkB,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE,UAAU;YACvE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,uBAAuB,MAA2D,EAAE;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB;YAAE;QAAO;QACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,sBAAsB,EAAU,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI;QACjE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,6BAA6B;IAC7B,MAAM,cAAc,MAA8E,EAAE;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gCAAgC;YAAE;QAAO;QAC7E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,aAA4B,EAAE;QAClD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS,cAAc,KAAK;QAC5C,SAAS,MAAM,CAAC,eAAe,cAAc,WAAW;QACxD,SAAS,MAAM,CAAC,YAAY,cAAc,QAAQ;QAElD,IAAI,cAAc,WAAW,EAAE;YAC7B,cAAc,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBACvC,SAAS,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE;YAC3C;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gCAAgC,UAAU;YAC7E,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,gBAAgB,EAAU,EAAE,OAA+B,EAAE;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;IAEA,MAAM,4BAA4B,WAAmB,EAAE,YAAoB,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,aAAa,SAAS,CAAC,EAAE;YACtH,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/C;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 2054, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useDynamicNavigation.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { \r\n  getStepsByLicenseTypeCode,\r\n  isLicenseTypeCodeSupported,\r\n  getLicenseTypeStepConfig,\r\n  StepConfig \r\n} from '@/config/licenseTypeStepConfig';\r\nimport { CustomerApiService } from '@/lib/customer-api';\r\n\r\ninterface NavigationParams {\r\n  licenseCategoryId: string;\r\n  applicationId?: string;\r\n}\r\n\r\ninterface UseDynamicNavigationProps {\r\n  currentStepRoute: string;\r\n  licenseCategoryId: string | null;\r\n  applicationId: string | null;\r\n}\r\n\r\ninterface UseDynamicNavigationReturn {\r\n  // Navigation functions\r\n  handleNext: (saveFunction?: () => Promise<boolean>) => Promise<void>;\r\n  handlePrevious: () => void;\r\n  navigateToStep: (stepRoute: string) => void;\r\n  \r\n  // Step information\r\n  currentStep: StepConfig | null;\r\n  nextStep: StepConfig | null;\r\n  previousStep: StepConfig | null;\r\n  currentStepIndex: number;\r\n  totalSteps: number;\r\n  \r\n  // State\r\n  loading: boolean;\r\n  error: string | null;\r\n  licenseTypeCode: string | null;\r\n  \r\n  // Utility functions\r\n  isFirstStep: boolean;\r\n  isLastStep: boolean;\r\n  canNavigateNext: boolean;\r\n  canNavigatePrevious: boolean;\r\n}\r\n\r\nexport const useDynamicNavigation = ({\r\n  currentStepRoute,\r\n  licenseCategoryId,\r\n  applicationId\r\n}: UseDynamicNavigationProps): UseDynamicNavigationReturn => {\r\n  const router = useRouter();\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [licenseTypeCode, setLicenseTypeCode] = useState<string | null>(null);\r\n  const [steps, setSteps] = useState<StepConfig[]>([]);\r\n\r\n  // Create customer API service instance\r\n  const customerApi = useMemo(() => new CustomerApiService(), []);\r\n\r\n  // Load license type and steps\r\n  const loadLicenseTypeSteps = useCallback(async () => {\r\n    if (!licenseCategoryId) {\r\n      setError('License category ID is required');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      console.log('🧭 Loading license type for navigation:', licenseCategoryId);\r\n\r\n      // Get license category and type\r\n      const category = await customerApi.getLicenseCategory(licenseCategoryId);\r\n      if (!category?.license_type_id) {\r\n        throw new Error('License category does not have a license type ID');\r\n      }\r\n\r\n      // Add small delay to prevent rate limiting\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      const licenseType = await customerApi.getLicenseType(category.license_type_id);\r\n      if (!licenseType) {\r\n        throw new Error('License type not found');\r\n      }\r\n\r\n      const typeCode = licenseType.code || licenseType.license_type_id;\r\n      setLicenseTypeCode(typeCode);\r\n\r\n      // Get steps based on license type code\r\n      let licenseSteps: StepConfig[] = [];\r\n      \r\n      if (isLicenseTypeCodeSupported(typeCode)) {\r\n        console.log('✅ Using optimized steps for supported license type:', typeCode);\r\n        licenseSteps = getStepsByLicenseTypeCode(typeCode);\r\n      } else {\r\n        console.log('⚠️ Using fallback steps for license type:', typeCode);\r\n        const config = getLicenseTypeStepConfig(typeCode);\r\n        licenseSteps = config.steps;\r\n      }\r\n\r\n      setSteps(licenseSteps);\r\n      console.log('🧭 Loaded steps for navigation:', licenseSteps.map(s => s.route));\r\n\r\n    } catch (err: any) {\r\n      console.error('Error loading license type steps:', err);\r\n      setError(err.message || 'Failed to load navigation configuration');\r\n      \r\n      // Use default fallback steps\r\n      const fallbackConfig = getLicenseTypeStepConfig('default');\r\n      setSteps(fallbackConfig.steps);\r\n      setLicenseTypeCode('default');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [licenseCategoryId, customerApi]);\r\n\r\n  // Load steps when dependencies change\r\n  useEffect(() => {\r\n    loadLicenseTypeSteps();\r\n  }, [loadLicenseTypeSteps]);\r\n\r\n  // Computed values\r\n  const currentStepIndex = useMemo(() => {\r\n    return steps.findIndex(step => step.route === currentStepRoute);\r\n  }, [steps, currentStepRoute]);\r\n\r\n  const currentStep = useMemo(() => {\r\n    return steps[currentStepIndex] || null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const nextStep = useMemo(() => {\r\n    return currentStepIndex >= 0 && currentStepIndex < steps.length - 1 \r\n      ? steps[currentStepIndex + 1] \r\n      : null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const previousStep = useMemo(() => {\r\n    return currentStepIndex > 0 \r\n      ? steps[currentStepIndex - 1] \r\n      : null;\r\n  }, [steps, currentStepIndex]);\r\n\r\n  const totalSteps = steps.length;\r\n  const isFirstStep = currentStepIndex === 0;\r\n  const isLastStep = currentStepIndex === steps.length - 1;\r\n  const canNavigateNext = !isLastStep && nextStep !== null;\r\n  const canNavigatePrevious = !isFirstStep && previousStep !== null;\r\n\r\n  // Navigation functions\r\n  const createNavigationUrl = useCallback((stepRoute: string) => {\r\n    const params = new URLSearchParams();\r\n    params.set('license_category_id', licenseCategoryId || '');\r\n    \r\n    if (applicationId) {\r\n      params.set('application_id', applicationId);\r\n    }\r\n    \r\n    return `/customer/applications/apply/${stepRoute}?${params.toString()}`;\r\n  }, [licenseCategoryId, applicationId]);\r\n\r\n  const navigateToStep = useCallback((stepRoute: string) => {\r\n    const url = createNavigationUrl(stepRoute);\r\n    console.log('🧭 Navigating to step:', stepRoute, 'URL:', url);\r\n    router.push(url);\r\n  }, [createNavigationUrl, router]);\r\n\r\n  const handleNext = useCallback(async (saveFunction?: () => Promise<boolean>) => {\r\n    if (!canNavigateNext || !nextStep) {\r\n      console.warn('⚠️ Cannot navigate to next step');\r\n      return;\r\n    }\r\n\r\n    // If save function is provided, save first\r\n    if (saveFunction) {\r\n      console.log('💾 Saving current step before navigation...');\r\n      try {\r\n        const saved = await saveFunction();\r\n        if (!saved) {\r\n          console.warn('⚠️ Save failed, not navigating');\r\n          return;\r\n        }\r\n      } catch (error: any) {\r\n        console.error('❌ Error during save operation:', error);\r\n\r\n        // Handle specific error types\r\n        if (error.message?.includes('timeout')) {\r\n          console.error('Save operation timed out');\r\n        } else if (error.message?.includes('Bad Request')) {\r\n          console.error('Invalid data provided for save operation');\r\n        } else if (error.message?.includes('Too many requests')) {\r\n          console.error('Rate limit exceeded, please wait and try again');\r\n        }\r\n\r\n        // Don't navigate if save failed\r\n        return;\r\n      }\r\n    }\r\n\r\n    navigateToStep(nextStep.route);\r\n  }, [canNavigateNext, nextStep, navigateToStep]);\r\n\r\n  const handlePrevious = useCallback(() => {\r\n    if (!canNavigatePrevious || !previousStep) {\r\n      console.warn('⚠️ Cannot navigate to previous step');\r\n      return;\r\n    }\r\n\r\n    navigateToStep(previousStep.route);\r\n  }, [canNavigatePrevious, previousStep, navigateToStep]);\r\n\r\n  return {\r\n    // Navigation functions\r\n    handleNext,\r\n    handlePrevious,\r\n    navigateToStep,\r\n    \r\n    // Step information\r\n    currentStep,\r\n    nextStep,\r\n    previousStep,\r\n    currentStepIndex,\r\n    totalSteps,\r\n    \r\n    // State\r\n    loading,\r\n    error,\r\n    licenseTypeCode,\r\n    \r\n    // Utility functions\r\n    isFirstStep,\r\n    isLastStep,\r\n    canNavigateNext,\r\n    canNavigatePrevious,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAMA;;AAVA;;;;;AAgDO,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACa;;IAC1B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAEnD,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE,IAAM,IAAI,gIAAA,CAAA,qBAAkB;oDAAI,EAAE;IAE9D,8BAA8B;IAC9B,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE;YACvC,IAAI,CAAC,mBAAmB;gBACtB,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,QAAQ,GAAG,CAAC,2CAA2C;gBAEvD,gCAAgC;gBAChC,MAAM,WAAW,MAAM,YAAY,kBAAkB,CAAC;gBACtD,IAAI,CAAC,UAAU,iBAAiB;oBAC9B,MAAM,IAAI,MAAM;gBAClB;gBAEA,2CAA2C;gBAC3C,MAAM,IAAI;8EAAQ,CAAA,UAAW,WAAW,SAAS;;gBAEjD,MAAM,cAAc,MAAM,YAAY,cAAc,CAAC,SAAS,eAAe;gBAC7E,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,WAAW,YAAY,IAAI,IAAI,YAAY,eAAe;gBAChE,mBAAmB;gBAEnB,uCAAuC;gBACvC,IAAI,eAA6B,EAAE;gBAEnC,IAAI,CAAA,GAAA,yIAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;oBACxC,QAAQ,GAAG,CAAC,uDAAuD;oBACnE,eAAe,CAAA,GAAA,yIAAA,CAAA,4BAAyB,AAAD,EAAE;gBAC3C,OAAO;oBACL,QAAQ,GAAG,CAAC,6CAA6C;oBACzD,MAAM,SAAS,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;oBACxC,eAAe,OAAO,KAAK;gBAC7B;gBAEA,SAAS;gBACT,QAAQ,GAAG,CAAC,mCAAmC,aAAa,GAAG;8EAAC,CAAA,IAAK,EAAE,KAAK;;YAE9E,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,SAAS,IAAI,OAAO,IAAI;gBAExB,6BAA6B;gBAC7B,MAAM,iBAAiB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;gBAChD,SAAS,eAAe,KAAK;gBAC7B,mBAAmB;YACrB,SAAU;gBACR,WAAW;YACb;QACF;iEAAG;QAAC;QAAmB;KAAY;IAEnC,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;QACF;yCAAG;QAAC;KAAqB;IAEzB,kBAAkB;IAClB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE;YAC/B,OAAO,MAAM,SAAS;kEAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;;QAChD;yDAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAC1B,OAAO,KAAK,CAAC,iBAAiB,IAAI;QACpC;oDAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YACvB,OAAO,oBAAoB,KAAK,mBAAmB,MAAM,MAAM,GAAG,IAC9D,KAAK,CAAC,mBAAmB,EAAE,GAC3B;QACN;iDAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE;YAC3B,OAAO,mBAAmB,IACtB,KAAK,CAAC,mBAAmB,EAAE,GAC3B;QACN;qDAAG;QAAC;QAAO;KAAiB;IAE5B,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,cAAc,qBAAqB;IACzC,MAAM,aAAa,qBAAqB,MAAM,MAAM,GAAG;IACvD,MAAM,kBAAkB,CAAC,cAAc,aAAa;IACpD,MAAM,sBAAsB,CAAC,eAAe,iBAAiB;IAE7D,uBAAuB;IACvB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YACvC,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,uBAAuB,qBAAqB;YAEvD,IAAI,eAAe;gBACjB,OAAO,GAAG,CAAC,kBAAkB;YAC/B;YAEA,OAAO,CAAC,6BAA6B,EAAE,UAAU,CAAC,EAAE,OAAO,QAAQ,IAAI;QACzE;gEAAG;QAAC;QAAmB;KAAc;IAErC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YAClC,MAAM,MAAM,oBAAoB;YAChC,QAAQ,GAAG,CAAC,0BAA0B,WAAW,QAAQ;YACzD,OAAO,IAAI,CAAC;QACd;2DAAG;QAAC;QAAqB;KAAO;IAEhC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO;YACpC,IAAI,CAAC,mBAAmB,CAAC,UAAU;gBACjC,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,2CAA2C;YAC3C,IAAI,cAAc;gBAChB,QAAQ,GAAG,CAAC;gBACZ,IAAI;oBACF,MAAM,QAAQ,MAAM;oBACpB,IAAI,CAAC,OAAO;wBACV,QAAQ,IAAI,CAAC;wBACb;oBACF;gBACF,EAAE,OAAO,OAAY;oBACnB,QAAQ,KAAK,CAAC,kCAAkC;oBAEhD,8BAA8B;oBAC9B,IAAI,MAAM,OAAO,EAAE,SAAS,YAAY;wBACtC,QAAQ,KAAK,CAAC;oBAChB,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,gBAAgB;wBACjD,QAAQ,KAAK,CAAC;oBAChB,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,sBAAsB;wBACvD,QAAQ,KAAK,CAAC;oBAChB;oBAEA,gCAAgC;oBAChC;gBACF;YACF;YAEA,eAAe,SAAS,KAAK;QAC/B;uDAAG;QAAC;QAAiB;QAAU;KAAe;IAE9C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACjC,IAAI,CAAC,uBAAuB,CAAC,cAAc;gBACzC,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,eAAe,aAAa,KAAK;QACnC;2DAAG;QAAC;QAAqB;QAAc;KAAe;IAEtD,OAAO;QACL,uBAAuB;QACvB;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QACA;QACA;QAEA,QAAQ;QACR;QACA;QACA;QAEA,oBAAoB;QACpB;QACA;QACA;QACA;IACF;AACF;GA/La;;QAKI,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 2291, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: string): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application with improved error handling\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    try {\r\n      console.log('Updating application:', id, 'with data:', data);\r\n      const response = await apiClient.put(`/applications/${id}`, data, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('Error updating application:', error);\r\n\r\n      // Handle specific error cases\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n\r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid application data';\r\n        console.error('400 Bad Request details:', error.response?.data);\r\n        throw new Error(`Bad Request: ${message}`);\r\n      }\r\n\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      // Estimate progress based on section name\r\n      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n      const sectionIndex = sectionOrder.indexOf(sectionName);\r\n      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications (filtered by authenticated user)\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      // Use dedicated endpoint that explicitly filters by current user\r\n      const response = await apiClient.get('/applications/user-applications');\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Handle paginated response structure\r\n      let applications = [];\r\n      if (processedResponse?.data) {\r\n        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];\r\n      } else if (Array.isArray(processedResponse)) {\r\n        applications = processedResponse;\r\n      } else if (processedResponse) {\r\n        // Single application or other structure\r\n        applications = [processedResponse];\r\n      }\r\n\r\n      return applications;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get data from entity-specific APIs for validation\r\n      let formData: Record<string, any> = {};\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application status\r\n  async updateStatus(applicationId: string, status: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/status`, { status });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating application status:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Assign application to an officer\r\n  async assignApplication(applicationId: string, assignedTo: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/assign`, { assignedTo });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error assigning application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAc;QAC1C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAc;QACtD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,IAAI,cAAc;YACvD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE,MAAM;gBAChE,SAAS;YACX;YACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,8BAA8B;YAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACjD,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE;gBAC1D,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,SAAS;YAC3C;YAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,0CAA0C;YAC1C,MAAM,eAAe;gBAAC;gBAAiB;gBAAkB;gBAAgB;gBAAgB;gBAAgB;gBAAgB;aAAe;YACxI,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAE3D,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,2DAA2D;IAC3D,MAAM;QACJ,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,sCAAsC;YACtC,IAAI,eAAe,EAAE;YACrB,IAAI,mBAAmB,MAAM;gBAC3B,eAAe,MAAM,OAAO,CAAC,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE;YACpF,OAAO,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBAC3C,eAAe;YACjB,OAAO,IAAI,mBAAmB;gBAC5B,wCAAwC;gBACxC,eAAe;oBAAC;iBAAkB;YACpC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,oDAAoD;YACpD,IAAI,WAAgC,CAAC;YAErC,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,cAAa,aAAqB,EAAE,MAAc;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;gBAAE;YAAO;YACzF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,mCAAmC;IACnC,MAAM,mBAAkB,aAAqB,EAAE,UAAkB;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,cAAc,OAAO,CAAC,EAAE;gBAAE;YAAW;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/evaluationService.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\nexport interface EvaluationCriteria {\r\n  criteria_id?: string;\r\n  category: string;\r\n  subcategory: string;\r\n  score: number;\r\n  weight: number;\r\n  max_marks?: number;\r\n  awarded_marks?: number;\r\n}\r\n\r\nexport interface Evaluation {\r\n  evaluation_id: string;\r\n  application_id: string;\r\n  evaluator_id: string;\r\n  evaluation_type: string;\r\n  status: 'draft' | 'completed' | 'approved' | 'rejected';\r\n  total_score: number;\r\n  recommendation: 'approve' | 'conditional_approve' | 'reject';\r\n  evaluators_notes?: string;\r\n  shareholding_compliance?: boolean;\r\n  completed_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  application?: any;\r\n  evaluator?: any;\r\n  criteria?: EvaluationCriteria[];\r\n}\r\n\r\nexport interface CreateEvaluationData {\r\n  application_id: string;\r\n  evaluator_id: string;\r\n  evaluation_type: string;\r\n  total_score: number;\r\n  recommendation: 'approve' | 'conditional_approve' | 'reject';\r\n  evaluators_notes?: string;\r\n  shareholding_compliance?: boolean;\r\n  criteria?: Omit<EvaluationCriteria, 'criteria_id'>[];\r\n}\r\n\r\nexport interface UpdateEvaluationData extends Partial<CreateEvaluationData> {\r\n  status?: 'draft' | 'completed' | 'approved' | 'rejected';\r\n}\r\n\r\nexport interface EvaluationStats {\r\n  total: number;\r\n  draft: number;\r\n  completed: number;\r\n  approved: number;\r\n  rejected: number;\r\n  averageScore: number;\r\n}\r\n\r\nexport const evaluationService = {\r\n  // Get all evaluations with pagination\r\n  async getEvaluations(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n  }) {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n\r\n    const response = await apiClient.get(`/evaluations?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get evaluation by ID\r\n  async getEvaluation(id: string): Promise<Evaluation> {\r\n    const response = await apiClient.get(`/evaluations/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get evaluation by application ID\r\n  async getEvaluationByApplication(applicationId: string): Promise<Evaluation | null> {\r\n    try {\r\n      const response = await apiClient.get(`/evaluations/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get evaluation criteria\r\n  async getEvaluationCriteria(evaluationId: string): Promise<EvaluationCriteria[]> {\r\n    const response = await apiClient.get(`/evaluations/${evaluationId}/criteria`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new evaluation\r\n  async createEvaluation(data: CreateEvaluationData): Promise<Evaluation> {\r\n    const response = await apiClient.post('/evaluations', data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update evaluation\r\n  async updateEvaluation(id: string, data: UpdateEvaluationData): Promise<Evaluation> {\r\n    const response = await apiClient.patch(`/evaluations/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete evaluation\r\n  async deleteEvaluation(id: string): Promise<void> {\r\n    await apiClient.delete(`/evaluations/${id}`);\r\n  },\r\n\r\n  // Get evaluation statistics\r\n  async getEvaluationStats(): Promise<EvaluationStats> {\r\n    const response = await apiClient.get('/evaluations/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Submit evaluation (mark as completed)\r\n  async submitEvaluation(id: string, data: {\r\n    total_score: number;\r\n    recommendation: 'approve' | 'conditional_approve' | 'reject';\r\n    evaluators_notes?: string;\r\n    criteria?: Omit<EvaluationCriteria, 'criteria_id'>[];\r\n  }): Promise<Evaluation> {\r\n    return this.updateEvaluation(id, {\r\n      ...data,\r\n      status: 'completed',\r\n    });\r\n  },\r\n\r\n  // Calculate total score from criteria\r\n  calculateTotalScore(criteria: EvaluationCriteria[]): number {\r\n    if (!criteria || criteria.length === 0) return 0;\r\n    \r\n    const weightedSum = criteria.reduce((sum, criterion) => {\r\n      return sum + (criterion.score * criterion.weight);\r\n    }, 0);\r\n    \r\n    const totalWeight = criteria.reduce((sum, criterion) => sum + criterion.weight, 0);\r\n    \r\n    return totalWeight > 0 ? (weightedSum / totalWeight) : 0;\r\n  },\r\n\r\n  // Get evaluation template based on license type\r\n  getEvaluationTemplate(licenseType: string): EvaluationCriteria[] {\r\n    const templates: Record<string, EvaluationCriteria[]> = {\r\n      postal_service: [\r\n        { category: 'financial_capacity', subcategory: 'financial_documents', score: 0, weight: 0.15, max_marks: 15 },\r\n        { category: 'financial_capacity', subcategory: 'capital_adequacy', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'financial_capacity', subcategory: 'financial_projections', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'financial_capacity', subcategory: 'credit_worthiness', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'business_plan', subcategory: 'market_analysis', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'business_plan', subcategory: 'business_model', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'business_plan', subcategory: 'revenue_projections', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'business_plan', subcategory: 'growth_strategy', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'technical_expertise', subcategory: 'technical_capacity', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'technical_expertise', subcategory: 'operational_plan', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'technical_expertise', subcategory: 'implementation_timeline', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'organizational_structure', subcategory: 'management_structure', score: 0, weight: 0.05, max_marks: 5 },\r\n      ],\r\n      telecommunications: [\r\n        { category: 'financial_capacity', subcategory: 'financial_documents', score: 0, weight: 0.20, max_marks: 20 },\r\n        { category: 'financial_capacity', subcategory: 'capital_adequacy', score: 0, weight: 0.15, max_marks: 15 },\r\n        { category: 'technical_expertise', subcategory: 'network_design', score: 0, weight: 0.20, max_marks: 20 },\r\n        { category: 'technical_expertise', subcategory: 'technical_capacity', score: 0, weight: 0.15, max_marks: 15 },\r\n        { category: 'business_plan', subcategory: 'market_analysis', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'business_plan', subcategory: 'business_model', score: 0, weight: 0.10, max_marks: 10 },\r\n        { category: 'organizational_structure', subcategory: 'management_structure', score: 0, weight: 0.05, max_marks: 5 },\r\n        { category: 'organizational_structure', subcategory: 'compliance_framework', score: 0, weight: 0.05, max_marks: 5 },\r\n      ],\r\n    };\r\n\r\n    return templates[licenseType] || templates.postal_service;\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAyDO,MAAM,oBAAoB;IAC/B,sCAAsC;IACtC,MAAM,gBAAe,MAMpB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,QAAQ,IAAI;QAC7E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,eAAc,EAAU;QAC5B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,4BAA2B,aAAqB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,eAAe;YAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,YAAoB;QAC9C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,aAAa,SAAS,CAAC;QAC5E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,kBAAiB,IAA0B;QAC/C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gBAAgB;QACtD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,kBAAiB,EAAU,EAAE,IAA0B;QAC3D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,oBAAoB;IACpB,MAAM,kBAAiB,EAAU;QAC/B,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;IAC7C;IAEA,4BAA4B;IAC5B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wCAAwC;IACxC,MAAM,kBAAiB,EAAU,EAAE,IAKlC;QACC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC/B,GAAG,IAAI;YACP,QAAQ;QACV;IACF;IAEA,sCAAsC;IACtC,qBAAoB,QAA8B;QAChD,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;QAE/C,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK;YACxC,OAAO,MAAO,UAAU,KAAK,GAAG,UAAU,MAAM;QAClD,GAAG;QAEH,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,YAAc,MAAM,UAAU,MAAM,EAAE;QAEhF,OAAO,cAAc,IAAK,cAAc,cAAe;IACzD;IAEA,gDAAgD;IAChD,uBAAsB,WAAmB;QACvC,MAAM,YAAkD;YACtD,gBAAgB;gBACd;oBAAE,UAAU;oBAAsB,aAAa;oBAAuB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC5G;oBAAE,UAAU;oBAAsB,aAAa;oBAAoB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACzG;oBAAE,UAAU;oBAAsB,aAAa;oBAAyB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC9G;oBAAE,UAAU;oBAAsB,aAAa;oBAAqB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBACzG;oBAAE,UAAU;oBAAiB,aAAa;oBAAmB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACnG;oBAAE,UAAU;oBAAiB,aAAa;oBAAkB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAClG;oBAAE,UAAU;oBAAiB,aAAa;oBAAuB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBACtG;oBAAE,UAAU;oBAAiB,aAAa;oBAAmB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBAClG;oBAAE,UAAU;oBAAuB,aAAa;oBAAsB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC5G;oBAAE,UAAU;oBAAuB,aAAa;oBAAoB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC1G;oBAAE,UAAU;oBAAuB,aAAa;oBAA2B,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBAChH;oBAAE,UAAU;oBAA4B,aAAa;oBAAwB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;aACnH;YACD,oBAAoB;gBAClB;oBAAE,UAAU;oBAAsB,aAAa;oBAAuB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC5G;oBAAE,UAAU;oBAAsB,aAAa;oBAAoB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACzG;oBAAE,UAAU;oBAAuB,aAAa;oBAAkB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACxG;oBAAE,UAAU;oBAAuB,aAAa;oBAAsB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAC5G;oBAAE,UAAU;oBAAiB,aAAa;oBAAmB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBACnG;oBAAE,UAAU;oBAAiB,aAAa;oBAAkB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAG;gBAClG;oBAAE,UAAU;oBAA4B,aAAa;oBAAwB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;gBAClH;oBAAE,UAAU;oBAA4B,aAAa;oBAAwB,OAAO;oBAAG,QAAQ;oBAAM,WAAW;gBAAE;aACnH;QACH;QAEA,OAAO,SAAS,CAAC,YAAY,IAAI,UAAU,cAAc;IAC3D;AACF", "debugId": null}}, {"offset": {"line": 2819, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["export interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LicenseCategory {\r\n  license_category_id: string;\r\n  name: string;\r\n  description?: string;\r\n  license_type_id: string;\r\n  license_type?: LicenseType;\r\n  parent_id?: string;\r\n  parent?: LicenseCategory;\r\n  children?: LicenseCategory[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: ApplicationStatus;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: {\r\n    applicant_id: string;\r\n    name: string;\r\n    business_registration_number: string;\r\n    tpin: string;\r\n    website: string;\r\n    email: string;\r\n    phone: string;\r\n    fax?: string;\r\n    level_of_insurance_cover?: string;\r\n    date_incorporation: string;\r\n    place_incorporation: string;\r\n    created_at: string;\r\n    updated_at: string;\r\n  };\r\n  license_category?: {\r\n    license_category_id: string;\r\n    name: string;\r\n    license_type?: {\r\n      license_type_id: string;\r\n      name: string;\r\n      code: string;\r\n    };\r\n  };\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    filter: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first: string;\r\n    previous: string;\r\n    current: string;\r\n    next: string;\r\n    last: string;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAsFO,IAAA,AAAK,2CAAA;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 2841, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/applications/%5Blicense-type%5D/evaluate/submit/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, use } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport EvaluationLayout from '@/components/evaluation/EvaluationLayout';\r\n\r\nimport { useDynamicNavigation } from '@/hooks/useDynamicNavigation';\r\nimport { applicationService } from '@/services/applicationService';\r\nimport { evaluationService, type Evaluation, type EvaluationCriteria } from '@/services/evaluationService';\r\nimport { ApplicationStatus } from '@/types/license';\r\n\r\n\r\n\r\ninterface EvaluateSubmitPageProps {\r\n  params: Promise<{\r\n    'license-type': string;\r\n  }>;\r\n}\r\n\r\nconst EvaluateSubmitPage: React.FC<EvaluateSubmitPageProps> = ({ params }) => {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const { isAuthenticated, loading: authLoading, user } = useAuth();\r\n\r\n  // Unwrap params using React.use()\r\n  const resolvedParams = use(params);\r\n  const licenseType = resolvedParams['license-type'];\r\n  const applicationId = searchParams.get('application_id');\r\n\r\n  // State\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [application, setApplication] = useState<any>(null);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);\r\n\r\n  // Final evaluation state\r\n  const [evaluation, setEvaluation] = useState<Evaluation | null>(null);\r\n  const [evaluationComment, setEvaluationComment] = useState('');\r\n  const [finalRecommendation, setFinalRecommendation] = useState<'approve' | 'conditional_approve' | 'reject' | ''>('');\r\n  const [totalScore, setTotalScore] = useState<number>(0);\r\n  const [evaluationCriteria, setEvaluationCriteria] = useState<EvaluationCriteria[]>([\r\n    { category: 'Technical Capability', subcategory: 'Infrastructure', score: 0, weight: 0.3, max_marks: 100, awarded_marks: 0 },\r\n    { category: 'Financial Capability', subcategory: 'Capital Requirements', score: 0, weight: 0.25, max_marks: 100, awarded_marks: 0 },\r\n    { category: 'Legal Compliance', subcategory: 'Regulatory Requirements', score: 0, weight: 0.2, max_marks: 100, awarded_marks: 0 },\r\n    { category: 'Experience', subcategory: 'Industry Experience', score: 0, weight: 0.15, max_marks: 100, awarded_marks: 0 },\r\n    { category: 'Documentation', subcategory: 'Completeness', score: 0, weight: 0.1, max_marks: 100, awarded_marks: 0 }\r\n  ]);\r\n  const [shareholdingCompliance, setShareholdingCompliance] = useState<boolean>(true);\r\n\r\n  // Dynamic navigation hook - same as apply pages\r\n  const {\r\n    previousStep,\r\n  } = useDynamicNavigation({\r\n    currentStepRoute: 'submit',\r\n    licenseCategoryId,\r\n    applicationId\r\n  });\r\n\r\n  // Calculate total score from criteria\r\n  const calculateTotalScore = (criteria: EvaluationCriteria[]): number => {\r\n    return evaluationService.calculateTotalScore(criteria);\r\n  };\r\n\r\n  // Update criteria score and recalculate total\r\n  const updateCriteriaScore = (index: number, awardedMarks: number) => {\r\n    const updatedCriteria = [...evaluationCriteria];\r\n    updatedCriteria[index].awarded_marks = awardedMarks;\r\n    updatedCriteria[index].score = (awardedMarks / (updatedCriteria[index].max_marks || 100)) * 100;\r\n    setEvaluationCriteria(updatedCriteria);\r\n    setTotalScore(calculateTotalScore(updatedCriteria));\r\n  };\r\n\r\n  // Load application data and existing evaluation\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      if (!applicationId || !isAuthenticated || !user) return;\r\n\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        // Load application details\r\n        const appResponse = await applicationService.getApplication(applicationId);\r\n        setApplication(appResponse);\r\n\r\n        // Set license category ID for navigation\r\n        if (appResponse?.license_category_id) {\r\n          setLicenseCategoryId(appResponse.license_category_id);\r\n        }\r\n\r\n        // Try to load existing evaluation for this application\r\n        try {\r\n          const evaluations = await evaluationService.getEvaluationByApplication(applicationId);\r\n          const currentUserEvaluation = Array.isArray(evaluations)\r\n            ? evaluations.find((evaluation: Evaluation) => evaluation.evaluator_id === user.user_id && evaluation.status !== 'completed')\r\n            : (evaluations && evaluations.evaluator_id === user.user_id && evaluations.status !== 'completed' ? evaluations : null);\r\n\r\n          if (currentUserEvaluation) {\r\n            setEvaluation(currentUserEvaluation);\r\n            setEvaluationComment(currentUserEvaluation.evaluators_notes || '');\r\n            setFinalRecommendation(currentUserEvaluation.recommendation || '');\r\n            setTotalScore(currentUserEvaluation.total_score || 0);\r\n            setShareholdingCompliance(currentUserEvaluation.shareholding_compliance ?? true);\r\n\r\n            if (currentUserEvaluation.criteria && currentUserEvaluation.criteria.length > 0) {\r\n              setEvaluationCriteria(currentUserEvaluation.criteria);\r\n            }\r\n          }\r\n        } catch (evalError) {\r\n          console.log('No existing evaluation found, will create new one');\r\n        }\r\n      } catch (err: any) {\r\n        console.error('Error loading application data:', err);\r\n        setError('Failed to load application data');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, [applicationId, isAuthenticated, user]);\r\n\r\n  // Navigation handlers - modified for evaluation\r\n  const handlePrevious = () => {\r\n    if (!applicationId || !previousStep) return;\r\n\r\n    const params = new URLSearchParams();\r\n    params.set('application_id', applicationId);\r\n    if (licenseCategoryId) {\r\n      params.set('license_category_id', licenseCategoryId);\r\n    }\r\n    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);\r\n  };\r\n\r\n  // Final evaluation submission handler\r\n  const handleSubmitFinalEvaluation = async (recommendation: 'approve' | 'conditional_approve' | 'reject') => {\r\n    if (!applicationId || !user) return;\r\n\r\n    if (!evaluationComment.trim()) {\r\n      setError('Please provide evaluation comments before submitting');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsSubmitting(true);\r\n      setError(null);\r\n\r\n      const evaluationData = {\r\n        total_score: totalScore,\r\n        recommendation,\r\n        evaluators_notes: evaluationComment,\r\n        criteria: evaluationCriteria.map(c => ({\r\n          category: c.category,\r\n          subcategory: c.subcategory,\r\n          score: c.score,\r\n          weight: c.weight,\r\n          max_marks: c.max_marks,\r\n          awarded_marks: c.awarded_marks\r\n        }))\r\n      };\r\n\r\n      if (evaluation) {\r\n        // Update existing evaluation\r\n        await evaluationService.submitEvaluation(evaluation.evaluation_id, evaluationData);\r\n      } else {\r\n        // Create new evaluation\r\n        const newEvaluation = await evaluationService.createEvaluation({\r\n          application_id: applicationId,\r\n          evaluator_id: user.user_id,\r\n          evaluation_type: 'FINAL_REVIEW' as any,\r\n          ...evaluationData,\r\n          shareholding_compliance: shareholdingCompliance\r\n        });\r\n        setEvaluation(newEvaluation);\r\n      }\r\n\r\n      // Update application status based on recommendation\r\n      let newStatus: string;\r\n      switch (recommendation) {\r\n        case 'approve':\r\n          newStatus = ApplicationStatus.APPROVED;\r\n          break;\r\n        case 'conditional_approve':\r\n          newStatus = ApplicationStatus.APPROVED; // Use APPROVED for conditional approval\r\n          break;\r\n        case 'reject':\r\n          newStatus = ApplicationStatus.REJECTED;\r\n          break;\r\n        default:\r\n          newStatus = ApplicationStatus.UNDER_REVIEW;\r\n      }\r\n\r\n      await applicationService.updateApplicationStatus(applicationId, newStatus);\r\n\r\n      // Show success message and redirect\r\n      alert(`Final evaluation submitted successfully! Application ${recommendation.replace('_', ' ')}.`);\r\n      router.push('/dashboard');\r\n    } catch (err: any) {\r\n      console.error('Error submitting final evaluation:', err);\r\n      setError('Failed to submit final evaluation');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Loading state\r\n  if (authLoading || loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600 dark:text-gray-400\">Loading application data...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <div className=\"text-center\">\r\n          <i className=\"ri-error-warning-line text-4xl text-red-500 mb-4\"></i>\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">Error Loading Application</h3>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">{error}</p>\r\n          <button\r\n            onClick={() => router.push('/dashboard')}\r\n            className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\"\r\n          >\r\n            Back to Dashboard\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // No application found\r\n  if (!application) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <div className=\"text-center\">\r\n          <i className=\"ri-file-search-line text-4xl text-gray-400 mb-4\"></i>\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">Application Not Found</h3>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">The requested application could not be found.</p>\r\n          <button\r\n            onClick={() => router.push('/dashboard')}\r\n            className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\"\r\n          >\r\n            Back to Dashboard\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n      <EvaluationLayout\r\n        applicationId={applicationId!}\r\n        licenseTypeCode={licenseType}\r\n        currentStepRoute=\"submit\"\r\n        onNext={undefined}\r\n        onPrevious={handlePrevious}\r\n        showNextButton={false}\r\n        showPreviousButton={!!previousStep}\r\n        nextButtonDisabled={isSubmitting}\r\n        previousButtonDisabled={isSubmitting}\r\n        nextButtonText=\"Continue\"\r\n        previousButtonText={previousStep ? `Back to ${previousStep.name}` : \"Back\"}\r\n      >\r\n\r\n      {/* Application Review Summary */}\r\n      <div className=\"space-y-6\">\r\n        {/* Application Overview */}\r\n        <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n              Application Summary\r\n            </h3>\r\n            <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\r\n              <i className=\"ri-file-list-line mr-2\"></i>\r\n              Final Review\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Application ID\r\n              </label>\r\n              <div className=\"p-3 bg-white dark:bg-gray-800 rounded-md border\">\r\n                <p className=\"text-gray-900 dark:text-gray-100 font-mono text-sm\">\r\n                  {applicationId}\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                License Type\r\n              </label>\r\n              <div className=\"p-3 bg-white dark:bg-gray-800 rounded-md border\">\r\n                <p className=\"text-gray-900 dark:text-gray-100 capitalize\">\r\n                  {licenseType?.replace('_', ' ')}\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Current Status\r\n              </label>\r\n              <div className=\"p-3 bg-white dark:bg-gray-800 rounded-md border\">\r\n                <p className=\"text-gray-900 dark:text-gray-100 capitalize\">\r\n                  {application?.status || 'Under Review'}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        {/* Review Instructions */}\r\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800\">\r\n          <h3 className=\"text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center\">\r\n            <i className=\"ri-information-line mr-2\"></i>\r\n            Review Instructions\r\n          </h3>\r\n          <div className=\"space-y-3\">\r\n            <p className=\"text-blue-700 dark:text-blue-300\">\r\n              Before making your final decision, please ensure you have:\r\n            </p>\r\n            <ul className=\"list-disc list-inside text-blue-700 dark:text-blue-300 space-y-2 ml-4\">\r\n              <li>Reviewed all application sections for completeness and accuracy</li>\r\n              <li>Verified all required documents have been submitted and are valid</li>\r\n              <li>Checked that the applicant meets all licensing requirements</li>\r\n              <li>Assessed the technical and financial capabilities of the applicant</li>\r\n              <li>Considered any regulatory or compliance concerns</li>\r\n              <li>Reviewed any previous evaluation comments from other steps</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Evaluation Criteria Section */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center\">\r\n            <i className=\"ri-clipboard-line mr-2\"></i>\r\n            Evaluation Criteria & Scoring\r\n          </h3>\r\n\r\n          <div className=\"space-y-4\">\r\n            {evaluationCriteria.map((criterion, index) => (\r\n              <div key={index} className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n                <div className=\"flex items-center justify-between mb-3\">\r\n                  <div>\r\n                    <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                      {criterion.category}\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                      {criterion.subcategory} (Weight: {(criterion.weight * 100).toFixed(0)}%)\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"text-right\">\r\n                    <div className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                      {criterion.score.toFixed(1)}%\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                      {criterion.awarded_marks || 0}/{criterion.max_marks || 100}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                    Awarded Marks:\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    max={criterion.max_marks || 100}\r\n                    value={criterion.awarded_marks || 0}\r\n                    onChange={(e) => updateCriteriaScore(index, parseInt(e.target.value) || 0)}\r\n                    className=\"w-24 px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100\"\r\n                  />\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    / {criterion.max_marks || 100}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            ))}\r\n\r\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h4 className=\"text-lg font-semibold text-blue-800 dark:text-blue-200\">\r\n                  Total Weighted Score\r\n                </h4>\r\n                <div className=\"text-2xl font-bold text-blue-800 dark:text-blue-200\">\r\n                  {totalScore.toFixed(1)}%\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Shareholding Compliance */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n            <i className=\"ri-shield-check-line mr-2\"></i>\r\n            Shareholding Compliance\r\n          </h3>\r\n\r\n          <div className=\"flex items-center space-x-4\">\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={shareholdingCompliance}\r\n                onChange={(e) => setShareholdingCompliance(e.target.checked)}\r\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n              />\r\n              <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\r\n                Applicant meets shareholding compliance requirements\r\n              </span>\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Final Evaluation Decision */}\r\n        <div className=\"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-6 border border-green-200 dark:border-green-800\">\r\n          <h3 className=\"text-xl font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\">\r\n            <i className=\"ri-shield-check-line mr-2\"></i>\r\n            Final Evaluation Decision\r\n          </h3>\r\n\r\n          <div className=\"space-y-6\">\r\n            {/* Evaluation Comments */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Final Evaluation Comments <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <textarea\r\n                value={evaluationComment}\r\n                onChange={(e) => setEvaluationComment(e.target.value)}\r\n                rows={4}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-800 dark:text-gray-100\"\r\n                placeholder=\"Provide comprehensive evaluation comments, including rationale for your decision, key findings, and any conditions or recommendations...\"\r\n                required\r\n              />\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                Please provide detailed comments explaining your evaluation and decision rationale.\r\n              </p>\r\n            </div>\r\n\r\n            {/* Recommendation Selection */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\r\n                Final Recommendation <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <div className=\"space-y-3\">\r\n                <label className=\"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"recommendation\"\r\n                    value=\"approve\"\r\n                    checked={finalRecommendation === 'approve'}\r\n                    onChange={(e) => setFinalRecommendation(e.target.value as any)}\r\n                    className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300\"\r\n                  />\r\n                  <div className=\"ml-3\">\r\n                    <div className=\"text-sm font-medium text-green-700 dark:text-green-300\">\r\n                      Approve Application\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                      Application meets all requirements and should be approved\r\n                    </div>\r\n                  </div>\r\n                </label>\r\n\r\n                <label className=\"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"recommendation\"\r\n                    value=\"conditional_approve\"\r\n                    checked={finalRecommendation === 'conditional_approve'}\r\n                    onChange={(e) => setFinalRecommendation(e.target.value as any)}\r\n                    className=\"h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300\"\r\n                  />\r\n                  <div className=\"ml-3\">\r\n                    <div className=\"text-sm font-medium text-yellow-700 dark:text-yellow-300\">\r\n                      Conditional Approval\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                      Approve with specific conditions that must be met\r\n                    </div>\r\n                  </div>\r\n                </label>\r\n\r\n                <label className=\"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"recommendation\"\r\n                    value=\"reject\"\r\n                    checked={finalRecommendation === 'reject'}\r\n                    onChange={(e) => setFinalRecommendation(e.target.value as any)}\r\n                    className=\"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300\"\r\n                  />\r\n                  <div className=\"ml-3\">\r\n                    <div className=\"text-sm font-medium text-red-700 dark:text-red-300\">\r\n                      Reject Application\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                      Application does not meet requirements and should be rejected\r\n                    </div>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Submit Button */}\r\n            <div className=\"pt-4 border-t border-gray-200 dark:border-gray-700\">\r\n              <button\r\n                onClick={() => finalRecommendation && handleSubmitFinalEvaluation(finalRecommendation)}\r\n                disabled={isSubmitting || !finalRecommendation || !evaluationComment.trim()}\r\n                className=\"w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                    Submitting Final Evaluation...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    Submit Final Evaluation\r\n                  </>\r\n                )}\r\n              </button>\r\n\r\n              {(!finalRecommendation || !evaluationComment.trim()) && (\r\n                <p className=\"text-xs text-red-500 mt-2 text-center\">\r\n                  Please select a recommendation and provide evaluation comments before submitting.\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n\r\n      </EvaluationLayout>\r\n  );\r\n};\r\n\r\nexport default EvaluateSubmitPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAoBA,MAAM,qBAAwD,CAAC,EAAE,MAAM,EAAE;;IACvE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE9D,kCAAkC;IAClC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,MAAG,AAAD,EAAE;IAC3B,MAAM,cAAc,cAAc,CAAC,eAAe;IAClD,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,yBAAyB;IACzB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqD;IAClH,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QACjF;YAAE,UAAU;YAAwB,aAAa;YAAkB,OAAO;YAAG,QAAQ;YAAK,WAAW;YAAK,eAAe;QAAE;QAC3H;YAAE,UAAU;YAAwB,aAAa;YAAwB,OAAO;YAAG,QAAQ;YAAM,WAAW;YAAK,eAAe;QAAE;QAClI;YAAE,UAAU;YAAoB,aAAa;YAA2B,OAAO;YAAG,QAAQ;YAAK,WAAW;YAAK,eAAe;QAAE;QAChI;YAAE,UAAU;YAAc,aAAa;YAAuB,OAAO;YAAG,QAAQ;YAAM,WAAW;YAAK,eAAe;QAAE;QACvH;YAAE,UAAU;YAAiB,aAAa;YAAgB,OAAO;YAAG,QAAQ;YAAK,WAAW;YAAK,eAAe;QAAE;KACnH;IACD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAE9E,gDAAgD;IAChD,MAAM,EACJ,YAAY,EACb,GAAG,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE;QACvB,kBAAkB;QAClB;QACA;IACF;IAEA,sCAAsC;IACtC,MAAM,sBAAsB,CAAC;QAC3B,OAAO,uIAAA,CAAA,oBAAiB,CAAC,mBAAmB,CAAC;IAC/C;IAEA,8CAA8C;IAC9C,MAAM,sBAAsB,CAAC,OAAe;QAC1C,MAAM,kBAAkB;eAAI;SAAmB;QAC/C,eAAe,CAAC,MAAM,CAAC,aAAa,GAAG;QACvC,eAAe,CAAC,MAAM,CAAC,KAAK,GAAG,AAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,IAAI,GAAG,IAAK;QAC5F,sBAAsB;QACtB,cAAc,oBAAoB;IACpC;IAEA,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;yDAAW;oBACf,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM;oBAEjD,IAAI;wBACF,WAAW;wBACX,SAAS;wBAET,2BAA2B;wBAC3B,MAAM,cAAc,MAAM,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;wBAC5D,eAAe;wBAEf,yCAAyC;wBACzC,IAAI,aAAa,qBAAqB;4BACpC,qBAAqB,YAAY,mBAAmB;wBACtD;wBAEA,uDAAuD;wBACvD,IAAI;4BACF,MAAM,cAAc,MAAM,uIAAA,CAAA,oBAAiB,CAAC,0BAA0B,CAAC;4BACvE,MAAM,wBAAwB,MAAM,OAAO,CAAC,eACxC,YAAY,IAAI;yEAAC,CAAC,aAA2B,WAAW,YAAY,KAAK,KAAK,OAAO,IAAI,WAAW,MAAM,KAAK;0EAC9G,eAAe,YAAY,YAAY,KAAK,KAAK,OAAO,IAAI,YAAY,MAAM,KAAK,cAAc,cAAc;4BAEpH,IAAI,uBAAuB;gCACzB,cAAc;gCACd,qBAAqB,sBAAsB,gBAAgB,IAAI;gCAC/D,uBAAuB,sBAAsB,cAAc,IAAI;gCAC/D,cAAc,sBAAsB,WAAW,IAAI;gCACnD,0BAA0B,sBAAsB,uBAAuB,IAAI;gCAE3E,IAAI,sBAAsB,QAAQ,IAAI,sBAAsB,QAAQ,CAAC,MAAM,GAAG,GAAG;oCAC/E,sBAAsB,sBAAsB,QAAQ;gCACtD;4BACF;wBACF,EAAE,OAAO,WAAW;4BAClB,QAAQ,GAAG,CAAC;wBACd;oBACF,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;uCAAG;QAAC;QAAe;QAAiB;KAAK;IAEzC,gDAAgD;IAChD,MAAM,iBAAiB;QACrB,IAAI,CAAC,iBAAiB,CAAC,cAAc;QAErC,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,kBAAkB;QAC7B,IAAI,mBAAmB;YACrB,OAAO,GAAG,CAAC,uBAAuB;QACpC;QACA,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY,UAAU,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAChG;IAEA,sCAAsC;IACtC,MAAM,8BAA8B,OAAO;QACzC,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAE7B,IAAI,CAAC,kBAAkB,IAAI,IAAI;YAC7B,SAAS;YACT;QACF;QAEA,IAAI;YACF,gBAAgB;YAChB,SAAS;YAET,MAAM,iBAAiB;gBACrB,aAAa;gBACb;gBACA,kBAAkB;gBAClB,UAAU,mBAAmB,GAAG,CAAC,CAAA,IAAK,CAAC;wBACrC,UAAU,EAAE,QAAQ;wBACpB,aAAa,EAAE,WAAW;wBAC1B,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,MAAM;wBAChB,WAAW,EAAE,SAAS;wBACtB,eAAe,EAAE,aAAa;oBAChC,CAAC;YACH;YAEA,IAAI,YAAY;gBACd,6BAA6B;gBAC7B,MAAM,uIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,WAAW,aAAa,EAAE;YACrE,OAAO;gBACL,wBAAwB;gBACxB,MAAM,gBAAgB,MAAM,uIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC;oBAC7D,gBAAgB;oBAChB,cAAc,KAAK,OAAO;oBAC1B,iBAAiB;oBACjB,GAAG,cAAc;oBACjB,yBAAyB;gBAC3B;gBACA,cAAc;YAChB;YAEA,oDAAoD;YACpD,IAAI;YACJ,OAAQ;gBACN,KAAK;oBACH,YAAY,0HAAA,CAAA,oBAAiB,CAAC,QAAQ;oBACtC;gBACF,KAAK;oBACH,YAAY,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,EAAE,wCAAwC;oBAChF;gBACF,KAAK;oBACH,YAAY,0HAAA,CAAA,oBAAiB,CAAC,QAAQ;oBACtC;gBACF;oBACE,YAAY,0HAAA,CAAA,oBAAiB,CAAC,YAAY;YAC9C;YAEA,MAAM,wIAAA,CAAA,qBAAkB,CAAC,uBAAuB,CAAC,eAAe;YAEhE,oCAAoC;YACpC,MAAM,CAAC,qDAAqD,EAAE,eAAe,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC;YACjG,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,sCAAsC;YACpD,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAIA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAE,WAAU;kCAAyC;;;;;;kCACtD,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,uBAAuB;IACvB,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;kCACrD,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACI,6LAAC,uJAAA,CAAA,UAAgB;QACf,eAAe;QACf,iBAAiB;QACjB,kBAAiB;QACjB,QAAQ;QACR,YAAY;QACZ,gBAAgB;QAChB,oBAAoB,CAAC,CAAC;QACtB,oBAAoB;QACpB,wBAAwB;QACxB,gBAAe;QACf,oBAAoB,eAAe,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,GAAG;kBAItE,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,6LAAC;oCAAK,WAAU;;sDACd,6LAAC;4CAAE,WAAU;;;;;;wCAA6B;;;;;;;;;;;;;sCAK9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;;;;;;;8CAIP,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DACV,aAAa,QAAQ,KAAK;;;;;;;;;;;;;;;;;8CAIjC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DACV,aAAa,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;;;;;;gCAA+B;;;;;;;sCAG9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAGhD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAMV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;;;;;;gCAA6B;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;;gCACZ,mBAAmB,GAAG,CAAC,CAAC,WAAW,sBAClC,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,UAAU,QAAQ;;;;;;0EAErB,6LAAC;gEAAE,WAAU;;oEACV,UAAU,WAAW;oEAAC;oEAAW,CAAC,UAAU,MAAM,GAAG,GAAG,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAG1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,UAAU,KAAK,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EAE9B,6LAAC;gEAAI,WAAU;;oEACZ,UAAU,aAAa,IAAI;oEAAE;oEAAE,UAAU,SAAS,IAAI;;;;;;;;;;;;;;;;;;;0DAK7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAuD;;;;;;kEAGxE,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAK,UAAU,SAAS,IAAI;wDAC5B,OAAO,UAAU,aAAa,IAAI;wDAClC,UAAU,CAAC,IAAM,oBAAoB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wDACxE,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;;4DAA2C;4DACtD,UAAU,SAAS,IAAI;;;;;;;;;;;;;;uCAjCtB;;;;;8CAuCZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyD;;;;;;0DAGvE,6LAAC;gDAAI,WAAU;;oDACZ,WAAW,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;;;;;;gCAAgC;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,OAAO;wCAC3D,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;8BAQtE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;;;;;;gCAAgC;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDAAkE;8DACvD,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE3D,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACpD,MAAM;4CACN,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;sDAEV,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;8CAM/D,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDAAkE;8DAC5D,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,wBAAwB;4DACjC,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;4DACtD,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAyD;;;;;;8EAGxE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;8DAM9D,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,wBAAwB;4DACjC,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;4DACtD,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA2D;;;;;;8EAG1E,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;8DAM9D,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,wBAAwB;4DACjC,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;4DACtD,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqD;;;;;;8EAGpE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASlE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,uBAAuB,4BAA4B;4CAClE,UAAU,gBAAgB,CAAC,uBAAuB,CAAC,kBAAkB,IAAI;4CACzE,WAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAAyC;;6EAIxD;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAA8B;;;;;;;;wCAMhD,CAAC,CAAC,uBAAuB,CAAC,kBAAkB,IAAI,EAAE,mBACjD,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarE;GAphBM;;QACW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QACoB,kIAAA,CAAA,UAAO;QA+B3D,uIAAA,CAAA,uBAAoB;;;KAlCpB;uCAshBS", "debugId": null}}]}