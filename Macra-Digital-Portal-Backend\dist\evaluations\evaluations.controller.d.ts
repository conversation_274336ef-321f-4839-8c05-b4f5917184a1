import { PaginateQuery } from 'nestjs-paginate';
import { EvaluationsService } from './evaluations.service';
import { CreateEvaluationDto } from '../dto/evaluations/create-evaluation.dto';
import { UpdateEvaluationDto } from '../dto/evaluations/update-evaluation.dto';
import { Evaluations } from '../entities/evaluations.entity';
export declare class EvaluationsController {
    private readonly evaluationsService;
    constructor(evaluationsService: EvaluationsService);
    create(createEvaluationDto: CreateEvaluationDto, req: any): Promise<Evaluations>;
    findAll(query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<Evaluations>>;
    getStats(): Promise<{
        total: number;
        draft: number;
        completed: number;
        approved: number;
        rejected: number;
        averageScore: number;
    }>;
    findOne(id: string): Promise<Evaluations>;
    findByApplication(applicationId: string): Promise<Evaluations | null>;
    findCriteria(id: string): Promise<import("../entities").EvaluationCriteria[]>;
    update(id: string, updateEvaluationDto: UpdateEvaluationDto, req: any): Promise<Evaluations>;
    remove(id: string): Promise<void>;
}
