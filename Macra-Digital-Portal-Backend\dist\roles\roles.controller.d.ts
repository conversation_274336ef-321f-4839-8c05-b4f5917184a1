import { RolesService } from './roles.service';
import { CreateRoleDto } from '../dto/role/create-role.dto';
import { UpdateRoleDto } from '../dto/role/update-role.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { Role } from '../entities';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class RolesController {
    private readonly rolesService;
    constructor(rolesService: RolesService);
    create(createRoleDto: CreateRoleDto): Promise<Role>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<Role>>;
    findOne(id: string): Promise<Role>;
    update(id: string, updateRoleDto: UpdateRoleDto): Promise<Role>;
    remove(id: string): Promise<{
        message: string;
    }>;
    assignPermissions(id: string, permissionIds: string[]): Promise<Role>;
    removePermissions(id: string, permissionIds: string[]): Promise<Role>;
}
