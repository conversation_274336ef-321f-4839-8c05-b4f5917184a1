import { Repository } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { ApplicationTaskHelperService } from './application-task-helper.service';
export declare class ApplicationsService {
    private applicationsRepository;
    private applicationTaskHelper;
    constructor(applicationsRepository: Repository<Applications>, applicationTaskHelper: ApplicationTaskHelperService);
    private readonly paginateConfig;
    create(createApplicationDto: CreateApplicationDto, createdBy: string): Promise<Applications>;
    findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Applications>>;
    findUserApplications(query: PaginateQuery): Promise<Paginated<Applications>>;
    findOne(id: string): Promise<Applications>;
    findByApplicant(applicantId: string): Promise<Applications[]>;
    findByStatus(status: string): Promise<Applications[]>;
    update(id: string, updateApplicationDto: UpdateApplicationDto, updatedBy: string): Promise<Applications>;
    remove(id: string): Promise<void>;
    updateStatus(id: string, status: string, updatedBy: string): Promise<Applications>;
    updateProgress(id: string, currentStep: number, progressPercentage: number, updatedBy: string): Promise<Applications>;
    getApplicationStats(): Promise<any>;
    assignApplication(applicationId: string, assignedTo: string, assignedBy: string): Promise<Applications>;
    getUnassignedApplications(query: PaginateQuery): Promise<Paginated<Applications>>;
    getAssignedApplications(userId: string, query: PaginateQuery): Promise<Paginated<Applications>>;
    findAllDebug(query: PaginateQuery): Promise<Paginated<Applications>>;
}
