export declare class CreateTypeApprovedManufacturerDto {
    manufacturer_id?: string;
    manufacturer_name: string;
    manufacturer_country_origin: string;
    manufacturer_region?: string;
    manufacturer_address?: string;
    manufacturer_contact_person?: string;
    manufacturer_email?: string;
    manufacturer_phone?: string;
    manufacturer_website: string;
    manufacturer_approval_number: string;
    manufacturer_approval_date: string;
    approval_certification_standard?: string;
    equipment_types?: string;
    created_by: string;
}
export declare class UpdateTypeApprovedManufacturerDto {
    manufacturer_id?: string;
    manufacturer_name?: string;
    manufacturer_country_origin?: string;
    manufacturer_region?: string;
    manufacturer_address?: string;
    manufacturer_contact_person?: string;
    manufacturer_email?: string;
    manufacturer_phone?: string;
    manufacturer_website?: string;
    manufacturer_approval_number?: string;
    manufacturer_approval_date?: string;
    approval_certification_standard?: string;
    equipment_types?: string;
    updated_by?: string;
}
