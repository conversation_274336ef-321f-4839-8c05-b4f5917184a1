import { CreateAddressDto } from '../dto/address/create.dto';
import { UpdateAddressDto } from '../dto/address/update.dto';
import { Address } from '../entities/address.entity';
import { DataSource, Repository } from 'typeorm';
export declare class AddressService {
    private readonly addressRepository;
    private readonly dataSource;
    constructor(addressRepository: Repository<Address>, dataSource: DataSource);
    createAddress(createAddressDto: CreateAddressDto, createdBy: string): Promise<Address>;
    editAddress(addressId: string, updateAddressDto: UpdateAddressDto, updatedBy: string): Promise<Address>;
    findAll(filter?: {
        entity_type?: string;
        entity_id?: string;
        address_type?: string;
    }): Promise<Address[]>;
    findOneById(id: string): Promise<Address>;
    softDelete(id: string, deletedBy: string): Promise<void>;
    restore(id: string): Promise<void>;
    hardDelete(id: string): Promise<void>;
}
