"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = exports.assetsDir = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const typeorm_1 = require("@nestjs/typeorm");
const mailer_1 = require("@nestjs-modules/mailer");
const handlebars_adapter_1 = require("@nestjs-modules/mailer/dist/adapters/handlebars.adapter");
const config_1 = require("@nestjs/config");
const throttler_1 = require("@nestjs/throttler");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const roles_module_1 = require("./roles/roles.module");
const permissions_module_1 = require("./permissions/permissions.module");
const audit_trail_module_1 = require("./audit-trail/audit-trail.module");
const seeder_module_1 = require("./database/seeders/seeder.module");
const license_types_module_1 = require("./license-types/license-types.module");
const license_categories_module_1 = require("./license-categories/license-categories.module");
const identification_types_module_1 = require("./identification-types/identification-types.module");
const license_category_documents_module_1 = require("./license-category-documents/license-category-documents.module");
const applications_module_1 = require("./applications/applications.module");
const applicants_module_1 = require("./applicants/applicants.module");
const documents_module_1 = require("./documents/documents.module");
const contacts_module_1 = require("./contacts/contacts.module");
const contact_persons_module_1 = require("./contact-persons/contact-persons.module");
const application_status_tracking_module_1 = require("./application-status-tracking/application-status-tracking.module");
const consumer_affairs_module_1 = require("./consumer-affairs/consumer-affairs.module");
const data_breach_module_1 = require("./data-breach/data-breach.module");
const evaluations_module_1 = require("./evaluations/evaluations.module");
const payments_module_1 = require("./payments/payments.module");
const response_interceptor_1 = require("./common/interceptors/response.interceptor");
const http_exception_filter_1 = require("./common/filters/http-exception.filter");
const Joi = __importStar(require("joi"));
const path_1 = require("path");
const postal_code_entity_1 = require("./entities/postal-code.entity");
const address_module_1 = require("./address/address.module");
const organization_module_1 = require("./organization/organization.module");
const department_module_1 = require("./department/department.module");
const stakeholders_module_1 = require("./stakeholders/stakeholders.module");
const scope_of_service_module_1 = require("./scope-of-service/scope-of-service.module");
const professional_services_module_1 = require("./professional-services/professional-services.module");
const legal_history_module_1 = require("./legal-history/legal-history.module");
const standards_module_1 = require("./standards/standards.module");
const dashboard_module_1 = require("./dashboard/dashboard.module");
const tasks_module_1 = require("./tasks/tasks.module");
function getDatabaseType(dbDriver) {
    const supported = ['mysql', 'mariadb', 'postgres', 'cockroachdb', 'mongodb', 'aurora-mysql'];
    if (dbDriver && supported.includes(dbDriver)) {
        return dbDriver;
    }
    console.warn(`Warning: Unsupported or missing DB_DRIVER "${dbDriver}". Defaulting to "mysql". Supported: ${supported.join(', ')}`);
    return 'mysql';
}
const isProd = process.env.NODE_ENV === 'production';
const isTest = process.env.NODE_ENV === 'test';
const templatesDir = isProd
    ? (0, path_1.join)(__dirname, 'templates')
    : (0, path_1.join)(process.cwd(), 'src', 'templates');
exports.assetsDir = isProd
    ? (0, path_1.join)(__dirname, 'templates', 'assets')
    : (0, path_1.join)(process.cwd(), 'src', 'templates', 'assets');
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: `.env${isTest ? '.test' : ''}`,
                validationSchema: Joi.object({
                    DB_DRIVER: Joi.string().valid('mysql', 'mariadb', 'postgres', 'cockroachdb', 'mongodb', 'aurora-mysql'),
                    DB_HOST: Joi.string().required(),
                    DB_PORT: Joi.number().default(3306),
                    DB_USERNAME: Joi.string().required(),
                    DB_PASSWORD: Joi.string().allow('').required(),
                    DB_NAME: Joi.string().required(),
                    EMAIL_USER: Joi.string().default(''),
                    EMAIL_PWD: Joi.string().default(''),
                    EMAIL_PORT: Joi.string().default(587),
                    NODE_ENV: Joi.string()
                        .valid('development', 'production', 'test')
                        .default('development'),
                }),
            }),
            typeorm_1.TypeOrmModule.forFeature([postal_code_entity_1.PostalCode]),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (config) => {
                    const dbType = getDatabaseType(config.get('DB_DRIVER'));
                    const baseConfig = {
                        type: dbType,
                        host: config.get('DB_HOST'),
                        port: config.get('DB_PORT'),
                        username: config.get('DB_USERNAME'),
                        password: config.get('DB_PASSWORD'),
                        database: config.get('DB_NAME'),
                        entities: [__dirname + '/**/*.entity{.ts,.js}'],
                        synchronize: false,
                        migrationsRun: false,
                        migrations: [(0, path_1.join)(__dirname, 'migrations', '*.{ts,js}')],
                        logging: config.get('NODE_ENV') === 'development',
                        retryAttempts: 3,
                        retryDelay: 3000,
                        ssl: config.get('DB_SSL', 'false') === 'true'
                            ? { rejectUnauthorized: false }
                            : false,
                    };
                    if (dbType === 'mysql' || dbType === 'mariadb') {
                        return {
                            ...baseConfig,
                            charset: 'utf8mb4',
                            timezone: '+00:00'
                        };
                    }
                    if (dbType === 'postgres') {
                        return {
                            ...baseConfig,
                            extra: {
                                installExtensions: false,
                            },
                            applicationName: 'MACRA Digital Portal',
                        };
                    }
                    return baseConfig;
                },
            }),
            mailer_1.MailerModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: async (config) => ({
                    transport: {
                        host: 'smtp.gmail.com',
                        port: parseInt(config.get('EMAIL_PORT') || '587', 10),
                        secure: false,
                        auth: {
                            user: config.get('EMAIL_USER'),
                            pass: config.get('EMAIL_PWD'),
                        },
                    },
                    defaults: {
                        from: `"MACRA Digital Portal" <${config.get('EMAIL_USER')}>`,
                    },
                    template: {
                        dir: templatesDir,
                        adapter: new handlebars_adapter_1.HandlebarsAdapter(),
                        options: {
                            strict: true,
                        },
                    },
                }),
            }),
            throttler_1.ThrottlerModule.forRoot([
                {
                    ttl: 60000,
                    limit: 200,
                },
            ]),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            roles_module_1.RolesModule,
            permissions_module_1.PermissionsModule,
            audit_trail_module_1.AuditTrailModule,
            seeder_module_1.SeederModule,
            license_types_module_1.LicenseTypesModule,
            license_categories_module_1.LicenseCategoriesModule,
            identification_types_module_1.IdentificationTypesModule,
            license_category_documents_module_1.LicenseCategoryDocumentsModule,
            applications_module_1.ApplicationsModule,
            applicants_module_1.ApplicantsModule,
            documents_module_1.DocumentsModule,
            contacts_module_1.ContactsModule,
            contact_persons_module_1.ContactPersonsModule,
            application_status_tracking_module_1.ApplicationStatusTrackingModule,
            address_module_1.AddressModule,
            consumer_affairs_module_1.ConsumerAffairsModule,
            data_breach_module_1.DataBreachModule,
            evaluations_module_1.EvaluationsModule,
            payments_module_1.PaymentsModule,
            organization_module_1.OrganizationModule,
            department_module_1.DepartmentModule,
            stakeholders_module_1.StakeholdersModule,
            scope_of_service_module_1.ScopeOfServiceModule,
            professional_services_module_1.ProfessionalServicesModule,
            legal_history_module_1.LegalHistoryModule,
            standards_module_1.StandardsModule,
            dashboard_module_1.DashboardModule,
            tasks_module_1.TasksModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: response_interceptor_1.ResponseInterceptor,
            },
            {
                provide: core_1.APP_FILTER,
                useClass: http_exception_filter_1.HttpExceptionFilter,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: throttler_1.ThrottlerGuard,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map