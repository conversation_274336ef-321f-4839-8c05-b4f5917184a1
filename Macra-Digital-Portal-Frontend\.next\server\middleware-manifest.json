{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "5/HwWYBhDdDMZ4WGkDyoObhSYCOuWATNxO63Fhks2rI=", "__NEXT_PREVIEW_MODE_ID": "b115ff2da386a8344625d1168c09d066", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f465cd905bf6e057ca038a3be1adaf29a342b44b035b00bc3ae38d1a183a7654", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "29753f8238d6eb14177c13cfd4aaebaab271affb179abee22d8b845d752468e6"}}}, "sortedMiddleware": ["/"], "functions": {}}