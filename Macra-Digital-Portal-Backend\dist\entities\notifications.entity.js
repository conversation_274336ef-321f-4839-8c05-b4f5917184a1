"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notifications = exports.RecipientType = exports.NotificationPriority = exports.NotificationStatus = exports.NotificationType = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const user_entity_1 = require("./user.entity");
var NotificationType;
(function (NotificationType) {
    NotificationType["APPLICATION_STATUS"] = "application_status";
    NotificationType["EVALUATION_ASSIGNED"] = "evaluation_assigned";
    NotificationType["PAYMENT_DUE"] = "payment_due";
    NotificationType["LICENSE_EXPIRY"] = "license_expiry";
    NotificationType["SYSTEM_ALERT"] = "system_alert";
    NotificationType["EMAIL"] = "email";
    NotificationType["SMS"] = "sms";
    NotificationType["PUSH"] = "push";
    NotificationType["IN_APP"] = "in_app";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var NotificationStatus;
(function (NotificationStatus) {
    NotificationStatus["PENDING"] = "pending";
    NotificationStatus["SENT"] = "sent";
    NotificationStatus["DELIVERED"] = "delivered";
    NotificationStatus["FAILED"] = "failed";
    NotificationStatus["BOUNCED"] = "bounced";
    NotificationStatus["READ"] = "read";
})(NotificationStatus || (exports.NotificationStatus = NotificationStatus = {}));
var NotificationPriority;
(function (NotificationPriority) {
    NotificationPriority["LOW"] = "low";
    NotificationPriority["MEDIUM"] = "medium";
    NotificationPriority["HIGH"] = "high";
    NotificationPriority["URGENT"] = "urgent";
})(NotificationPriority || (exports.NotificationPriority = NotificationPriority = {}));
var RecipientType;
(function (RecipientType) {
    RecipientType["CUSTOMER"] = "customer";
    RecipientType["STAFF"] = "staff";
    RecipientType["ADMIN"] = "admin";
    RecipientType["SYSTEM"] = "system";
})(RecipientType || (exports.RecipientType = RecipientType = {}));
let Notifications = class Notifications {
    notification_id;
    type;
    status;
    priority;
    recipient_type;
    recipient_id;
    recipient_email;
    recipient_phone;
    subject;
    message;
    html_content;
    entity_type;
    entity_id;
    metadata;
    external_id;
    error_message;
    retry_count;
    is_read;
    sent_at;
    delivered_at;
    action_url;
    expires_at;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    read_at;
    recipient;
    creator;
    updater;
    generateId() {
        if (!this.notification_id) {
            this.notification_id = (0, uuid_1.v4)();
        }
    }
};
exports.Notifications = Notifications;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], Notifications.prototype, "notification_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notification type',
        enum: NotificationType,
        example: NotificationType.EMAIL
    }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
    }),
    (0, class_validator_1.IsEnum)(NotificationType),
    __metadata("design:type", String)
], Notifications.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notification status',
        enum: NotificationStatus,
        example: NotificationStatus.SENT
    }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: NotificationStatus.PENDING,
    }),
    (0, class_validator_1.IsEnum)(NotificationStatus),
    __metadata("design:type", String)
], Notifications.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notification priority',
        enum: NotificationPriority,
        example: NotificationPriority.MEDIUM
    }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: NotificationPriority.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(NotificationPriority),
    __metadata("design:type", String)
], Notifications.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient type',
        enum: RecipientType,
        example: RecipientType.CUSTOMER
    }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
    }),
    (0, class_validator_1.IsEnum)(RecipientType),
    __metadata("design:type", String)
], Notifications.prototype, "recipient_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient user ID',
        example: 'user-uuid-here'
    }),
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], Notifications.prototype, "recipient_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Recipient email address',
        example: '<EMAIL>'
    }),
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], Notifications.prototype, "recipient_email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Recipient phone number',
        example: '+************'
    }),
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Notifications.prototype, "recipient_phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notification subject/title',
        example: 'Application Status Update'
    }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Notifications.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notification message content',
        example: 'Your application has been approved.'
    }),
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Notifications.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'HTML content for email notifications'
    }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Notifications.prototype, "html_content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Entity type this notification relates to',
        example: 'application'
    }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Notifications.prototype, "entity_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Entity ID this notification relates to',
        example: 'application-uuid-here'
    }),
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], Notifications.prototype, "entity_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata as JSON'
    }),
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], Notifications.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'External provider message ID'
    }),
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Notifications.prototype, "external_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Error message if notification failed'
    }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Notifications.prototype, "error_message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of retry attempts'
    }),
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Notifications.prototype, "retry_count", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Notifications.prototype, "is_read", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'When notification was sent'
    }),
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Notifications.prototype, "sent_at", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'When notification was delivered'
    }),
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Notifications.prototype, "delivered_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Notifications.prototype, "action_url", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Notifications.prototype, "expires_at", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Notifications.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Notifications.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Notifications.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Notifications.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], Notifications.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Notifications.prototype, "read_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'recipient_id' }),
    __metadata("design:type", user_entity_1.User)
], Notifications.prototype, "recipient", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], Notifications.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], Notifications.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Notifications.prototype, "generateId", null);
exports.Notifications = Notifications = __decorate([
    (0, typeorm_1.Entity)('notifications')
], Notifications);
//# sourceMappingURL=notifications.entity.js.map