{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/applications/%5Blicense-type%5D/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next';\n\nexport const metadata: Metadata = {\n  title: 'License Applications - MACRA Digital Portal',\n  description: 'Manage license applications for various service types',\n};\n\nexport default function LicenseTypeLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,kBAAkB,EACxC,QAAQ,EAGT;IACC,qBAAO;kBAAG;;AACZ", "debugId": null}}]}