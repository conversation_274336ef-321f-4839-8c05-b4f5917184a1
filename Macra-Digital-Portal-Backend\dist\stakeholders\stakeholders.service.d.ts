import { Repository } from 'typeorm';
import { Stakeholder } from 'src/entities/stakeholders.entity';
import { CreateStakeholderDto } from 'src/dto/stakeholder/create-stakeholder.dto';
import { UpdateStakeholderDto } from 'src/dto/stakeholder/update-stakeholder.dto';
export declare class StakeholdersService {
    private readonly stakeholderRepository;
    constructor(stakeholderRepository: Repository<Stakeholder>);
    create(dto: CreateStakeholderDto, createdBy: string): Promise<Stakeholder>;
    findAll(): Promise<Stakeholder[]>;
    findOne(id: string): Promise<Stakeholder>;
    findByApplicant(applicationId: string): Promise<Stakeholder[]>;
    update(id: string, dto: UpdateStakeholderDto, updatedBy: string): Promise<Stakeholder>;
    softDelete(id: string): Promise<void>;
}
