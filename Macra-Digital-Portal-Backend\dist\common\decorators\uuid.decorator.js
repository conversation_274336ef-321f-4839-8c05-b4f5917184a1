"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UuidPrimaryColumn = UuidPrimaryColumn;
exports.UuidColumn = UuidColumn;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
function UuidPrimaryColumn() {
    return function (target, propertyKey) {
        (0, typeorm_1.PrimaryGeneratedColumn)('uuid')(target, propertyKey);
        const originalBeforeInsert = target.constructor.prototype.beforeInsert;
        target.constructor.prototype.beforeInsert = function () {
            if (!this[propertyKey]) {
                this[propertyKey] = (0, uuid_1.v4)();
            }
            if (originalBeforeInsert) {
                originalBeforeInsert.call(this);
            }
        };
        (0, typeorm_1.BeforeInsert)()(target, 'beforeInsert');
    };
}
function UuidColumn() {
    return function (target, propertyKey) {
        const { Column } = require('typeorm');
        Column({
            type: 'varchar',
            length: 36,
            primary: true,
            unique: true,
        })(target, propertyKey);
        const originalBeforeInsert = target.constructor.prototype.beforeInsert;
        target.constructor.prototype.beforeInsert = function () {
            if (!this[propertyKey]) {
                this[propertyKey] = (0, uuid_1.v4)();
            }
            if (originalBeforeInsert) {
                originalBeforeInsert.call(this);
            }
        };
        (0, typeorm_1.BeforeInsert)()(target, 'beforeInsert');
    };
}
//# sourceMappingURL=uuid.decorator.js.map