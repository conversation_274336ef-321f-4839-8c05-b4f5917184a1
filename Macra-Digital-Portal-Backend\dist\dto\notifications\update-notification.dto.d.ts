import { NotificationStatus } from '../../entities/notifications.entity';
import { CreateNotificationDto } from './create-notification.dto';
declare const UpdateNotificationDto_base: import("@nestjs/common").Type<Partial<CreateNotificationDto>>;
export declare class UpdateNotificationDto extends UpdateNotificationDto_base {
    status?: NotificationStatus;
    external_id?: string;
    error_message?: string;
    retry_count?: number;
    is_read?: boolean;
    sent_at?: Date;
    delivered_at?: Date;
    read_at?: Date;
}
export {};
