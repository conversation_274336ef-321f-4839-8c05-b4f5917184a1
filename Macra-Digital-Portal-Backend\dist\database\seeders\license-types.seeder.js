"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const license_types_entity_1 = require("../../entities/license-types.entity");
class LicenseTypesSeeder {
    async run(dataSource) {
        const repository = dataSource.getRepository(license_types_entity_1.LicenseTypes);
        const existingCount = await repository.count();
        if (existingCount > 0) {
            console.log('License types already exist, skipping seeder...');
            return;
        }
        const licenseTypes = [
            {
                name: 'Telecommunications',
                code: 'telecommunications',
                description: 'Licenses for telecommunications services including mobile networks, fixed networks, internet services, and broadcasting',
                validity: 5,
            },
            {
                name: 'Postal Services',
                code: 'postal_services',
                description: 'Licenses for postal and courier services including domestic and international mail delivery',
                validity: 3,
            },
            {
                name: 'Standards Compliance',
                code: 'standards_compliance',
                description: 'Certificates for standards compliance including type approval, equipment certification, and technical standards',
                validity: 2,
            },
            {
                name: 'Broadcasting',
                code: 'broadcasting',
                description: 'Licenses for radio and television broadcasting services',
                validity: 5,
            },
            {
                name: 'Spectrum Management',
                code: 'spectrum_management',
                description: 'Licenses for radio frequency spectrum allocation and management',
                validity: 10,
            },
        ];
        console.log('Seeding license types...');
        for (const licenseTypeData of licenseTypes) {
            const licenseType = repository.create(licenseTypeData);
            await repository.save(licenseType);
            console.log(`✅ Created license type: ${licenseTypeData.name}`);
        }
        console.log('License types seeding completed!');
    }
}
exports.default = LicenseTypesSeeder;
//# sourceMappingURL=license-types.seeder.js.map