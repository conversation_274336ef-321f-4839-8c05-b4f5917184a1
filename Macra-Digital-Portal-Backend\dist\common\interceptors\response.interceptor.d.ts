import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
export interface StandardResponse<T = any> {
    success: boolean;
    message?: string;
    data?: T;
    meta?: any;
    timestamp: string;
    path: string;
    statusCode: number;
}
export declare class ResponseInterceptor<T> implements NestInterceptor<T, StandardResponse<T>> {
    private readonly logger;
    intercept(context: ExecutionContext, next: CallHandler): Observable<StandardResponse<T>>;
    private wrapResponse;
    private isStandardResponse;
    private isPaginated;
    private getSuccessMessage;
    private extractResource;
    private capitalizeWords;
}
