import { EvaluationType, EvaluationStatus, EvaluationRecommendation } from '../../entities/evaluations.entity';
export declare class CreateEvaluationCriteriaDto {
    category: string;
    subcategory: string;
    score: number;
    weight: number;
    max_marks?: number;
    awarded_marks?: number;
}
export declare class CreateEvaluationDto {
    application_id: string;
    evaluator_id: string;
    evaluation_type: EvaluationType;
    status?: EvaluationStatus;
    total_score: number;
    recommendation: EvaluationRecommendation;
    evaluators_notes?: string;
    shareholding_compliance?: boolean;
    criteria?: CreateEvaluationCriteriaDto[];
}
