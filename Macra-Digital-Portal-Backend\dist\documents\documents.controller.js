"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const swagger_1 = require("@nestjs/swagger");
const documents_service_1 = require("./documents.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_document_dto_1 = require("../dto/document/create-document.dto");
const update_document_dto_1 = require("../dto/document/update-document.dto");
const documents_entity_1 = require("../entities/documents.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let DocumentsController = class DocumentsController {
    documentsService;
    constructor(documentsService) {
        this.documentsService = documentsService;
    }
    async create(createDocumentDto, req) {
        return this.documentsService.create(createDocumentDto, req.user.userId);
    }
    async uploadFile(file, uploadData, req) {
        if (!file) {
            throw new Error('No file uploaded');
        }
        const createDocumentDto = {
            document_type: uploadData.document_type || 'OTHER',
            file_name: uploadData.file_name || file.originalname,
            entity_type: uploadData.entity_type,
            entity_id: uploadData.entity_id,
            file_path: file.path,
            file_size: file.size,
            mime_type: file.mimetype,
            is_required: uploadData.is_required === 'true',
            application_id: uploadData.application_id || null,
        };
        const document = await this.documentsService.create(createDocumentDto, req.user.userId);
        return {
            success: true,
            message: 'Document uploaded successfully',
            data: document,
        };
    }
    async findAll(query, req) {
        const userRoles = req.user?.roles || [];
        const userId = req.user?.userId;
        const result = await this.documentsService.findAll(query, userRoles, userId);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async getStats() {
        return this.documentsService.getDocumentStats();
    }
    async getTotalFileSize() {
        const totalSize = await this.documentsService.getTotalFileSize();
        return { totalSize };
    }
    async findByApplication(applicationId) {
        return this.documentsService.findByApplication(applicationId);
    }
    async findByEntity(entityType, entityId) {
        return this.documentsService.findByEntity(entityType, entityId);
    }
    async findByDocumentType(documentType) {
        return this.documentsService.findByDocumentType(documentType);
    }
    async findByMimeType(mimeType) {
        return this.documentsService.getDocumentsByMimeType(mimeType);
    }
    async findRequiredDocuments() {
        return this.documentsService.findRequiredDocuments();
    }
    async findOne(id) {
        return this.documentsService.findOne(id);
    }
    async update(id, updateDocumentDto, req) {
        return this.documentsService.update(id, updateDocumentDto, req.user.userId);
    }
    async downloadDocument(id, res) {
        const document = await this.documentsService.findOne(id);
        const fileStream = await this.documentsService.getFileStream(document.file_path);
        res.set({
            'Content-Type': document.mime_type,
            'Content-Disposition': `attachment; filename="${document.file_name}"`,
        });
        fileStream.pipe(res);
    }
    async previewDocument(id, res) {
        const document = await this.documentsService.findOne(id);
        const previewableMimeTypes = [
            'application/pdf',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp',
            'text/plain',
            'text/html',
            'text/css',
            'text/javascript',
            'application/json',
        ];
        if (!previewableMimeTypes.includes(document.mime_type)) {
            res.status(400).json({
                message: 'Document type not supported for preview',
                supportedTypes: previewableMimeTypes
            });
            return;
        }
        const fileStream = await this.documentsService.getFileStream(document.file_path);
        res.set({
            'Content-Type': document.mime_type,
            'Content-Disposition': `inline; filename="${document.file_name}"`,
        });
        fileStream.pipe(res);
    }
    async remove(id) {
        await this.documentsService.remove(id);
        return { message: 'Document deleted successfully' };
    }
};
exports.DocumentsController = DocumentsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new document' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Document created successfully',
        type: documents_entity_1.Documents,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Created new document',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_document_dto_1.CreateDocumentDto, Object]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('upload'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload a document file' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Document uploaded successfully',
        type: documents_entity_1.Documents,
    }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        storage: (0, multer_1.diskStorage)({
            destination: './uploads/documents',
            filename: (req, file, callback) => {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
                const ext = (0, path_1.extname)(file.originalname);
                const filename = `${file.fieldname}-${uniqueSuffix}${ext}`;
                callback(null, filename);
            },
        }),
        fileFilter: (req, file, callback) => {
            const allowedMimes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/jpeg',
                'image/png',
                'image/gif',
                'text/plain',
            ];
            if (allowedMimes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new Error('Invalid file type'), false);
            }
        },
        limits: {
            fileSize: 10 * 1024 * 1024,
        },
    })),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Uploaded document file',
    }),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all documents with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Documents retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed documents list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get document statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document statistics retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed document statistics',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('total-file-size'),
    (0, swagger_1.ApiOperation)({ summary: 'Get total file size of all documents' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Total file size retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed total file size',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "getTotalFileSize", null);
__decorate([
    (0, common_1.Get)('by-application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get documents by application' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Documents retrieved successfully',
        type: [documents_entity_1.Documents],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed documents by application',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "findByApplication", null);
__decorate([
    (0, common_1.Get)('by-entity/:entityType/:entityId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get documents by entity' }),
    (0, swagger_1.ApiParam)({ name: 'entityType', description: 'Entity type' }),
    (0, swagger_1.ApiParam)({ name: 'entityId', description: 'Entity UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Documents retrieved successfully',
        type: [documents_entity_1.Documents],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed documents by entity',
    }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "findByEntity", null);
__decorate([
    (0, common_1.Get)('by-document-type/:documentType'),
    (0, swagger_1.ApiOperation)({ summary: 'Get documents by document type' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Documents retrieved successfully',
        type: [documents_entity_1.Documents],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed documents by type',
    }),
    __param(0, (0, common_1.Param)('documentType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "findByDocumentType", null);
__decorate([
    (0, common_1.Get)('by-mime-type/:mimeType'),
    (0, swagger_1.ApiOperation)({ summary: 'Get documents by MIME type' }),
    (0, swagger_1.ApiParam)({ name: 'mimeType', description: 'MIME type' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Documents retrieved successfully',
        type: [documents_entity_1.Documents],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed documents by MIME type',
    }),
    __param(0, (0, common_1.Param)('mimeType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "findByMimeType", null);
__decorate([
    (0, common_1.Get)('required'),
    (0, swagger_1.ApiOperation)({ summary: 'Get required documents' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Required documents retrieved successfully',
        type: [documents_entity_1.Documents],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed required documents',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "findRequiredDocuments", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get document by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Document UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document retrieved successfully',
        type: documents_entity_1.Documents,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Viewed document details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update document' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Document UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document updated successfully',
        type: documents_entity_1.Documents,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Updated document',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_document_dto_1.UpdateDocumentDto, Object]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "update", null);
__decorate([
    (0, common_1.Get)(':id/download'),
    (0, swagger_1.ApiOperation)({ summary: 'Download document' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Document UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document downloaded successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Downloaded document',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "downloadDocument", null);
__decorate([
    (0, common_1.Get)(':id/preview'),
    (0, swagger_1.ApiOperation)({ summary: 'Preview document' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Document UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document preview retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Previewed document',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "previewDocument", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete document' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Document UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.DOCUMENT_MANAGEMENT,
        resourceType: 'Document',
        description: 'Deleted document',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentsController.prototype, "remove", null);
exports.DocumentsController = DocumentsController = __decorate([
    (0, swagger_1.ApiTags)('Documents'),
    (0, common_1.Controller)('documents'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [documents_service_1.DocumentsService])
], DocumentsController);
//# sourceMappingURL=documents.controller.js.map