import { User } from './user.entity';
export declare class Address {
    address_id: string;
    address_line_1: string;
    address_type: string;
    entity_type: string;
    entity_id: string;
    address_line_2?: string;
    address_line_3?: string;
    postal_code: string;
    country: string;
    city: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    creator: User;
    updater?: User;
    generateId(): void;
}
