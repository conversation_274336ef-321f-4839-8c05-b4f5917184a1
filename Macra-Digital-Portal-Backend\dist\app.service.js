"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const postal_code_entity_1 = require("./entities/postal-code.entity");
let AppService = class AppService {
    postalCodeRepository;
    constructor(postalCodeRepository) {
        this.postalCodeRepository = postalCodeRepository;
    }
    getHello() {
        return 'Hello World!';
    }
    async searchPostalCodes(searchCode) {
        const { region, district, location, postal_code } = searchCode;
        if (!region) {
            throw new common_1.BadRequestException({
                success: false,
                message: 'Region is required!',
                data: null,
            });
        }
        const query = this.postalCodeRepository.createQueryBuilder('postal');
        query.where('LOWER(postal.region) = :region', { region: region.toLowerCase() });
        if (district?.trim()) {
            query.andWhere('LOWER(postal.district) = :district', {
                district: district.toLowerCase(),
            });
        }
        if (location?.trim()) {
            query.andWhere('LOWER(postal.location) = :location', {
                location: location.toLowerCase(),
            });
        }
        if (postal_code?.trim()) {
            query.andWhere('postal.postal_code = :postal_code', { postal_code });
        }
        const results = await query
            .orderBy('postal.region', 'ASC')
            .addOrderBy('postal.district', 'ASC')
            .addOrderBy('postal.location', 'ASC')
            .getMany();
        return {
            success: true,
            message: results.length
                ? 'Postal codes retrieved successfully'
                : 'No postal codes found for the given filters',
            data: results,
            meta: { total: results.length },
            timestamp: new Date().toISOString(),
            path: '/postal-codes/search',
            statusCode: 200,
        };
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(postal_code_entity_1.PostalCode)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AppService);
//# sourceMappingURL=app.service.js.map