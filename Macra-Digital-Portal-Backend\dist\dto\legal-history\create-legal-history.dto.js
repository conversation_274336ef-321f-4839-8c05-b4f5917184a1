"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLegalHistoryDto = void 0;
const class_validator_1 = require("class-validator");
class CreateLegalHistoryDto {
    application_id;
    criminal_history;
    criminal_details;
    bankruptcy_history;
    bankruptcy_details;
    regulatory_actions;
    regulatory_details;
    litigation_history;
    litigation_details;
    compliance_record;
    previous_licenses;
    declaration_accepted;
}
exports.CreateLegalHistoryDto = CreateLegalHistoryDto;
__decorate([
    (0, class_validator_1.IsUUID)('4', { message: 'Application ID not valid!' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Application ID is required' }),
    __metadata("design:type", String)
], CreateLegalHistoryDto.prototype, "application_id", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)({ message: 'Criminal history must be a boolean value' }),
    __metadata("design:type", Boolean)
], CreateLegalHistoryDto.prototype, "criminal_history", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Criminal details contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Criminal details must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateLegalHistoryDto.prototype, "criminal_details", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)({ message: 'Bankruptcy history must be a boolean value' }),
    __metadata("design:type", Boolean)
], CreateLegalHistoryDto.prototype, "bankruptcy_history", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Bankruptcy details contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Bankruptcy details must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateLegalHistoryDto.prototype, "bankruptcy_details", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)({ message: 'Regulatory actions must be a boolean value' }),
    __metadata("design:type", Boolean)
], CreateLegalHistoryDto.prototype, "regulatory_actions", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Regulatory details contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Regulatory details must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateLegalHistoryDto.prototype, "regulatory_details", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)({ message: 'Litigation history must be a boolean value' }),
    __metadata("design:type", Boolean)
], CreateLegalHistoryDto.prototype, "litigation_history", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Litigation details contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Litigation details must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateLegalHistoryDto.prototype, "litigation_details", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Compliance record contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Compliance record must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateLegalHistoryDto.prototype, "compliance_record", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Previous licenses contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Previous licenses must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateLegalHistoryDto.prototype, "previous_licenses", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)({ message: 'Declaration accepted must be a boolean value' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Declaration must be accepted' }),
    __metadata("design:type", Boolean)
], CreateLegalHistoryDto.prototype, "declaration_accepted", void 0);
//# sourceMappingURL=create-legal-history.dto.js.map