"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTasksTableStructure1736252000000 = void 0;
class UpdateTasksTableStructure1736252000000 {
    name = 'UpdateTasksTableStructure1736252000000';
    async up(queryRunner) {
        const tableExists = await queryRunner.hasTable('tasks');
        if (tableExists) {
            console.log('Tasks table exists, updating structure...');
            const hasEntityType = await queryRunner.hasColumn('tasks', 'entity_type');
            if (!hasEntityType) {
                await queryRunner.query(`ALTER TABLE tasks ADD COLUMN entity_type VARCHAR(50) NULL`);
                await queryRunner.query(`CREATE INDEX idx_tasks_entity_type ON tasks (entity_type)`);
            }
            const hasEntityId = await queryRunner.hasColumn('tasks', 'entity_id');
            if (!hasEntityId) {
                await queryRunner.query(`ALTER TABLE tasks ADD COLUMN entity_id VARCHAR(36) NULL`);
                await queryRunner.query(`CREATE INDEX idx_tasks_entity_id ON tasks (entity_id)`);
            }
            const hasReview = await queryRunner.hasColumn('tasks', 'review');
            if (!hasReview) {
                await queryRunner.query(`ALTER TABLE tasks ADD COLUMN review TEXT NULL`);
            }
            const hasCreatedBy = await queryRunner.hasColumn('tasks', 'created_by');
            if (!hasCreatedBy) {
                await queryRunner.query(`ALTER TABLE tasks ADD COLUMN created_by VARCHAR(36) NOT NULL`);
                await queryRunner.query(`CREATE INDEX idx_tasks_created_by ON tasks (created_by)`);
                await queryRunner.query(`
          ALTER TABLE tasks 
          ADD CONSTRAINT fk_tasks_created_by 
          FOREIGN KEY (created_by) REFERENCES users (user_id)
        `);
            }
            const hasUpdatedBy = await queryRunner.hasColumn('tasks', 'updated_by');
            if (!hasUpdatedBy) {
                await queryRunner.query(`ALTER TABLE tasks ADD COLUMN updated_by VARCHAR(36) NULL`);
                await queryRunner.query(`
          ALTER TABLE tasks 
          ADD CONSTRAINT fk_tasks_updated_by 
          FOREIGN KEY (updated_by) REFERENCES users (user_id)
        `);
            }
            await queryRunner.query(`
        ALTER TABLE tasks 
        MODIFY COLUMN task_type VARCHAR(50) DEFAULT 'application'
      `);
            await queryRunner.query(`
        ALTER TABLE tasks 
        MODIFY COLUMN status VARCHAR(50) DEFAULT 'pending'
      `);
            await queryRunner.query(`
        ALTER TABLE tasks 
        MODIFY COLUMN priority VARCHAR(50) DEFAULT 'medium'
      `);
            const hasApplicationId = await queryRunner.hasColumn('tasks', 'application_id');
            if (hasApplicationId) {
                try {
                    await queryRunner.query(`ALTER TABLE tasks DROP FOREIGN KEY fk_tasks_application_id`);
                }
                catch (error) {
                    console.log('Foreign key constraint fk_tasks_application_id does not exist, continuing...');
                }
                try {
                    await queryRunner.query(`DROP INDEX idx_tasks_application_id ON tasks`);
                }
                catch (error) {
                    console.log('Index idx_tasks_application_id does not exist, continuing...');
                }
                await queryRunner.query(`ALTER TABLE tasks DROP COLUMN application_id`);
            }
            console.log('Tasks table structure updated successfully!');
        }
        else {
            console.log('Tasks table does not exist, it will be created by the create-tasks-table migration.');
        }
    }
    async down(queryRunner) {
        console.log('This migration cannot be easily rolled back as it modifies existing data structure.');
    }
}
exports.UpdateTasksTableStructure1736252000000 = UpdateTasksTableStructure1736252000000;
//# sourceMappingURL=1736252000000-update-tasks-table-structure.js.map