import { User } from './user.entity';
import { Applications } from './applications.entity';
export declare class LegalHistory {
    legal_history_id: string;
    application_id: string;
    criminal_history: boolean;
    criminal_details?: string;
    bankruptcy_history: boolean;
    bankruptcy_details?: string;
    regulatory_actions: boolean;
    regulatory_details?: string;
    litigation_history: boolean;
    litigation_details?: string;
    compliance_record?: string;
    previous_licenses?: string;
    declaration_accepted: boolean;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    application: Applications;
    creator: User;
    updater?: User;
    generateId(): void;
}
