"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchPostalCodeDTO = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class SearchPostalCodeDTO {
    region;
    district;
    location;
    postal_code;
}
exports.SearchPostalCodeDTO = SearchPostalCodeDTO;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "The input region - required",
        example: "Southern"
    }),
    (0, class_validator_1.IsString)({ message: "Invalid region name!" }),
    (0, class_validator_1.IsNotEmpty)({ message: "Region is required!" }),
    __metadata("design:type", String)
], SearchPostalCodeDTO.prototype, "region", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Input district",
        example: "Blantyre"
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: "Invalid district name!" }),
    __metadata("design:type", String)
], SearchPostalCodeDTO.prototype, "district", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Input location",
        example: "Chichiri"
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: "Invalid location name!" }),
    __metadata("design:type", String)
], SearchPostalCodeDTO.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Input postal code",
        example: "312225"
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: "Invalid postal code!" }),
    __metadata("design:type", String)
], SearchPostalCodeDTO.prototype, "postal_code", void 0);
//# sourceMappingURL=search.dto.js.map