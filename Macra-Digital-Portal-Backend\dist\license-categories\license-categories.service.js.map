{"version": 3, "file": "license-categories.service.js", "sourceRoot": "", "sources": ["../../src/license-categories/license-categories.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAkF;AAClF,6CAAmD;AACnD,qCAA6C;AAC7C,qFAA0E;AAC1E,2EAAgE;AAGhE,qDAA0E;AAC1E,oFAAmG;AAG5F,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAGzB;IAEA;IAJV,YAEU,2BAA0D,EAE1D,sBAAgD;QAFhD,gCAA2B,GAA3B,2BAA2B,CAA+B;QAE1D,2BAAsB,GAAtB,sBAAsB,CAA0B;IACvD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAoB;QAChC,MAAM,MAAM,GAAsC;YAChD,eAAe,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC;YAC9C,iBAAiB,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,CAAC;YACxD,aAAa,EAAE,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACvC,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,GAAG;YACb,iBAAiB,EAAE;gBACjB,eAAe,EAAE,IAAI;aACtB;YACD,SAAS,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC;SACxE,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC;QAC/E,OAAO,4CAAqB,CAAC,SAAS,CAAoB,MAAM,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACrE,KAAK,EAAE,EAAE,mBAAmB,EAAE,EAAE,EAAE;YAClC,SAAS,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC;SACxE,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,aAAqB;QAC3C,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;YAC3C,KAAK,EAAE,EAAE,eAAe,EAAE,aAAa,EAAE;YACzC,SAAS,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC;SACxE,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,wBAAkD,EAAE,MAAc;QAE7E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC5D,KAAK,EAAE,EAAE,eAAe,EAAE,wBAAwB,CAAC,eAAe,EAAE;SACrE,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,CAAC,CAAC;QACxD,CAAC;QAGD,IAAI,wBAAwB,CAAC,SAAS,EAAE,CAAC;YACvC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;gBACpE,KAAK,EAAE,EAAE,mBAAmB,EAAE,wBAAwB,CAAC,SAAS,EAAE;aACnE,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,CAAC,CAAC;YAC3D,CAAC;YAGD,IAAI,cAAc,CAAC,eAAe,KAAK,wBAAwB,CAAC,eAAe,EAAE,CAAC;gBAChF,MAAM,IAAI,0BAAiB,CAAC,sDAAsD,CAAC,CAAC;YACtF,CAAC;QACH,CAAC;QAGD,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAC7E,KAAK,EAAE;gBACL,IAAI,EAAE,wBAAwB,CAAC,IAAI;gBACnC,eAAe,EAAE,wBAAwB,CAAC,eAAe;aAC1D;SACF,CAAC,CAAC;QAEH,IAAI,uBAAuB,EAAE,CAAC;YAC5B,MAAM,IAAI,0BAAiB,CAAC,sEAAsE,CAAC,CAAC;QACtG,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC;YAC9D,GAAG,wBAAwB;YAC3B,UAAU,EAAE,MAAM;SACnB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,wBAAkD,EAAE,MAAc;QACzF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAG/C,IAAI,wBAAwB,CAAC,eAAe,EAAE,CAAC;YAC7C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBAC5D,KAAK,EAAE,EAAE,eAAe,EAAE,wBAAwB,CAAC,eAAe,EAAE;aACrE,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAGD,IAAI,wBAAwB,CAAC,SAAS,EAAE,CAAC;YAEvC,IAAI,wBAAwB,CAAC,SAAS,KAAK,EAAE,EAAE,CAAC;gBAC9C,MAAM,IAAI,0BAAiB,CAAC,mCAAmC,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;gBACpE,KAAK,EAAE,EAAE,mBAAmB,EAAE,wBAAwB,CAAC,SAAS,EAAE;aACnE,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,CAAC,CAAC;YAC3D,CAAC;YAGD,MAAM,aAAa,GAAG,wBAAwB,CAAC,eAAe,IAAI,eAAe,CAAC,eAAe,CAAC;YAClG,IAAI,cAAc,CAAC,eAAe,KAAK,aAAa,EAAE,CAAC;gBACrD,MAAM,IAAI,0BAAiB,CAAC,sDAAsD,CAAC,CAAC;YACtF,CAAC;YAGD,MAAM,IAAI,CAAC,2BAA2B,CAAC,EAAE,EAAE,wBAAwB,CAAC,SAAS,CAAC,CAAC;QACjF,CAAC;QAGD,IAAI,wBAAwB,CAAC,IAAI,IAAI,wBAAwB,CAAC,eAAe,EAAE,CAAC;YAC9E,MAAM,WAAW,GAAG,wBAAwB,CAAC,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC;YAC1E,MAAM,oBAAoB,GAAG,wBAAwB,CAAC,eAAe,IAAI,eAAe,CAAC,eAAe,CAAC;YAEzG,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;gBAC7E,KAAK,EAAE;oBACL,IAAI,EAAE,WAAW;oBACjB,eAAe,EAAE,oBAAoB;iBACtC;aACF,CAAC,CAAC;YAEH,IAAI,uBAAuB,IAAI,uBAAuB,CAAC,mBAAmB,KAAK,EAAE,EAAE,CAAC;gBAClF,MAAM,IAAI,0BAAiB,CAAC,sEAAsE,CAAC,CAAC;YACtG,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE,EAAE;YAChD,GAAG,wBAAwB;YAC3B,UAAU,EAAE,MAAM;SACnB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC/C,MAAM,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IACxD,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,aAAqB;QAC5C,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;YAC3C,KAAK,EAAE;gBACL,eAAe,EAAE,aAAa;gBAC9B,SAAS,EAAE,IAAA,gBAAM,GAAE;aACpB;YACD,SAAS,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;YAC7D,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,aAAqB;QAC1C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAGpE,KAAK,MAAM,QAAQ,IAAI,cAAc,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAKO,KAAK,CAAC,oBAAoB,CAAC,QAA2B;QAC5D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;YAC3D,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,mBAAmB,EAAE;YAClD,SAAS,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,CAAC;YACjD,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;SACvB,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAG7B,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,2BAA2B,CAAC,UAAkB,EAAE,QAAgB;QAC5E,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAClC,IAAI,SAAS,GAAuB,QAAQ,CAAC;QAE7C,OAAO,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5C,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;gBAC7B,MAAM,IAAI,0BAAiB,CAAC,uDAAuD,CAAC,CAAC;YACvF,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAEvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;gBAC5D,KAAK,EAAE,EAAE,mBAAmB,EAAE,SAAS,EAAE;gBACzC,MAAM,EAAE,CAAC,WAAW,CAAC;aACtB,CAAC,CAAC;YAEH,SAAS,GAAG,MAAM,EAAE,SAAS,IAAI,SAAS,CAAC;QAC7C,CAAC;IACH,CAAC;IAMD,KAAK,CAAC,gCAAgC,CAAC,aAAqB,EAAE,iBAA0B;QACtF,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,yDAAyD,EAAE,aAAa,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;YAE/H,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,UAAU,CAAC;iBACjF,iBAAiB,CAAC,iBAAiB,EAAE,QAAQ,CAAC;iBAC9C,KAAK,CAAC,2CAA2C,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;YAEzE,IAAI,iBAAiB,EAAE,CAAC;gBACtB,YAAY,CAAC,QAAQ,CAAC,oDAAoD,EAAE,EAAE,iBAAiB,EAAE,CAAC,CAAC;YACrG,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,YAAY;iBAC9B,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC;iBAC/B,OAAO,EAAE,CAAC;YAEb,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5K,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,aAAqB,EAAE,iBAA0B;QAC1E,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,UAAU,CAAC;aACjF,KAAK,CAAC,2CAA2C,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QAEzE,IAAI,iBAAiB,EAAE,CAAC;YACtB,YAAY,CAAC,QAAQ,CAAC,oDAAoD,EAAE,EAAE,iBAAiB,EAAE,CAAC,CAAC;QACrG,CAAC;QAED,OAAO,YAAY;aAChB,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC;aAC/B,OAAO,EAAE,CAAC;IACf,CAAC;CACF,CAAA;AAhRY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;qCADM,oBAAU;QAEf,oBAAU;GALjC,wBAAwB,CAgRpC"}