import { Repository } from 'typeorm';
import { ContactPersons } from '../entities/contact-persons.entity';
import { CreateContactPersonDto } from '../dto/contact-person/create-contact-person.dto';
import { UpdateContactPersonDto } from '../dto/contact-person/update-contact-person.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
export declare class ContactPersonsService {
    private contactPersonsRepository;
    constructor(contactPersonsRepository: Repository<ContactPersons>);
    private readonly paginateConfig;
    create(createContactPersonDto: CreateContactPersonDto, createdBy: string): Promise<ContactPersons>;
    findAll(query: PaginateQuery): Promise<Paginated<ContactPersons>>;
    findOne(id: string): Promise<ContactPersons>;
    findByApplicationId(applicationId: string): Promise<ContactPersons[]>;
    findPrimaryByApplicationId(applicationId: string): Promise<ContactPersons | null>;
    findByEmail(email: string): Promise<ContactPersons | null>;
    findByPhone(phone: string): Promise<ContactPersons | null>;
    update(id: string, updateContactPersonDto: UpdateContactPersonDto, updatedBy: string): Promise<ContactPersons>;
    remove(id: string): Promise<void>;
    search(searchTerm: string): Promise<ContactPersons[]>;
    setPrimaryContact(applicationId: string, contactPersonId: string, updatedBy: string): Promise<ContactPersons>;
    getContactPersonsByApplicant(applicationId: string): Promise<{
        primary: ContactPersons | null;
        secondary: ContactPersons[];
    }>;
}
