import { User } from './user.entity';
import { Applications } from './applications.entity';
export declare class ContactPersons {
    contact_id: string;
    entity_type: string;
    entity_id: string;
    first_name: string;
    application_id: string;
    last_name: string;
    middle_name?: string;
    designation: string;
    email: string;
    phone: string;
    is_primary: boolean;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    application: Applications;
    creator: User;
    updater?: User;
    generateId(): void;
}
