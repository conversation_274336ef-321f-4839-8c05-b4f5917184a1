import { StakeholdersService } from './stakeholders.service';
import { CreateStakeholderDto } from 'src/dto/stakeholder/create-stakeholder.dto';
import { UpdateStakeholderDto } from 'src/dto/stakeholder/update-stakeholder.dto';
import { Stakeholder } from 'src/entities/stakeholders.entity';
export declare class StakeholdersController {
    private readonly stakeholderService;
    constructor(stakeholderService: StakeholdersService);
    create(createDto: CreateStakeholderDto, req: any): Promise<Stakeholder>;
    findAll(): Promise<Stakeholder[]>;
    findByApplicant(applicantId: string): Promise<Stakeholder[]>;
    findByApplication(applicationId: string): Promise<Stakeholder[]>;
    findOne(id: string): Promise<Stakeholder>;
    update(id: string, updateDto: UpdateStakeholderDto, req: any): Promise<Stakeholder>;
    remove(id: string): Promise<void>;
}
