import { User } from './user.entity';
import { Applications } from './applications.entity';
export declare class Stakeholder {
    stakeholder_id: string;
    application_id: string;
    first_name: string;
    last_name: string;
    middle_name?: string;
    nationality: string;
    position: string;
    profile: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    application: Applications;
    creator: User;
    updater?: User;
    generateId(): void;
}
