import { AppService } from './app.service';
import { PostalCode } from './entities/postal-code.entity';
import { SearchPostalCodeDTO } from './dto/postal-code/search.dto';
export declare class AppController {
    private readonly appService;
    constructor(appService: AppService);
    getHello(): string;
    getHealth(): {
        status: string;
        timestamp: string;
        service: string;
        version: string;
    };
    searchPostalCodes(searchCode: SearchPostalCodeDTO): Promise<import("./common/interceptors/response.interceptor").StandardResponse<PostalCode[]>>;
}
