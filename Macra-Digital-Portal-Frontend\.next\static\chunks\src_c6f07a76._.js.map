{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/imageUtils.ts"], "sourcesContent": ["/**\r\n * Utility functions for handling profile images\r\n */\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n/**\r\n * Get the full URL for a profile image\r\n * @param profileImage - The profile image path from the API\r\n * @returns Full URL for the profile image\r\n */\r\nexport const getProfileImageUrl = (profileImage?: string): string | null => {\r\n  if (!profileImage) return null;\r\n  \r\n  // If it's already a full URL (starts with http), return as is\r\n  if (profileImage.startsWith('http://') || profileImage.startsWith('https://')) {\r\n    return profileImage;\r\n  }\r\n  \r\n  // If it's a relative path, prepend the API base URL\r\n  if (profileImage.startsWith('/')) {\r\n    return `${API_BASE_URL}${profileImage}`;\r\n  }\r\n  \r\n  // If it's just a filename, assume it's in the uploads directory\r\n  return `${API_BASE_URL}/uploads/avatars/${profileImage}`;\r\n};\r\n\r\n/**\r\n * Get user initials for fallback display\r\n * @param firstName - User's first name\r\n * @param lastName - User's last name\r\n * @returns Initials string (e.g., \"JD\")\r\n */\r\nexport const getUserInitials = (firstName?: string, lastName?: string): string => {\r\n  const first = firstName?.charAt(0)?.toUpperCase() || '';\r\n  const last = lastName?.charAt(0)?.toUpperCase() || '';\r\n  return `${first}${last}` || 'U';\r\n};\r\n\r\n/**\r\n * Validate image file for upload\r\n * @param file - File to validate\r\n * @returns Validation result with error message if invalid\r\n */\r\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\r\n  // Check file type\r\n  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return {\r\n      valid: false,\r\n      error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'\r\n    };\r\n  }\r\n\r\n  // Check file size (5MB max)\r\n  const maxSize = 5 * 1024 * 1024; // 5MB\r\n  if (file.size > maxSize) {\r\n    return {\r\n      valid: false,\r\n      error: 'File size too large. Maximum size is 5MB.'\r\n    };\r\n  }\r\n\r\n  return { valid: true };\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAEoB;AAArB,MAAM,eAAe,6DAAmC;AAOjD,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,cAAc,OAAO;IAE1B,8DAA8D;IAC9D,IAAI,aAAa,UAAU,CAAC,cAAc,aAAa,UAAU,CAAC,aAAa;QAC7E,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,aAAa,UAAU,CAAC,MAAM;QAChC,OAAO,GAAG,eAAe,cAAc;IACzC;IAEA,gEAAgE;IAChE,OAAO,GAAG,aAAa,iBAAiB,EAAE,cAAc;AAC1D;AAQO,MAAM,kBAAkB,CAAC,WAAoB;IAClD,MAAM,QAAQ,WAAW,OAAO,IAAI,iBAAiB;IACrD,MAAM,OAAO,UAAU,OAAO,IAAI,iBAAiB;IACnD,OAAO,GAAG,QAAQ,MAAM,IAAI;AAC9B;AAOO,MAAM,oBAAoB,CAAC;IAChC,kBAAkB;IAClB,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,UAAU,IAAI,OAAO,MAAM,MAAM;IACvC,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useNotifications.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useToast } from '@/contexts/ToastContext';\n\nexport interface Notification {\n  notification_id: string;\n  type: string;\n  status: string;\n  priority: string;\n  recipient_type: string;\n  recipient_id: string;\n  recipient_email?: string;\n  subject: string;\n  message: string;\n  entity_type?: string;\n  entity_id?: string;\n  is_read: boolean;\n  read_at?: string;\n  created_at: string;\n  updated_at: string;\n  action_url?: string;\n}\n\nexport interface NotificationResponse {\n  data: Notification[];\n  meta: {\n    total: number;\n    page: number;\n    limit: number;\n    totalPages: number;\n  };\n}\n\nexport interface UseNotificationsReturn {\n  notifications: Notification[];\n  unreadCount: number;\n  loading: boolean;\n  error: string | null;\n  fetchNotifications: () => Promise<void>;\n  markAsRead: (id: string) => Promise<void>;\n  markAllAsRead: () => Promise<void>;\n  refreshNotifications: () => Promise<void>;\n}\n\nexport const useNotifications = (): UseNotificationsReturn => {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const { user, isAuthenticated } = useAuth();\n  const { showError } = useToast();\n\n  const fetchNotifications = useCallback(async () => {\n    if (!isAuthenticated || !user) {\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const token = localStorage.getItem('auth_token');\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/notifications/my-notifications`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data: NotificationResponse = await response.json();\n      \n      if (data && data.data) {\n        setNotifications(data.data);\n        // Calculate unread count\n        const unread = data.data.filter(notification => !notification.is_read).length;\n        setUnreadCount(unread);\n      } else {\n        setNotifications([]);\n        setUnreadCount(0);\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';\n      setError(errorMessage);\n      console.error('Error fetching notifications:', err);\n      // Don't show toast error for initial load failures\n    } finally {\n      setLoading(false);\n    }\n  }, [isAuthenticated, user]);\n\n  const markAsRead = useCallback(async (id: string) => {\n    if (!isAuthenticated) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('auth_token');\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/notifications/${id}/mark-read`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      // Update local state\n      setNotifications(prev => \n        prev.map(notification => \n          notification.notification_id === id \n            ? { ...notification, is_read: true, read_at: new Date().toISOString() }\n            : notification\n        )\n      );\n\n      // Update unread count\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';\n      setError(errorMessage);\n      showError('Failed to mark notification as read');\n      console.error('Error marking notification as read:', err);\n    }\n  }, [isAuthenticated, showError]);\n\n  const markAllAsRead = useCallback(async () => {\n    if (!isAuthenticated || notifications.length === 0) {\n      return;\n    }\n\n    try {\n      // Mark all unread notifications as read\n      const unreadNotifications = notifications.filter(n => !n.is_read);\n      \n      for (const notification of unreadNotifications) {\n        await markAsRead(notification.notification_id);\n      }\n\n      setUnreadCount(0);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';\n      setError(errorMessage);\n      showError('Failed to mark all notifications as read');\n      console.error('Error marking all notifications as read:', err);\n    }\n  }, [isAuthenticated, notifications, markAsRead, showError]);\n\n  const refreshNotifications = useCallback(async () => {\n    await fetchNotifications();\n  }, [fetchNotifications]);\n\n  // Initial fetch when component mounts and user is authenticated\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      fetchNotifications();\n    }\n  }, [isAuthenticated, user, fetchNotifications]);\n\n  // Set up polling for new notifications every 30 seconds\n  useEffect(() => {\n    if (!isAuthenticated || !user) {\n      return;\n    }\n\n    const interval = setInterval(() => {\n      fetchNotifications();\n    }, 30000); // 30 seconds\n\n    return () => clearInterval(interval);\n  }, [isAuthenticated, user, fetchNotifications]);\n\n  return {\n    notifications,\n    unreadCount,\n    loading,\n    error,\n    fetchNotifications,\n    markAsRead,\n    markAllAsRead,\n    refreshNotifications,\n  };\n};\n"], "names": [], "mappings": ";;;AAgEsC;AA9DtC;AACA;AACA;;AAJA;;;;AA8CO,MAAM,mBAAmB;;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,+BAA+B,CAAC,EAAE;oBAC3H,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;gBAEtD,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACrB,iBAAiB,KAAK,IAAI;oBAC1B,yBAAyB;oBACzB,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM;4EAAC,CAAA,eAAgB,CAAC,aAAa,OAAO;2EAAE,MAAM;oBAC7E,eAAe;gBACjB,OAAO;oBACL,iBAAiB,EAAE;oBACnB,eAAe;gBACjB;YACF,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mDAAmD;YACrD,SAAU;gBACR,WAAW;YACb;QACF;2DAAG;QAAC;QAAiB;KAAK;IAE1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACpC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,eAAe,EAAE,GAAG,UAAU,CAAC,EAAE;oBAC1H,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,qBAAqB;gBACrB;gEAAiB,CAAA,OACf,KAAK,GAAG;wEAAC,CAAA,eACP,aAAa,eAAe,KAAK,KAC7B;oCAAE,GAAG,YAAY;oCAAE,SAAS;oCAAM,SAAS,IAAI,OAAO,WAAW;gCAAG,IACpE;;;gBAIR,sBAAsB;gBACtB;gEAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;;YAC5C,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;mDAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAChC,IAAI,CAAC,mBAAmB,cAAc,MAAM,KAAK,GAAG;gBAClD;YACF;YAEA,IAAI;gBACF,wCAAwC;gBACxC,MAAM,sBAAsB,cAAc,MAAM;uFAAC,CAAA,IAAK,CAAC,EAAE,OAAO;;gBAEhE,KAAK,MAAM,gBAAgB,oBAAqB;oBAC9C,MAAM,WAAW,aAAa,eAAe;gBAC/C;gBAEA,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;sDAAG;QAAC;QAAiB;QAAe;QAAY;KAAU;IAE1D,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,MAAM;QACR;6DAAG;QAAC;KAAmB;IAEvB,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,mBAAmB,MAAM;gBAC3B;YACF;QACF;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,MAAM,WAAW;uDAAY;oBAC3B;gBACF;sDAAG,QAAQ,aAAa;YAExB;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAjJa;;QAKuB,kIAAA,CAAA,UAAO;QACnB,mIAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Modal.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useEffect } from 'react';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\n  showCloseButton?: boolean;\n  closeOnOverlayClick?: boolean;\n}\n\nexport default function Modal({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true,\n  closeOnOverlayClick = true,\n}: ModalProps) {\n  // Handle escape key press\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when modal is open\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'sm:max-w-sm';\n      case 'md':\n        return 'sm:max-w-md';\n      case 'lg':\n        return 'sm:max-w-lg';\n      case 'xl':\n        return 'sm:max-w-xl';\n      case '2xl':\n        return 'sm:max-w-2xl';\n      default:\n        return 'sm:max-w-md';\n    }\n  };\n\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\n      <div\n        className=\"fixed inset-0\"\n        onClick={handleOverlayClick}\n        aria-hidden=\"true\"\n      />\n      \n      <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ${getSizeClasses()} mx-4 transform transition-all max-h-[90vh] overflow-hidden`}>\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n            {title}\n          </h3>\n          {showCloseButton && (\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\n              aria-label=\"Close modal\"\n            >\n              <i className=\"ri-close-line text-xl\"></i>\n            </button>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className=\"overflow-y-auto max-h-[calc(90vh-120px)]\">\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS,MAAM,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EACf;;IACX,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;wBACpC;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,yCAAyC;gBACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YACvD;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS;gBACT,eAAY;;;;;;0BAGd,6LAAC;gBAAI,WAAW,CAAC,+DAA+D,EAAE,iBAAiB,2DAA2D,CAAC;;kCAE7J,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,iCACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAzFwB;KAAA", "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationItem.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Notification } from '@/hooks/useNotifications';\n\ninterface NotificationItemProps {\n  notification: Notification;\n  onMarkAsRead: (id: string) => void;\n  onNotificationClick?: (notification: Notification) => void;\n}\n\nconst NotificationItem: React.FC<NotificationItemProps> = ({\n  notification,\n  onMarkAsRead,\n  onNotificationClick,\n}) => {\n  const formatTimeAgo = (dateString: string) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n    if (diffInSeconds < 60) {\n      return 'Just now';\n    } else if (diffInSeconds < 3600) {\n      const minutes = Math.floor(diffInSeconds / 60);\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 86400) {\n      const hours = Math.floor(diffInSeconds / 3600);\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 604800) {\n      const days = Math.floor(diffInSeconds / 86400);\n      return `${days} day${days > 1 ? 's' : ''} ago`;\n    } else {\n      return date.toLocaleDateString();\n    }\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type.toLowerCase()) {\n      case 'email':\n        return 'ri-mail-line';\n      case 'sms':\n        return 'ri-message-3-line';\n      case 'in_app':\n        return 'ri-notification-3-line';\n      case 'application_status':\n        return 'ri-file-list-3-line';\n      case 'evaluation_assigned':\n        return 'ri-user-settings-line';\n      case 'payment_due':\n        return 'ri-money-dollar-circle-line';\n      case 'license_expiry':\n        return 'ri-calendar-event-line';\n      case 'system_alert':\n        return 'ri-alert-line';\n      default:\n        return 'ri-notification-3-line';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority.toLowerCase()) {\n      case 'urgent':\n        return 'text-red-600 dark:text-red-400';\n      case 'high':\n        return 'text-orange-600 dark:text-orange-400';\n      case 'medium':\n        return 'text-yellow-600 dark:text-yellow-400';\n      case 'low':\n        return 'text-green-600 dark:text-green-400';\n      default:\n        return 'text-gray-600 dark:text-gray-400';\n    }\n  };\n\n  const handleItemClick = () => {\n    if (!notification.is_read) {\n      onMarkAsRead(notification.notification_id);\n    }\n    if (onNotificationClick) {\n      onNotificationClick(notification);\n    }\n  };\n\n  const handleMarkAsReadClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    onMarkAsRead(notification.notification_id);\n  };\n\n  return (\n    <div\n      className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ${\n        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''\n      }`}\n      onClick={handleItemClick}\n    >\n      <div className=\"flex items-start space-x-3\">\n        {/* Notification Icon */}\n        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\n          !notification.is_read \n            ? 'bg-blue-100 dark:bg-blue-900/50' \n            : 'bg-gray-100 dark:bg-gray-700'\n        }`}>\n          <i className={`${getNotificationIcon(notification.type)} text-sm ${\n            !notification.is_read \n              ? 'text-blue-600 dark:text-blue-400' \n              : 'text-gray-600 dark:text-gray-400'\n          }`}></i>\n        </div>\n\n        {/* Notification Content */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex-1\">\n              <h4 className={`text-sm font-medium ${\n                !notification.is_read \n                  ? 'text-gray-900 dark:text-gray-100' \n                  : 'text-gray-700 dark:text-gray-300'\n              }`}>\n                {notification.subject}\n              </h4>\n              <p className={`mt-1 text-sm ${\n                !notification.is_read \n                  ? 'text-gray-700 dark:text-gray-300' \n                  : 'text-gray-500 dark:text-gray-400'\n              }`}>\n                {notification.message}\n              </p>\n            </div>\n\n            {/* Priority Indicator */}\n            {notification.priority && notification.priority !== 'medium' && (\n              <div className={`flex-shrink-0 ml-2 ${getPriorityColor(notification.priority)}`}>\n                <i className=\"ri-flag-line text-xs\"></i>\n              </div>\n            )}\n          </div>\n\n          {/* Metadata */}\n          <div className=\"mt-2 flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\n              <span>{formatTimeAgo(notification.created_at)}</span>\n              {notification.entity_type && (\n                <span className=\"capitalize\">{notification.entity_type}</span>\n              )}\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex items-center space-x-2\">\n              {!notification.is_read && (\n                <button\n                  onClick={handleMarkAsReadClick}\n                  className=\"text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium\"\n                  title=\"Mark as read\"\n                >\n                  Mark as read\n                </button>\n              )}\n              {notification.is_read && (\n                <span className=\"text-xs text-green-600 dark:text-green-400 flex items-center\">\n                  <i className=\"ri-check-line mr-1\"></i>\n                  Read\n                </span>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Unread Indicator */}\n        {!notification.is_read && (\n          <div className=\"flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full\"></div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default NotificationItem;\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,MAAM,mBAAoD,CAAC,EACzD,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACpB;IACC,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;YAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;QACzD,OAAO,IAAI,gBAAgB,OAAO;YAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;QACnD,OAAO,IAAI,gBAAgB,QAAQ;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;QAChD,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,aAAa,aAAa,eAAe;QAC3C;QACA,IAAI,qBAAqB;YACvB,oBAAoB;QACtB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,EAAE,eAAe;QACjB,aAAa,aAAa,eAAe;IAC3C;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,wIAAwI,EAClJ,CAAC,aAAa,OAAO,GAAG,mCAAmC,IAC3D;QACF,SAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,CAAC,oEAAoE,EACnF,CAAC,aAAa,OAAO,GACjB,oCACA,gCACJ;8BACA,cAAA,6LAAC;wBAAE,WAAW,GAAG,oBAAoB,aAAa,IAAI,EAAE,SAAS,EAC/D,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;;;;;;;;;;;8BAIJ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;sDACC,aAAa,OAAO;;;;;;sDAEvB,6LAAC;4CAAE,WAAW,CAAC,aAAa,EAC1B,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;sDACC,aAAa,OAAO;;;;;;;;;;;;gCAKxB,aAAa,QAAQ,IAAI,aAAa,QAAQ,KAAK,0BAClD,6LAAC;oCAAI,WAAW,CAAC,mBAAmB,EAAE,iBAAiB,aAAa,QAAQ,GAAG;8CAC7E,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAM,cAAc,aAAa,UAAU;;;;;;wCAC3C,aAAa,WAAW,kBACvB,6LAAC;4CAAK,WAAU;sDAAc,aAAa,WAAW;;;;;;;;;;;;8CAK1D,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,aAAa,OAAO,kBACpB,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;sDACP;;;;;;wCAIF,aAAa,OAAO,kBACnB,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;gBAS/C,CAAC,aAAa,OAAO,kBACpB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;KApKM;uCAsKS", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Modal from '@/components/common/Modal';\nimport NotificationItem from './NotificationItem';\nimport Loader from '@/components/Loader';\nimport { useNotifications, Notification } from '@/hooks/useNotifications';\nimport { useToast } from '@/contexts/ToastContext';\n\ninterface NotificationModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst NotificationModal: React.FC<NotificationModalProps> = ({\n  isOpen,\n  onClose,\n}) => {\n  const {\n    notifications,\n    unreadCount,\n    loading,\n    error,\n    markAsRead,\n    markAllAsRead,\n    refreshNotifications,\n  } = useNotifications();\n  \n  const { showSuccess, showError } = useToast();\n  const [filter, setFilter] = useState<'all' | 'unread'>('all');\n  const [localLoading, setLocalLoading] = useState(false);\n\n  // Filter notifications based on selected filter\n  const filteredNotifications = notifications.filter(notification => {\n    if (filter === 'unread') {\n      return !notification.is_read;\n    }\n    return true;\n  });\n\n  // Refresh notifications when modal opens\n  useEffect(() => {\n    if (isOpen) {\n      refreshNotifications();\n    }\n  }, [isOpen, refreshNotifications]);\n\n  const handleMarkAsRead = async (id: string) => {\n    try {\n      await markAsRead(id);\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    if (unreadCount === 0) {\n      showError('No unread notifications to mark');\n      return;\n    }\n\n    setLocalLoading(true);\n    try {\n      await markAllAsRead();\n      showSuccess('All notifications marked as read');\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n      showError('Failed to mark all notifications as read');\n    } finally {\n      setLocalLoading(false);\n    }\n  };\n\n  const handleNotificationClick = (notification: Notification) => {\n    // Handle notification click - could navigate to related page\n    if (notification.action_url) {\n      window.location.href = notification.action_url;\n    } else if (notification.entity_type === 'application' && notification.entity_id) {\n      // Navigate to application details\n      window.location.href = `/customer/my-licenses?application_id=${notification.entity_id}`;\n    }\n  };\n\n  const handleRefresh = async () => {\n    setLocalLoading(true);\n    try {\n      await refreshNotifications();\n    } catch (error) {\n      console.error('Error refreshing notifications:', error);\n      showError('Failed to refresh notifications');\n    } finally {\n      setLocalLoading(false);\n    }\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"Notifications\"\n      size=\"lg\"\n    >\n      <div className=\"flex flex-col h-96\">\n        {/* Header with filters and actions */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center space-x-4\">\n            {/* Filter buttons */}\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={() => setFilter('all')}\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\n                  filter === 'all'\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\n                }`}\n              >\n                All ({notifications.length})\n              </button>\n              <button\n                onClick={() => setFilter('unread')}\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\n                  filter === 'unread'\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\n                }`}\n              >\n                Unread ({unreadCount})\n              </button>\n            </div>\n          </div>\n\n          {/* Action buttons */}\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={handleRefresh}\n              disabled={loading || localLoading}\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50\"\n              title=\"Refresh notifications\"\n            >\n              <i className={`ri-refresh-line ${(loading || localLoading) ? 'animate-spin' : ''}`}></i>\n            </button>\n            \n            {unreadCount > 0 && (\n              <button\n                onClick={handleMarkAllAsRead}\n                disabled={loading || localLoading}\n                className=\"px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50\"\n              >\n                Mark all as read\n              </button>\n            )}\n          </div>\n        </div>\n\n        {/* Notifications list */}\n        <div className=\"flex-1 overflow-y-auto\">\n          {loading && notifications.length === 0 ? (\n            <div className=\"flex items-center justify-center h-full\">\n              <Loader message=\"Loading notifications...\" />\n            </div>\n          ) : error ? (\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\n              <i className=\"ri-error-warning-line text-4xl text-red-500 mb-2\"></i>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\n                Error Loading Notifications\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                {error}\n              </p>\n              <button\n                onClick={handleRefresh}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200\"\n              >\n                Try Again\n              </button>\n            </div>\n          ) : filteredNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\n              <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-2\"></i>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\n                {filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {filter === 'unread' \n                  ? 'All caught up! You have no unread notifications.'\n                  : 'You have no notifications at this time.'\n                }\n              </p>\n            </div>\n          ) : (\n            <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n              {filteredNotifications.map((notification) => (\n                <NotificationItem\n                  key={notification.notification_id}\n                  notification={notification}\n                  onMarkAsRead={handleMarkAsRead}\n                  onNotificationClick={handleNotificationClick}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Footer */}\n        {filteredNotifications.length > 0 && (\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 text-center\">\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Showing {filteredNotifications.length} of {notifications.length} notifications\n            </p>\n          </div>\n        )}\n      </div>\n    </Modal>\n  );\n};\n\nexport default NotificationModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAcA,MAAM,oBAAsD,CAAC,EAC3D,MAAM,EACN,OAAO,EACR;;IACC,MAAM,EACJ,aAAa,EACb,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,EACV,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gDAAgD;IAChD,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,UAAU;YACvB,OAAO,CAAC,aAAa,OAAO;QAC9B;QACA,OAAO;IACT;IAEA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;sCAAG;QAAC;QAAQ;KAAqB;IAEjC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,GAAG;YACrB,UAAU;YACV;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,6DAA6D;QAC7D,IAAI,aAAa,UAAU,EAAE;YAC3B,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa,UAAU;QAChD,OAAO,IAAI,aAAa,WAAW,KAAK,iBAAiB,aAAa,SAAS,EAAE;YAC/E,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,qCAAqC,EAAE,aAAa,SAAS,EAAE;QACzF;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,wIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,4DAA4D,EACtE,WAAW,QACP,qEACA,iFACJ;;4CACH;4CACO,cAAc,MAAM;4CAAC;;;;;;;kDAE7B,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,4DAA4D,EACtE,WAAW,WACP,qEACA,iFACJ;;4CACH;4CACU;4CAAY;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCAAE,WAAW,CAAC,gBAAgB,EAAE,AAAC,WAAW,eAAgB,iBAAiB,IAAI;;;;;;;;;;;gCAGnF,cAAc,mBACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;8BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;+BAEhB,sBACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,6LAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;+BAID,sBAAsB,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CACX,WAAW,WAAW,4BAA4B;;;;;;0CAErD,6LAAC;gCAAE,WAAU;0CACV,WAAW,WACR,qDACA;;;;;;;;;;;6CAKR,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC,0JAAA,CAAA,UAAgB;gCAEf,cAAc;gCACd,cAAc;gCACd,qBAAqB;+BAHhB,aAAa,eAAe;;;;;;;;;;;;;;;gBAW1C,sBAAsB,MAAM,GAAG,mBAC9B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA2C;4BAC7C,sBAAsB,MAAM;4BAAC;4BAAK,cAAc,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO9E;GAxMM;;QAYA,mIAAA,CAAA,mBAAgB;QAEe,mIAAA,CAAA,WAAQ;;;KAdvC;uCA0MS", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\nimport UserMenu from './UserMenu';\r\nimport LogoutButton from './LogoutButton';\r\nimport { getUserInitials } from '../utils/imageUtils';\r\nimport { useNotifications } from '../hooks/useNotifications';\r\nimport NotificationModal from './notifications/NotificationModal';\r\n\r\ninterface HeaderProps {\r\n  activeTab?: string;\r\n  onTabChange?: (tab: string) => void;\r\n  onMobileMenuToggle?: () => void;\r\n}\r\n\r\nconst Header = ({ activeTab = 'overview', onTabChange, onMobileMenuToggle }: HeaderProps) => {\r\n  const { isAuthenticated, user } = useAuth();\r\n  const { unreadCount } = useNotifications();\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const [notificationModalOpen, setNotificationModalOpen] = useState(false);\r\n  const pathname = usePathname();\r\n\r\n  // Only show dashboard tabs when on dashboard routes\r\n  const showDashboardTabs = pathname.startsWith('/dashboard');\r\n\r\n  const toggleDropdown = () => {\r\n    setDropdownOpen(!dropdownOpen);\r\n  };\r\n\r\n  const toggleNotificationModal = () => {\r\n    setNotificationModalOpen(!notificationModalOpen);\r\n  };\r\n\r\n  const tabs = [\r\n    { id: 'overview', label: 'Overview' },\r\n    { id: 'licenses', label: 'Licenses' },\r\n    { id: 'users', label: 'Users' },\r\n    { id: 'transactions', label: 'Transactions' },\r\n    { id: 'spectrum', label: 'Spectrum' },\r\n    { id: 'compliance', label: 'Compliance' },\r\n  ];\r\n\r\n  return (\r\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n      <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n        <button\r\n          id=\"mobileMenuBtn\"\r\n          type=\"button\"\r\n          onClick={onMobileMenuToggle}\r\n          className=\"md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none\"\r\n        >\r\n          <div className=\"w-6 h-6 flex items-center justify-center\">\r\n            <i className=\"ri-menu-line ri-lg\"></i>\r\n          </div>\r\n        </button>\r\n        <div className=\"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start\">\r\n          <div className=\"max-w-lg w-full\">\r\n            <label htmlFor=\"search\" className=\"sr-only\">Search</label>\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <div className=\"w-5 h-5 flex items-center justify-center text-gray-400 dark:text-gray-500\">\r\n                  <i className=\"ri-search-line\"></i>\r\n                </div>\r\n              </div>\r\n              <input\r\n                id=\"search\"\r\n                name=\"search\"\r\n                className=\"block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white dark:hover:bg-gray-600 transition-colors\"\r\n                placeholder=\"Search for licenses, users, or transactions...\"\r\n                type=\"search\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={toggleNotificationModal}\r\n            className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative transition-colors duration-200\"\r\n            title={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <div className=\"w-6 h-6 flex items-center justify-center\">\r\n              <i className=\"ri-notification-3-line ri-lg\"></i>\r\n            </div>\r\n            {unreadCount > 0 && (\r\n              <span className=\"absolute -top-1 -right-1 flex items-center justify-center h-5 w-5 text-xs font-bold text-white bg-red-500 rounded-full ring-2 ring-white dark:ring-gray-800\">\r\n                {unreadCount > 99 ? '99+' : unreadCount}\r\n              </span>\r\n            )}\r\n          </button>\r\n          <div className=\"dropdown relative\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleDropdown}\r\n              className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              {user?.profile_image ? (\r\n                <img\r\n                  className=\"h-8 w-8 rounded-full object-cover\"\r\n                  src={user.profile_image}\r\n                  alt=\"Profile\"\r\n                  onError={(e) => {\r\n                    // Fallback to initials if image fails to load\r\n                    const target = e.target as HTMLImageElement;\r\n                    target.style.display = 'none';\r\n                    target.nextElementSibling?.classList.remove('hidden');\r\n                  }}\r\n                />\r\n              ) : null}\r\n              <div className={`h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user?.profile_image ? 'hidden' : ''}`}>\r\n                {user ? getUserInitials(user.first_name, user.last_name) : 'U'}\r\n              </div>\r\n            </button>\r\n            <div\r\n              className={`dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-gray-600 ring-opacity-5 ${\r\n                dropdownOpen ? 'show' : ''\r\n              }`}\r\n            >\r\n              <div className=\"py-1\">\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                >\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                >\r\n                  Settings\r\n                </Link>\r\n                <div className=\"px-4 py-2\">\r\n                  <LogoutButton\r\n                    variant=\"text\"\r\n                    size=\"sm\"\r\n                    className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                    showConfirmation={true}\r\n                    redirectTo=\"/auth/login\"\r\n                  >\r\n                    Sign out\r\n                  </LogoutButton>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {/* Secondary navigation - only show on dashboard routes */}\r\n      {showDashboardTabs && (\r\n        <div className=\"border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6\">\r\n          <div className=\"py-3 flex space-x-8\">\r\n            {tabs.map((tab) => (\r\n              <button\r\n                key={tab.id}\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  onTabChange?.(tab.id);\r\n                  // Dispatch custom event for dashboard to listen to\r\n                  window.dispatchEvent(new CustomEvent('tabChange', { detail: { tab: tab.id } }));\r\n                }}\r\n                className={`tab-button text-sm px-1 py-2 ${\r\n                  activeTab === tab.id ? 'active' : 'text-gray-500'\r\n                }`}\r\n              >\r\n                {tab.label}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={notificationModalOpen}\r\n        onClose={() => setNotificationModalOpen(false)}\r\n      />\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAkBA,MAAM,SAAS,CAAC,EAAE,YAAY,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAe;;IACtF,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,oDAAoD;IACpD,MAAM,oBAAoB,SAAS,UAAU,CAAC;IAE9C,MAAM,iBAAiB;QACrB,gBAAgB,CAAC;IACnB;IAEA,MAAM,0BAA0B;QAC9B,yBAAyB,CAAC;IAC5B;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAS,OAAO;QAAQ;QAC9B;YAAE,IAAI;YAAgB,OAAO;QAAe;QAC5C;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAc,OAAO;QAAa;KACzC;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,IAAG;wBACH,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAS,WAAU;8CAAU;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;sDAGjB,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO,CAAC,aAAa,EAAE,cAAc,IAAI,CAAC,EAAE,EAAE,YAAY,QAAQ,CAAC,GAAG,IAAI;;kDAE1E,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;oCAEd,cAAc,mBACb,6LAAC;wCAAK,WAAU;kDACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,MAAM,8BACL,6LAAC;gDACC,WAAU;gDACV,KAAK,KAAK,aAAa;gDACvB,KAAI;gDACJ,SAAS,CAAC;oDACR,8CAA8C;oDAC9C,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oDACvB,OAAO,kBAAkB,EAAE,UAAU,OAAO;gDAC9C;;;;;uDAEA;0DACJ,6LAAC;gDAAI,WAAW,CAAC,gGAAgG,EAAE,MAAM,gBAAgB,WAAW,IAAI;0DACrJ,OAAO,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,IAAI;;;;;;;;;;;;kDAG/D,6LAAC;wCACC,WAAW,CAAC,8HAA8H,EACxI,eAAe,SAAS,IACxB;kDAEF,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;wDACX,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,kBAAkB;wDAClB,YAAW;kEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUZ,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;4BAEC,MAAK;4BACL,SAAS;gCACP,cAAc,IAAI,EAAE;gCACpB,mDAAmD;gCACnD,OAAO,aAAa,CAAC,IAAI,YAAY,aAAa;oCAAE,QAAQ;wCAAE,KAAK,IAAI,EAAE;oCAAC;gCAAE;4BAC9E;4BACA,WAAW,CAAC,6BAA6B,EACvC,cAAc,IAAI,EAAE,GAAG,WAAW,iBAClC;sCAED,IAAI,KAAK;2BAXL,IAAI,EAAE;;;;;;;;;;;;;;;0BAmBrB,6LAAC,2JAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,yBAAyB;;;;;;;;;;;;AAIhD;GArKM;;QAC8B,kIAAA,CAAA,UAAO;QACjB,mIAAA,CAAA,mBAAgB;QAGvB,qIAAA,CAAA,cAAW;;;KALxB;uCAuKS", "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/NavItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\n\r\ninterface NavItemProps {\r\n  href: string;\r\n  icon: string;\r\n  label: string;\r\n  isActive?: boolean;\r\n  onClick?: () => void;\r\n}\r\n\r\nconst NavItem: React.FC<NavItemProps> = ({ \r\n  href, \r\n  icon, \r\n  label, \r\n  isActive = false,\r\n  onClick \r\n}) => {\r\n  const { showLoader } = useLoading();\r\n\r\n  const handleClick = () => {\r\n    // Show loader with specific message based on the page\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/dashboard': 'Loading Dashboard...',\r\n      '/applications/telecommunications': 'Loading Telecommunications...',\r\n      '/applications/postal': 'Loading Postal Services...',\r\n      '/applications/standards': 'Loading Standards...',\r\n      '/applications/clf': 'Loading CLF...',\r\n      '/resources': 'Loading Resources...',\r\n      '/procurement': 'Loading Procurement...',\r\n      '/spectrum': 'Loading Spectrum Management...',\r\n      '/financial': 'Loading Financial Data...',\r\n      '/reports': 'Loading Reports...',\r\n      '/users': 'Loading User Management...',\r\n      '/audit-trail': 'Loading Audit Trail...',\r\n      '/help': 'Loading Help & Support...'\r\n    };\r\n\r\n    const message = pageMessages[href] || 'Loading page...';\r\n    showLoader(message);\r\n\r\n    if (onClick) {\r\n      onClick();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      onClick={handleClick}\r\n      className={`\r\n        flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200\r\n        ${isActive\r\n          ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n        }\r\n      `}\r\n    >\r\n      <div className={`w-5 h-5 flex items-center justify-center mr-3 ${isActive ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n        <i className={icon}></i>\r\n      </div>\r\n      {label}\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default NavItem;"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAcA,MAAM,UAAkC,CAAC,EACvC,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,KAAK,EAChB,OAAO,EACR;;IACC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,MAAM,cAAc;QAClB,sDAAsD;QACtD,MAAM,eAA0C;YAC9C,cAAc;YACd,oCAAoC;YACpC,wBAAwB;YACxB,2BAA2B;YAC3B,qBAAqB;YACrB,cAAc;YACd,gBAAgB;YAChB,aAAa;YACb,cAAc;YACd,YAAY;YACZ,UAAU;YACV,gBAAgB;YAChB,SAAS;QACX;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI;QACtC,WAAW;QAEX,IAAI,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,SAAS;QACT,WAAW,CAAC;;QAEV,EAAE,WACE,8GACA,wHACH;MACH,CAAC;;0BAED,6LAAC;gBAAI,WAAW,CAAC,8CAA8C,EAAE,WAAW,mCAAmC,IAAI;0BACjH,cAAA,6LAAC;oBAAE,WAAW;;;;;;;;;;;YAEf;;;;;;;AAGP;GArDM;;QAOmB,qIAAA,CAAA,aAAU;;;KAP7B;uCAuDS", "debugId": null}}, {"offset": {"line": 1751, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/cacheService.ts"], "sourcesContent": ["// Cache service for API responses to reduce rate limiting\n\ninterface CacheItem<T> {\n  data: T;\n  timestamp: number;\n  expiresAt: number;\n}\n\nclass CacheService {\n  private cache = new Map<string, CacheItem<any>>();\n  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL\n\n  /**\n   * Set cache item with TTL\n   */\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\n    const now = Date.now();\n    const item: CacheItem<T> = {\n      data,\n      timestamp: now,\n      expiresAt: now + ttl\n    };\n    \n    this.cache.set(key, item);\n    console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);\n  }\n\n  /**\n   * Get cache item if not expired\n   */\n  get<T>(key: string): T | null {\n    const item = this.cache.get(key);\n    \n    if (!item) {\n      console.log(`Cache MISS: ${key}`);\n      return null;\n    }\n\n    const now = Date.now();\n    if (now > item.expiresAt) {\n      console.log(`Cache EXPIRED: ${key}`);\n      this.cache.delete(key);\n      return null;\n    }\n\n    console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);\n    return item.data as T;\n  }\n\n  /**\n   * Check if cache has valid item\n   */\n  has(key: string): boolean {\n    return this.get(key) !== null;\n  }\n\n  /**\n   * Delete cache item\n   */\n  delete(key: string): boolean {\n    console.log(`Cache DELETE: ${key}`);\n    return this.cache.delete(key);\n  }\n\n  /**\n   * Clear all cache\n   */\n  clear(): void {\n    console.log('Cache CLEAR: All items');\n    this.cache.clear();\n  }\n\n  /**\n   * Get cache stats\n   */\n  getStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n\n  /**\n   * Clean expired items\n   */\n  cleanup(): void {\n    const now = Date.now();\n    let cleaned = 0;\n\n    for (const [key, item] of this.cache.entries()) {\n      if (now > item.expiresAt) {\n        this.cache.delete(key);\n        cleaned++;\n      }\n    }\n\n    if (cleaned > 0) {\n      console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);\n    }\n  }\n\n  /**\n   * Get or set pattern - fetch data if not cached\n   */\n  async getOrSet<T>(\n    key: string,\n    fetcher: () => Promise<T>,\n    ttl: number = this.defaultTTL\n  ): Promise<T> {\n    // Try to get from cache first\n    const cached = this.get<T>(key);\n    if (cached !== null) {\n      return cached;\n    }\n\n    // Fetch fresh data\n    console.log(`Cache FETCH: ${key}`);\n    const data = await fetcher();\n    \n    // Store in cache\n    this.set(key, data, ttl);\n    \n    return data;\n  }\n\n  /**\n   * Invalidate cache by pattern\n   */\n  invalidatePattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    let invalidated = 0;\n\n    for (const key of this.cache.keys()) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n        invalidated++;\n      }\n    }\n\n    if (invalidated > 0) {\n      console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);\n    }\n  }\n}\n\n// Create singleton instance\nexport const cacheService = new CacheService();\n\n// Cache keys constants\nexport const CACHE_KEYS = {\n  LICENSE_TYPES: 'license-types',\n  LICENSE_CATEGORIES: 'license-categories',\n  LICENSE_CATEGORIES_BY_TYPE: (typeId: string) => `license-categories-type-${typeId}`,\n  USER_APPLICATIONS: 'user-applications',\n  APPLICATION: (id: string) => `application-${id}`,\n} as const;\n\n// Cache TTL constants (in milliseconds)\nexport const CACHE_TTL = {\n  SHORT: 2 * 60 * 1000,      // 2 minutes\n  MEDIUM: 5 * 60 * 1000,     // 5 minutes\n  LONG: 15 * 60 * 1000,      // 15 minutes\n  VERY_LONG: 60 * 60 * 1000, // 1 hour\n} as const;\n\n// Auto cleanup every 5 minutes\nsetInterval(() => {\n  cacheService.cleanup();\n}, 5 * 60 * 1000);\n\nexport default cacheService;\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAQ1D,MAAM;IACI,QAAQ,IAAI,MAA8B;IAC1C,aAAa,IAAI,KAAK,KAAK;IAEnC;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAqB;YACzB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,EAAE,IAAI,GAAG,CAAC;IACvD;IAEA;;GAEC,GACD,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC;QAChE,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,IAAI,GAAW,EAAW;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,OAAO,GAAW,EAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA;;GAEC,GACD,QAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,WAA6C;QAC3C,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,UAAU;QAEd,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC9C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,UAAU,GAAG;YACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,MAAc,IAAI,CAAC,UAAU,EACjB;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK;QACjC,MAAM,OAAO,MAAM;QAEnB,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAEpB,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,IAAI,cAAc;QAElB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,cAAc,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,yBAAyB,EAAE,SAAS;QAC3F;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa;IACxB,eAAe;IACf,oBAAoB;IACpB,4BAA4B,CAAC,SAAmB,CAAC,wBAAwB,EAAE,QAAQ;IACnF,mBAAmB;IACnB,aAAa,CAAC,KAAe,CAAC,YAAY,EAAE,IAAI;AAClD;AAGO,MAAM,YAAY;IACvB,OAAO,IAAI,KAAK;IAChB,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAEA,+BAA+B;AAC/B,YAAY;IACV,aAAa,OAAO;AACtB,GAAG,IAAI,KAAK;uCAEG", "debugId": null}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseTypeService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\r\n\r\n// Types\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  code?: string;\r\n  description: string;\r\n  validity: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n  creator?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n  updater?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n}\r\n\r\nexport interface NavigationItem {\r\n  license_type_id: string;\r\n  name: string;\r\n  code: string;\r\n  href: string;\r\n  label: string;\r\n  roles: string[];\r\n}\r\n\r\nexport interface CreateLicenseTypeDto {\r\n  name: string;\r\n  description: string;\r\n  validity: number;\r\n}\r\n\r\nexport interface UpdateLicenseTypeDto {\r\n  name?: string;\r\n  description?: string;\r\n  validity?: number;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\nexport type LicenseTypesResponse = PaginatedResponse<LicenseType>;\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport const licenseTypeService = {\r\n  // Get all license types with pagination\r\n  async getLicenseTypes(query: PaginateQuery = {}): Promise<LicenseTypesResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-types?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license type by ID\r\n  async getLicenseType(id: string): Promise<LicenseType> {\r\n    const response = await apiClient.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  \r\n  // Get license type by ID\r\n  async getLicenseTypeByCode(code: string): Promise<LicenseType> {\r\n    const response = await apiClient.get(`/license-types/by-code/${code}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n\r\n  // Create new license type\r\n  async createLicenseType(licenseTypeData: CreateLicenseTypeDto): Promise<LicenseType> {\r\n    const response = await apiClient.post('/license-types', licenseTypeData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license type\r\n  async updateLicenseType(id: string, licenseTypeData: UpdateLicenseTypeDto): Promise<LicenseType> {\r\n    const response = await apiClient.put(`/license-types/${id}`, licenseTypeData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license type\r\n  async deleteLicenseType(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get all license types (simple list for dropdowns) with caching\r\n  async getAllLicenseTypes(): Promise<any> {\r\n    return cacheService.getOrSet(\r\n      CACHE_KEYS.LICENSE_TYPES,\r\n      async () => {\r\n        console.log('Fetching license types from API...');\r\n        // Reduce limit to avoid rate limiting\r\n        const response = await this.getLicenseTypes({ limit: 100 });\r\n        return processApiResponse(response);\r\n      },\r\n      CACHE_TTL.LONG // Cache for 15 minutes\r\n    );\r\n  },\r\n\r\n  // Get license types for sidebar navigation\r\n  async getNavigationItems(): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get('/license-types/navigation/sidebar');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('LicenseTypeService.getNavigationItems error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAiFO,MAAM,qBAAqB;IAChC,wCAAwC;IACxC,MAAM,iBAAgB,QAAuB,CAAC,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAGA,yBAAyB;IACzB,MAAM,sBAAqB,IAAY;QACrC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM;QACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAGA,0BAA0B;IAC1B,MAAM,mBAAkB,eAAqC;QAC3D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,kBAAkB;QACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU,EAAE,eAAqC;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;QAC9D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iEAAiE;IACjE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,aAAa,EACxB;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAAE,OAAO;YAAI;YACzD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,2CAA2C;IAC3C,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;AAGF", "debugId": null}}, {"offset": {"line": 1980, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport NavItem from './NavItem';\r\nimport Link from 'next/link';\r\nimport { getUserInitials } from '@/utils/imageUtils';\r\nimport { licenseTypeService, NavigationItem } from '@/services/licenseTypeService';\r\n\r\nconst Sidebar: React.FC = () => {\r\n  const { user } = useAuth();\r\n  const pathname = usePathname();\r\n  const [isMobileOpen, setIsMobileOpen] = useState(false);\r\n  const [licenseTypeNavItems, setLicenseTypeNavItems] = useState<NavigationItem[]>([]);\r\n  const [loadingLicenseTypes, setLoadingLicenseTypes] = useState(true);\r\n\r\n  // Close mobile sidebar when route changes\r\n  useEffect(() => {\r\n    setIsMobileOpen(false);\r\n  }, [pathname]);\r\n\r\n  // Load license types for navigation\r\n  useEffect(() => {\r\n    const loadLicenseTypes = async () => {\r\n      try {\r\n        setLoadingLicenseTypes(true);\r\n        const data = await licenseTypeService.getNavigationItems();\r\n        const navigationItems: NavigationItem[] = data.data;\r\n        setLicenseTypeNavItems(navigationItems);\r\n      } catch (error) {\r\n        console.error('Failed to load license types for navigation:', error);\r\n        // Fallback to empty array if API fails\r\n        setLicenseTypeNavItems([]);\r\n      } finally {\r\n        setLoadingLicenseTypes(false);\r\n      }\r\n    };\r\n\r\n    loadLicenseTypes();\r\n  }, []);\r\n\r\n  // Close mobile sidebar when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const sidebar = document.getElementById('mobile-sidebar');\r\n      const toggleButton = document.getElementById('mobile-sidebar-toggle');\r\n      \r\n      if (\r\n        isMobileOpen &&\r\n        sidebar &&\r\n        !sidebar.contains(event.target as Node) &&\r\n        toggleButton &&\r\n        !toggleButton.contains(event.target as Node)\r\n      ) {\r\n        setIsMobileOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [isMobileOpen]);\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileOpen(!isMobileOpen);\r\n  };\r\n\r\n  // Static navigation items\r\n  const topStaticNavigationItems = [\r\n    {\r\n      href: '/dashboard',\r\n      icon: 'ri-dashboard-line',\r\n      label: 'Dashboard',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    },\r\n    {\r\n      href: '/tasks',\r\n      icon: 'ri-user-add-line',\r\n      label: 'Tasks',\r\n      roles: ['administrator', 'all']\r\n    }\r\n  ];\r\n\r\n  const staticNavigationItems = [\r\n\r\n    {\r\n      href: '/consumer-affairs',\r\n      icon: 'ri-shield-user-line',\r\n      label: 'Consumer Affairs',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    },\r\n    {\r\n      href: '/data-breach',\r\n      icon: 'ri-shield-cross-line',\r\n      label: 'Data Breach',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/resources',\r\n      icon: 'ri-folder-line',\r\n      label: 'Resources',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/procurement',\r\n      icon: 'ri-shopping-bag-line',\r\n      label: 'Procurement',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/financial',\r\n      icon: 'ri-money-dollar-circle-line',\r\n      label: 'Accounts & Finance',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/reports',\r\n      icon: 'ri-file-chart-line',\r\n      label: 'Reports & Analytics',\r\n      roles: ['administrator', 'evaluator']\r\n    }\r\n  ];\r\n\r\n  const settingsNavigationItems = [\r\n    {\r\n      href: '/users',\r\n      icon: 'ri-user-settings-line',\r\n      label: 'User Management',\r\n      roles: ['administrator']\r\n    },\r\n    {\r\n      href: '/settings',\r\n      icon: 'ri-settings-3-line',\r\n      label: 'Management Settings',\r\n      roles: ['administrator']\r\n    },\r\n    {\r\n      href: '/audit-trail',\r\n      icon: 'ri-shield-line',\r\n      label: 'Audit Trail',\r\n      roles: ['administrator', 'evaluator']\r\n    },\r\n    {\r\n      href: '/help',\r\n      icon: 'ri-question-line',\r\n      label: 'Help & Support',\r\n      roles: ['administrator', 'evaluator', 'customer']\r\n    }\r\n  ];\r\n\r\n  // Combine static navigation items with dynamic license types\r\n  const mainNavigationItems = [\r\n    ...licenseTypeNavItems.map(item => ({\r\n      href: item.href,\r\n      icon: 'ri-file-list-line', // Generic icon for all license types\r\n      label: item.label,\r\n      roles: item.roles\r\n    })),\r\n    ...staticNavigationItems\r\n  ];\r\n\r\n  const filteredMainNavItems = mainNavigationItems.filter(item =>\r\n    user?.roles?.some(role => item.roles.includes(role)) ||\r\n    item.roles.includes('customer')\r\n  );\r\n\r\n  const filteredSettingsNavItems = settingsNavigationItems.filter(item =>\r\n    user?.roles?.some(role => item.roles.includes(role)) ||\r\n    item.roles.includes('customer')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile Sidebar Toggle Button */}\r\n      <button\r\n        id=\"mobile-sidebar-toggle\"\r\n        onClick={toggleMobileSidebar}\r\n        className=\"lg:hidden fixed top-4 left-4 z-50 p-2 bg-primary text-white rounded-md shadow-lg hover:bg-red-700 transition-colors\"\r\n        aria-label=\"Toggle mobile sidebar\"\r\n      >\r\n        <i className={`fas ${isMobileOpen ? 'fa-times' : 'fa-bars'}`}></i>\r\n      </button>\r\n\r\n      {/* Mobile Overlay */}\r\n      {isMobileOpen && (\r\n        <div \r\n          className=\"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40\"\r\n          onClick={() => setIsMobileOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside\r\n        id=\"mobile-sidebar\"\r\n        className={`\r\n          fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out\r\n          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n        `}\r\n      >\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <img src=\"/images/macra-logo.png\" alt=\"MACRA Logo\" className=\"max-h-12 w-auto\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            {topStaticNavigationItems.length > 0 && (\r\n              <div className=\"mt-2 space-y-1\">\r\n                {topStaticNavigationItems.map((item) => (\r\n                  <NavItem\r\n                    key={item.href}\r\n                    href={item.href}\r\n                    icon={item.icon}\r\n                    label={item.label}\r\n                    isActive={pathname === item.href}\r\n                    onClick={() => setIsMobileOpen(false)}\r\n                  />\r\n              ))}\r\n            </div>\r\n            )}\r\n\r\n            {/* Main Navigation */}\r\n            {filteredMainNavItems.length > 0 && (\r\n              <div className=\"mt-8\">\r\n                <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Main Menu\r\n                </h3>\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {filteredMainNavItems.map((item) => (\r\n                    <NavItem\r\n                      key={item.href}\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                      onClick={() => setIsMobileOpen(false)}\r\n                    />\r\n                ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Settings Section */}\r\n            {filteredSettingsNavItems.length > 0 && (\r\n              <div className=\"mt-8\">\r\n                <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Settings\r\n                </h3>\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {filteredSettingsNavItems.map((item) => (\r\n                    <NavItem\r\n                      key={item.href}\r\n                      href={item.href}\r\n                      icon={item.icon}\r\n                      label={item.label}\r\n                      isActive={pathname === item.href}\r\n                      onClick={() => setIsMobileOpen(false)}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          {user && (\r\n            <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                {user.profile_image ? (\r\n                  <img\r\n                    className=\"h-10 w-10 rounded-full object-cover\"\r\n                    src={user.profile_image}\r\n                    alt=\"Profile\"\r\n                    onError={(e) => {\r\n                      // Fallback to initials if image fails to load\r\n                      const target = e.target as HTMLImageElement;\r\n                      target.style.display = 'none';\r\n                      target.nextElementSibling?.classList.remove('hidden');\r\n                    }}\r\n                  />\r\n                ) : null}\r\n                <div className={`h-10 w-10 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user.profile_image ? 'hidden' : ''}`}>\r\n                  {getUserInitials(user.first_name, user.last_name)}\r\n                </div>\r\n                <Link href='/profile' className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                    {user.first_name || 'Unknown'} {user.last_name || 'User'}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                    {user.roles && user.roles.length > 0\r\n                      ? user.roles.map(role =>\r\n                          typeof role === 'string'\r\n                            ? role.replace(/_/g, ' ').replace(/\\b\\w/g, (l: string) => l.toUpperCase())\r\n                            : 'Unknown'\r\n                        ).join(', ')\r\n                      : 'User'\r\n                    }\r\n                  </p>\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </aside>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,UAAoB;;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,gBAAgB;QAClB;4BAAG;QAAC;KAAS;IAEb,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;sDAAmB;oBACvB,IAAI;wBACF,uBAAuB;wBACvB,MAAM,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;wBACxD,MAAM,kBAAoC,KAAK,IAAI;wBACnD,uBAAuB;oBACzB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gDAAgD;wBAC9D,uCAAuC;wBACvC,uBAAuB,EAAE;oBAC3B,SAAU;wBACR,uBAAuB;oBACzB;gBACF;;YAEA;QACF;4BAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;wDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,MAAM,eAAe,SAAS,cAAc,CAAC;oBAE7C,IACE,gBACA,WACA,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KAC9B,gBACA,CAAC,aAAa,QAAQ,CAAC,MAAM,MAAM,GACnC;wBACA,gBAAgB;oBAClB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;4BAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB;QAC1B,gBAAgB,CAAC;IACnB;IAEA,0BAA0B;IAC1B,MAAM,2BAA2B;QAC/B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAM;QACjC;KACD;IAED,MAAM,wBAAwB;QAE5B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;KACD;IAED,MAAM,0BAA0B;QAC9B;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAgB;QAC1B;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;aAAgB;QAC1B;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;aAAY;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;gBAAC;gBAAiB;gBAAa;aAAW;QACnD;KACD;IAED,6DAA6D;IAC7D,MAAM,sBAAsB;WACvB,oBAAoB,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAClC,MAAM,KAAK,IAAI;gBACf,MAAM;gBACN,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;YACnB,CAAC;WACE;KACJ;IAED,MAAM,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,OACtD,MAAM,OAAO,KAAK,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,UAC9C,KAAK,KAAK,CAAC,QAAQ,CAAC;IAGtB,MAAM,2BAA2B,wBAAwB,MAAM,CAAC,CAAA,OAC9D,MAAM,OAAO,KAAK,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,UAC9C,KAAK,KAAK,CAAC,QAAQ,CAAC;IAGtB,qBACE;;0BAEE,6LAAC;gBACC,IAAG;gBACH,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC;oBAAE,WAAW,CAAC,IAAI,EAAE,eAAe,aAAa,WAAW;;;;;;;;;;;YAI7D,8BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;0BAKnC,6LAAC;gBACC,IAAG;gBACH,WAAW,CAAC;;UAEV,EAAE,eAAe,kBAAkB,qCAAqC;QAC1E,CAAC;0BAED,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,KAAI;oCAAyB,KAAI;oCAAa,WAAU;;;;;;;;;;;;;;;;sCAKjE,6LAAC;4BAAI,WAAU;;gCAEZ,yBAAyB,MAAM,GAAG,mBACjC,6LAAC;oCAAI,WAAU;8CACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,6LAAC,gIAAA,CAAA,UAAO;4CAEN,MAAM,KAAK,IAAI;4CACf,MAAM,KAAK,IAAI;4CACf,OAAO,KAAK,KAAK;4CACjB,UAAU,aAAa,KAAK,IAAI;4CAChC,SAAS,IAAM,gBAAgB;2CAL1B,KAAK,IAAI;;;;;;;;;;gCAYrB,qBAAqB,MAAM,GAAG,mBAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,qBAAqB,GAAG,CAAC,CAAC,qBACzB,6LAAC,gIAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,UAAU,aAAa,KAAK,IAAI;oDAChC,SAAS,IAAM,gBAAgB;mDAL1B,KAAK,IAAI;;;;;;;;;;;;;;;;gCAavB,yBAAyB,MAAM,GAAG,mBACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,6LAAC,gIAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,UAAU,aAAa,KAAK,IAAI;oDAChC,SAAS,IAAM,gBAAgB;mDAL1B,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;wBAczB,sBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,aAAa,iBACjB,6LAAC;wCACC,WAAU;wCACV,KAAK,KAAK,aAAa;wCACvB,KAAI;wCACJ,SAAS,CAAC;4CACR,8CAA8C;4CAC9C,MAAM,SAAS,EAAE,MAAM;4CACvB,OAAO,KAAK,CAAC,OAAO,GAAG;4CACvB,OAAO,kBAAkB,EAAE,UAAU,OAAO;wCAC9C;;;;;+CAEA;kDACJ,6LAAC;wCAAI,WAAW,CAAC,kGAAkG,EAAE,KAAK,aAAa,GAAG,WAAW,IAAI;kDACtJ,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS;;;;;;kDAElD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;;0DAC9B,6LAAC;gDAAE,WAAU;;oDACV,KAAK,UAAU,IAAI;oDAAU;oDAAE,KAAK,SAAS,IAAI;;;;;;;0DAEpD,6LAAC;gDAAE,WAAU;0DACV,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,IAC/B,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACb,OAAO,SAAS,WACZ,KAAK,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC,IAAc,EAAE,WAAW,MACrE,WACJ,IAAI,CAAC,QACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB;GA9SM;;QACa,kIAAA,CAAA,UAAO;QACP,qIAAA,CAAA,cAAW;;;KAFxB;uCAgTS", "debugId": null}}, {"offset": {"line": 2449, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/consumer-affairs/layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport Header from \"@/components/Header\";\r\nimport Sidebar from \"@/components/Sidebar\";\r\n\r\nexport default function ConsumerAffairsLayout({ children }: { children: React.ReactNode }) {\r\n  const { isAuthenticated, loading } = useAuth();\r\n  const router = useRouter();\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (!loading && !isAuthenticated) {\r\n      router.push('/auth/login');\r\n    }\r\n  }, [isAuthenticated, loading, router]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!isAuthenticated) {\r\n    return null; // Will redirect\r\n  }\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900\">\r\n      {/* Mobile sidebar overlay */}\r\n      <div\r\n        id=\"mobileSidebarOverlay\"\r\n        className={`mobile-sidebar-overlay ${isMobileSidebarOpen ? 'show' : ''}`}\r\n        onClick={() => setIsMobileSidebarOpen(false)}\r\n      ></div>\r\n\r\n      <Sidebar />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Header\r\n          activeTab={activeTab}\r\n          onTabChange={setActiveTab}\r\n          onMobileMenuToggle={toggleMobileSidebar}\r\n        />\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          {children}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS,sBAAsB,EAAE,QAAQ,EAAiC;;IACvF,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;0CAAG;QAAC;QAAiB;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,gBAAgB;IAC/B;IAEA,MAAM,sBAAsB;QAC1B,uBAAuB,CAAC;IAC1B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,IAAG;gBACH,WAAW,CAAC,uBAAuB,EAAE,sBAAsB,SAAS,IAAI;gBACxE,SAAS,IAAM,uBAAuB;;;;;;0BAGxC,6LAAC,gIAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+HAAA,CAAA,UAAM;wBACL,WAAW;wBACX,aAAa;wBACb,oBAAoB;;;;;;kCAEtB,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAlDwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}