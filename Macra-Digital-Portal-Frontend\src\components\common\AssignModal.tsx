'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from '@/contexts/ToastContext';
import { taskService, TaskType, TaskPriority, TaskStatus, Task } from '@/services/task-assignment';

interface Officer {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department?: {
    department_id: string;
    name: string;
    code: string;
  } | string; // Can be either a department object or string for backward compatibility
}

interface AssignModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: string | null;
  itemType: 'data_breach' | 'application' | 'complaint' | 'inspection' | 'document_review' | 'task';
  itemTitle?: string;
  onAssignSuccess?: () => void;
  // Reassignment mode props
  mode?: 'assign' | 'reassign';
  task?: Task | null; // For reassignment mode
  onReassignSuccess?: () => void;
}

const AssignModal: React.FC<AssignModalProps> = ({
  isOpen,
  onClose,
  itemId,
  itemType,
  itemTitle,
  onAssignSuccess,
  mode = 'assign',
  task,
  onReassignSuccess
}) => {
  const { showSuccess, showError } = useToast();
  const [officers, setOfficers] = useState<Officer[]>([]);
  const [loading, setLoading] = useState(false);
  const [assigning, setAssigning] = useState(false);
  const [selectedOfficer, setSelectedOfficer] = useState<string>('');
  const [comment, setComment] = useState('');
  const [dueDate, setDueDate] = useState<string>('');

  const isReassignMode = mode === 'reassign' && task;

  useEffect(() => {
    if (isOpen) {
      fetchOfficers();
      // For reassignment, pre-select the current assignee
      setSelectedOfficer(isReassignMode ? (task?.assigned_to || '') : '');
      setComment('');
      // For reassignment, pre-fill the current due date if available
      setDueDate(isReassignMode && task?.due_date ? task.due_date.split('T')[0] : '');
    }
  }, [isOpen, isReassignMode, task]);

  const fetchOfficers = async () => {
    setLoading(true);
    try {
      const response = await taskService.getOfficers();
      const officersData = response.data || [];
      setOfficers(officersData);

      if (officersData.length === 0) {
        console.warn('No officers found for task assignment');
      }
    } catch (error) {
      console.error('Error fetching officers:', error);
      setOfficers([]);
      showError('Failed to load officers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getTaskTypeFromItemType = (itemType: string): TaskType => {
    switch (itemType) {
      case 'application':
        return TaskType.EVALUATION;
      case 'data_breach':
        return TaskType.DATA_BREACH;
      case 'complaint':
        return TaskType.COMPLAINT;
      case 'inspection':
        return TaskType.INSPECTION;
      case 'document_review':
        return TaskType.DOCUMENT_REVIEW;
      case 'task':
        return TaskType.APPLICATION; // Default for existing tasks
      default:
        return TaskType.APPLICATION;
    }
  };

  const getTaskTitle = (itemType: string, itemTitle?: string): string => {
    const baseTitle = itemTitle || 'Untitled';
    switch (itemType) {
      case 'application':
        return `Application Evaluation: ${baseTitle}`;
      case 'data_breach':
        return `Data Breach Investigation: ${baseTitle}`;
      case 'complaint':
        return `Complaint Review: ${baseTitle}`;
      case 'inspection':
        return `Inspection Task: ${baseTitle}`;
      case 'document_review':
        return `Document Review: ${baseTitle}`;
      default:
        return `Task: ${baseTitle}`;
    }
  };

  const getTaskDescription = (itemType: string, itemTitle?: string): string => {
    const baseTitle = itemTitle || 'item';
    switch (itemType) {
      case 'application':
        return `Evaluate and review application ${baseTitle} for compliance and approval.`;
      case 'data_breach':
        return `Investigate and assess data breach report ${baseTitle} for regulatory compliance.`;
      case 'complaint':
        return `Review and resolve complaint ${baseTitle} according to regulatory procedures.`;
      case 'inspection':
        return `Conduct inspection for ${baseTitle} to ensure regulatory compliance.`;
      case 'document_review':
        return `Review and validate document ${baseTitle} for accuracy and compliance.`;
      default:
        return `Process and review ${baseTitle}.`;
    }
  };

  const handleAssign = async () => {
    if (!selectedOfficer) {
      showError('Please select an officer');
      return;
    }

    if (!dueDate) {
      showError('Please select a due date');
      return;
    }

    if (isReassignMode && !task) {
      showError('Task information is missing');
      return;
    }

    if (!isReassignMode && !itemId) {
      showError('Item ID is missing');
      return;
    }

    setAssigning(true);
    try {
      if (isReassignMode && task) {
        // Reassign existing task
        await taskService.reassignTask(task.task_id, {
          assignedTo: selectedOfficer,
          comment: comment.trim() || undefined
        });

        // Update due date if provided
        if (dueDate) {
          await taskService.updateTask(task.task_id, {
            due_date: dueDate
          });
        }

        showSuccess('Task reassigned successfully');
        onReassignSuccess?.();
      } else {
        // Create a new polymorphic task based on item type
        const taskData = {
          task_type: getTaskTypeFromItemType(itemType),
          title: getTaskTitle(itemType, itemTitle),
          description: getTaskDescription(itemType, itemTitle),
          priority: TaskPriority.MEDIUM,
          status: TaskStatus.PENDING,
          entity_type: itemType,
          entity_id: itemId!,
          assigned_to: selectedOfficer,
          due_date: dueDate,
          metadata: {
            comment: comment.trim() || undefined,
            original_item_title: itemTitle,
            assignment_context: 'manual_assignment'
          }
        };

        console.log('🚀 Creating task with data:', taskData);
        const createdTask = await taskService.createTask(taskData);
        console.log('✅ Task created:', createdTask);

        // Note: For application tasks, the backend automatically handles:
        // - Setting application.assigned_to and assigned_at fields
        // - Updating application status to 'evaluation'
        // This happens in the TasksService when the task is assigned

        showSuccess(`Successfully assigned ${itemType.replace('_', ' ')} to officer`);
        onAssignSuccess?.();
      }

      onClose();
    } catch (error) {
      console.error(`Error ${isReassignMode ? 'reassigning' : 'creating assignment'} task:`, error);
      showError(`Failed to ${isReassignMode ? 'reassign' : 'assign'} task`);
    } finally {
      setAssigning(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {isReassignMode ? 'Reassign Task' : `Create Task for ${itemType.replace('_', ' ').toUpperCase()}`}
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <i className="ri-close-line text-xl"></i>
            </button>
          </div>

          {/* Task Details */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center">
              <i className="ri-task-line mr-2"></i>
              {isReassignMode ? 'Task Details:' : 'Task to be Created:'}
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
              {isReassignMode ? task?.title : getTaskTitle(itemType, itemTitle)}
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              {isReassignMode ? task?.description : getTaskDescription(itemType, itemTitle)}
            </p>
            {isReassignMode && task?.task_number && (
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                Task #{task.task_number}
              </p>
            )}
            {isReassignMode && task?.assignee && (
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                Currently assigned to: {task.assignee.first_name} {task.assignee.last_name}
              </p>
            )}
          </div>

          {/* Officer Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'}
            </label>
            {loading ? (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                Loading officers...
              </div>
            ) : (
              <select
                value={selectedOfficer}
                onChange={(e) => setSelectedOfficer(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              >
                <option value="">Select an officer...</option>
                {officers.map((officer) => (
                  <option key={officer.user_id} value={officer.user_id}>
                    {officer.first_name} {officer.last_name} - {officer.email}
                    {officer.department ? ` (${typeof officer.department === 'string' ? officer.department : officer.department.name})` : ''}
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* Due Date */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Due Date *
            </label>
            <input
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]} // Prevent past dates
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            />
          </div>

          {/* Comment */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'}
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder={isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this task...'}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            />
          </div>

          {/* Selected Officer Summary */}
          {selectedOfficer && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg">
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
                Selected Officer:
              </h4>
              <div className="text-sm text-green-700 dark:text-green-200">
                {(() => {
                  const officer = officers.find(o => o.user_id === selectedOfficer);
                  return officer ? (
                    <>
                      {officer.first_name} {officer.last_name}
                      <br />
                      {officer.email}
                      {officer.department && (
                        <>
                          <br />
                          Department: {typeof officer.department === 'string' ? officer.department : officer.department.name}
                        </>
                      )}
                    </>
                  ) : 'Officer not found';
                })()}
              </div>
              {dueDate && (
                <div className="text-sm text-green-700 dark:text-green-200 mt-2">
                  <strong>Due Date:</strong> {new Date(dueDate).toLocaleDateString()}
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAssign}
              disabled={!selectedOfficer || !dueDate || assigning}
              className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {assigning ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isReassignMode ? 'Reassigning...' : 'Creating Task...'}
                </>
              ) : (
                <>
                  <i className={isReassignMode ? "ri-user-shared-line mr-2" : "ri-task-line mr-2"}></i>
                  {isReassignMode ? 'Reassign Task' : 'Create Task'}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignModal;
