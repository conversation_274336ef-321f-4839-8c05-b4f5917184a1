"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateContactPersonDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateContactPersonDto {
    application_id;
    first_name;
    last_name;
    middle_name;
    designation;
    email;
    phone;
    is_primary = false;
}
exports.CreateContactPersonDto = CreateContactPersonDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application ID this contact person belongs to',
        example: 'a46c4216-ec16-47ab-b24c-bcceae6a2a00'
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateContactPersonDto.prototype, "application_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'First name of the contact person',
        example: 'John',
        minLength: 1,
        maxLength: 100
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 100),
    __metadata("design:type", String)
], CreateContactPersonDto.prototype, "first_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last name of the contact person',
        example: 'Doe',
        minLength: 1,
        maxLength: 100
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 100),
    __metadata("design:type", String)
], CreateContactPersonDto.prototype, "last_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Middle name of the contact person',
        example: 'Michael',
        required: false,
        minLength: 1,
        maxLength: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 100),
    __metadata("design:type", String)
], CreateContactPersonDto.prototype, "middle_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job title or designation of the contact person',
        example: 'Chief Executive Officer',
        minLength: 5,
        maxLength: 50
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(5, 50),
    __metadata("design:type", String)
], CreateContactPersonDto.prototype, "designation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email address of the contact person',
        example: '<EMAIL>',
        format: 'email'
    }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateContactPersonDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phone number of the contact person',
        example: '+265991234567',
        pattern: '^[+]?[\\d\\s\\-()]+$'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(10, 20),
    (0, class_validator_1.Matches)(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' }),
    __metadata("design:type", String)
], CreateContactPersonDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether this is the primary contact person',
        example: true,
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateContactPersonDto.prototype, "is_primary", void 0);
//# sourceMappingURL=create-contact-person.dto.js.map