{"version": 3, "file": "auth.types.js", "sourceRoot": "", "sources": ["../../../src/common/types/auth.types.ts"], "names": [], "mappings": ";;;AAiFA,wDAEC;AAED,wCAMC;AAED,oCAGC;AAED,sCAIC;AAjGD,gEAA8D;AA4E9D,SAAgB,sBAAsB,CAAC,MAAc;IACnD,OAAO,MAAM,CAAC,MAAM,CAAC,gCAAe,CAAC,CAAC,QAAQ,CAAC,MAAyB,CAAC,CAAC;AAC5E,CAAC;AAED,SAAgB,cAAc,CAAC,GAAQ;IACrC,OAAO,GAAG;QACR,OAAO,GAAG,CAAC,YAAY,KAAK,QAAQ;QACpC,GAAG,CAAC,IAAI;QACR,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ;QACpC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;AACvC,CAAC;AAED,SAAgB,YAAY,CAAC,KAAa;IACxC,MAAM,UAAU,GAAG,4BAA4B,CAAC;IAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAED,SAAgB,aAAa,CAAC,MAAc;IAE1C,MAAM,SAAS,GAAG,wEAAwE,CAAC;IAC3F,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAkCD,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,IAAI,GAAG,YAAY,CAAC;IACpB,OAAO,CAAuB;IAE9B,YAAY,OAAe,EAAE,OAA6B;QACxD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AATD,kDASC;AAED,MAAa,cAAe,SAAQ,KAAK;IACvC,IAAI,GAAG,kBAAkB,CAAC;IAC1B,OAAO,CAAuB;IAE9B,YAAY,OAAe,EAAE,OAA6B;QACxD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AATD,wCASC"}