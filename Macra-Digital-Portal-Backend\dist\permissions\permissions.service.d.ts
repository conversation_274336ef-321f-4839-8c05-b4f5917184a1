import { Repository } from 'typeorm';
import { Permission } from '../entities/permission.entity';
import { CreatePermissionDto } from '../dto/permission/create-permission.dto';
import { UpdatePermissionDto } from '../dto/permission/update-permission.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class PermissionsService {
    private permissionsRepository;
    constructor(permissionsRepository: Repository<Permission>);
    create(createPermissionDto: CreatePermissionDto): Promise<Permission>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<Permission>>;
    findAllSimple(): Promise<Permission[]>;
    findByCategory(): Promise<{
        [category: string]: Permission[];
    }>;
    findOne(id: string): Promise<Permission>;
    findByName(name: string): Promise<Permission | null>;
    update(id: string, updatePermissionDto: UpdatePermissionDto): Promise<Permission>;
    remove(id: string): Promise<void>;
}
