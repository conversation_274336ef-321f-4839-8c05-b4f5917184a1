{"version": 3, "file": "license.seeder.service.js", "sourceRoot": "", "sources": ["../../../src/database/seeders/license.seeder.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,8EAAmE;AACnE,wFAA6E;AAC7E,sGAA0F;AAC1F,4DAAkD;AAG3C,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAGrB;IAEA;IAEA;IAEA;IARV,YAEU,sBAAgD,EAEhD,2BAA0D,EAE1D,kCAAuE,EAEvE,eAAiC;QANjC,2BAAsB,GAAtB,sBAAsB,CAA0B;QAEhD,gCAA2B,GAA3B,2BAA2B,CAA+B;QAE1D,uCAAkC,GAAlC,kCAAkC,CAAqC;QAEvE,oBAAe,GAAf,eAAe,CAAkB;IACxC,CAAC;IAEJ,KAAK,CAAC,gBAAgB;QACpB,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAG5C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QAChE,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE;SACnC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,YAAY,GAAG;YACnB;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,yHAAyH;gBACtI,QAAQ,EAAE,CAAC;gBACX,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,6FAA6F;gBAC1G,QAAQ,EAAE,CAAC;gBACX,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,iHAAiH;gBAC9H,QAAQ,EAAE,CAAC;gBACX,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,yDAAyD;gBACtE,QAAQ,EAAE,CAAC;gBACX,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,iEAAiE;gBAC9E,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;SACF,CAAC;QAEF,KAAK,MAAM,eAAe,IAAI,YAAY,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACxE,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,OAAO,CAAC,GAAG,CAAC,8BAA8B,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAGhD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACrE,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;YAClE,OAAO;QACT,CAAC;QAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE;SACnC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,+EAA+E,CAAC,CAAC;QACnG,CAAC;QAGD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,EAAE,CAAC,CAAC;QAChH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC;QACzG,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,sBAAsB,EAAE,EAAE,CAAC,CAAC;QACnH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;QACpG,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAAE,CAAC,CAAC;QAEjH,IAAI,CAAC,kBAAkB,IAAI,CAAC,cAAc,IAAI,CAAC,mBAAmB,IAAI,CAAC,YAAY,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3G,OAAO,CAAC,KAAK,CAAC,sEAAsE,CAAC,CAAC;YACtF,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG;YAEjB;gBACE,IAAI,EAAE,+BAA+B;gBACrC,WAAW,EAAE,0DAA0D;gBACvE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,+FAA+F;gBAC3G,eAAe,EAAE,kBAAkB,CAAC,eAAe;gBACnD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,iCAAiC;gBACvC,WAAW,EAAE,sDAAsD;gBACnE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,6EAA6E;gBACzF,eAAe,EAAE,kBAAkB,CAAC,eAAe;gBACnD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,yDAAyD;gBACtE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,kEAAkE;gBAC9E,eAAe,EAAE,kBAAkB,CAAC,eAAe;gBACnD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,iCAAiC;gBACvC,WAAW,EAAE,8CAA8C;gBAC3D,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,mEAAmE;gBAC/E,eAAe,EAAE,kBAAkB,CAAC,eAAe;gBACnD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,kCAAkC;gBACxC,WAAW,EAAE,oDAAoD;gBACjE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,kEAAkE;gBAC9E,eAAe,EAAE,kBAAkB,CAAC,eAAe;gBACnD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YAGD;gBACE,IAAI,EAAE,kCAAkC;gBACxC,WAAW,EAAE,iEAAiE;gBAC9E,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,wEAAwE;gBACpF,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,6BAA6B;gBACnC,WAAW,EAAE,qDAAqD;gBAClE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,mEAAmE;gBAC/E,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,iDAAiD;gBAC9D,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,qEAAqE;gBACjF,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,yDAAyD;gBACtE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,yEAAyE;gBACrF,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,uDAAuD;gBACpE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,+DAA+D;gBAC3E,eAAe,EAAE,cAAc,CAAC,eAAe;gBAC/C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YAGD;gBACE,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,4DAA4D;gBACzE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,gEAAgE;gBAC5E,eAAe,EAAE,mBAAmB,CAAC,eAAe;gBACpD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,qDAAqD;gBAClE,GAAG,EAAE,SAAS;gBACd,UAAU,EAAE,sDAAsD;gBAClE,eAAe,EAAE,mBAAmB,CAAC,eAAe;gBACpD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,wDAAwD;gBACrE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,wDAAwD;gBACpE,eAAe,EAAE,mBAAmB,CAAC,eAAe;gBACpD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,gCAAgC;gBACtC,WAAW,EAAE,gDAAgD;gBAC7D,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,2DAA2D;gBACvE,eAAe,EAAE,mBAAmB,CAAC,eAAe;gBACpD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YAGD;gBACE,IAAI,EAAE,4BAA4B;gBAClC,WAAW,EAAE,yCAAyC;gBACtD,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,mEAAmE;gBAC/E,eAAe,EAAE,YAAY,CAAC,eAAe;gBAC7C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,iCAAiC;gBACvC,WAAW,EAAE,8CAA8C;gBAC3D,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,wEAAwE;gBACpF,eAAe,EAAE,YAAY,CAAC,eAAe;gBAC7C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,gDAAgD;gBAC7D,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,yDAAyD;gBACrE,eAAe,EAAE,YAAY,CAAC,eAAe;gBAC7C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,wDAAwD;gBACrE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,6DAA6D;gBACzE,eAAe,EAAE,YAAY,CAAC,eAAe;gBAC7C,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YAGD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,wCAAwC;gBACrD,GAAG,EAAE,YAAY;gBACjB,UAAU,EAAE,oDAAoD;gBAChE,eAAe,EAAE,kBAAkB,CAAC,eAAe;gBACnD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,yDAAyD;gBACtE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,6DAA6D;gBACzE,eAAe,EAAE,kBAAkB,CAAC,eAAe;gBACnD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;YACD;gBACE,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,iDAAiD;gBAC9D,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,+DAA+D;gBAC3E,eAAe,EAAE,kBAAkB,CAAC,eAAe;gBACnD,UAAU,EAAE,SAAS,CAAC,OAAO;aAC9B;SACF,CAAC;QAEF,KAAK,MAAM,YAAY,IAAI,UAAU,EAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACvE,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtD,OAAO,CAAC,GAAG,CAAC,kCAAkC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,4BAA4B;QAChC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAGxD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,CAAC;QAC5E,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;YAC1E,OAAO;QACT,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,CAAC;QACjE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;YAC/F,OAAO;QACT,CAAC;QAGD,MAAM,iBAAiB,GAAG;YACxB,eAAe;YACf,kBAAkB;YAClB,iBAAiB;YACjB,iCAAiC;YACjC,6DAA6D;YAC7D,kBAAkB;YAClB,mCAAmC;YACnC,iDAAiD;YACjD,oDAAoD;YACpD,kCAAkC;YAClC,mEAAmE;YACnE,sCAAsC;YACtC,4BAA4B;YAC5B,8BAA8B;YAC9B,yBAAyB;YACzB,gCAAgC;YAChC,iCAAiC;YACjC,+CAA+C;SAChD,CAAC;QAGF,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,0CAA0C,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAEvE,KAAK,MAAM,YAAY,IAAI,iBAAiB,EAAE,CAAC;gBAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,kCAAkC,CAAC,MAAM,CAAC;oBAC9D,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB;oBACjD,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,IAAI;iBAClB,CAAC,CAAC;gBAEH,MAAM,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/D,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,gBAAgB,iBAAiB,CAAC,MAAM,kBAAkB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACtD,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACnC,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEnD,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACpC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAChD,CAAC;CACF,CAAA;AAjYY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,0DAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;qCALS,oBAAU;QAEL,oBAAU;QAEH,oBAAU;QAE7B,oBAAU;GAT1B,oBAAoB,CAiYhC"}