{"version": 3, "file": "legal-history.controller.js", "sourceRoot": "", "sources": ["../../src/legal-history/legal-history.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAYwB;AACxB,mEAA8D;AAC9D,4FAAsF;AACtF,4FAAsF;AACtF,6CAAuG;AACvG,2EAAgE;AAChE,kEAA6D;AAItD,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IACJ;IAA7B,YAA6B,mBAAwC;QAAxC,wBAAmB,GAAnB,mBAAmB,CAAqB;IAAG,CAAC;IAQzE,MAAM,CAAS,SAAgC,EAAa,GAAQ;QAClE,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IAOD,OAAO;QACL,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;IAC5C,CAAC;IAQD,iBAAiB,CAAyB,aAAqB;QAC7D,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;IACnE,CAAC;IASD,4BAA4B,CACF,aAAqB,EACrC,SAAwD,EACrD,GAAQ;QAEnB,OAAO,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,aAAa,EAAE,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5F,CAAC;IAQD,OAAO,CAAc,EAAU;QAC7B,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;IASD,MAAM,CAAc,EAAU,EAAU,SAAgC,EAAa,GAAQ;QAC3F,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzE,CAAC;IASD,MAAM,CAAc,EAAU;QAC5B,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IACjD,CAAC;CACF,CAAA;AA9EY,wDAAsB;AASjC;IANC,IAAA,aAAI,GAAE;IACN,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IAC9D,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,gDAAqB,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IACjF,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,oCAAoC,EAAE,IAAI,EAAE,mCAAY,EAAE,CAAC;IACnG,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,EAAC,UAAU,CAAC;IAClB,WAAA,IAAA,aAAI,GAAE,CAAA;IAAoC,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAAjC,gDAAqB;;oDAE9C;AAOD;IALC,IAAA,YAAG,GAAE;IACL,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAC1D,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,+BAA+B,EAAE,IAAI,EAAE,CAAC,mCAAY,CAAC,EAAE,CAAC;IAChG,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,EAAC,UAAU,CAAC;;;;qDAGzB;AAQD;IANC,IAAA,YAAG,EAAC,4BAA4B,CAAC;IACjC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;IAChE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACpF,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,qBAAqB,EAAE,IAAI,EAAE,mCAAY,EAAE,CAAC;IACpF,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,EAAC,UAAU,CAAC;IACP,WAAA,IAAA,cAAK,EAAC,eAAe,CAAC,CAAA;;;;+DAExC;AASD;IAPC,IAAA,aAAI,EAAC,4BAA4B,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gDAAgD,EAAE,CAAC;IAC3E,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACpF,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,gDAAqB,EAAE,WAAW,EAAE,6CAA6C,EAAE,CAAC;IACpG,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,kCAAkC,EAAE,IAAI,EAAE,mCAAY,EAAE,CAAC;IACjG,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,EAAC,UAAU,CAAC;IAEvB,WAAA,IAAA,cAAK,EAAC,eAAe,CAAC,CAAA;IACtB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;0EAGX;AAQD;IANC,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;IACpD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IAC3E,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,qBAAqB,EAAE,IAAI,EAAE,mCAAY,EAAE,CAAC;IACpF,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,EAAC,UAAU,CAAC;IACjB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;qDAEnB;AASD;IAPC,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACvD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;IACxC,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,gDAAqB,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IACjF,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,uBAAuB,EAAE,IAAI,EAAE,mCAAY,EAAE,CAAC;IACtF,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,EAAC,UAAU,CAAC;IAClB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IAAc,WAAA,IAAA,aAAI,GAAE,CAAA;IAAoC,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAAjC,gDAAqB;;oDAEvE;AASD;IAPC,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACvD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;IACxC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,uBAAuB,EAAE,CAAC;IAClE,IAAA,iBAAQ,EAAC,mBAAU,CAAC,UAAU,CAAC;IAC/B,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,EAAC,UAAU,CAAC;IAClB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;oDAElB;iCA7EU,sBAAsB;IAFlC,IAAA,iBAAO,EAAC,eAAe,CAAC;IACxB,IAAA,mBAAU,EAAC,eAAe,CAAC;qCAEwB,2CAAmB;GAD1D,sBAAsB,CA8ElC"}