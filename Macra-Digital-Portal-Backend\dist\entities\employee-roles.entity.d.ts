import { User } from './user.entity';
import { Employee } from './employee.entity';
import { Role } from './role.entity';
export declare class EmployeeRoles {
    employee_id: string;
    role_id: string;
    created_at: Date;
    created_by?: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    employee: Employee;
    role: Role;
    creator?: User;
    updater?: User;
}
