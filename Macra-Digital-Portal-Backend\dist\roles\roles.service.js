"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const role_entity_1 = require("../entities/role.entity");
const permission_entity_1 = require("../entities/permission.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
let RolesService = class RolesService {
    rolesRepository;
    permissionsRepository;
    constructor(rolesRepository, permissionsRepository) {
        this.rolesRepository = rolesRepository;
        this.permissionsRepository = permissionsRepository;
    }
    async create(createRoleDto) {
        const existingRole = await this.rolesRepository.findOne({
            where: { name: createRoleDto.name },
        });
        if (existingRole) {
            throw new common_1.ConflictException('Role with this name already exists');
        }
        let permissions = [];
        if (createRoleDto.permission_ids && createRoleDto.permission_ids.length > 0) {
            permissions = await this.permissionsRepository.findBy({
                permission_id: (0, typeorm_2.In)(createRoleDto.permission_ids),
            });
        }
        const role = this.rolesRepository.create({
            name: createRoleDto.name,
            description: createRoleDto.description,
            permissions,
        });
        return this.rolesRepository.save(role);
    }
    async findAll(query) {
        console.log('RolesService: findAll called with query:', JSON.stringify(query, null, 2));
        const config = {
            sortableColumns: ['name', 'created_at'],
            searchableColumns: ['name', 'description'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            relations: ['permissions', 'users'],
        };
        console.log('RolesService: Using config:', JSON.stringify(config, null, 2));
        const result = await (0, nestjs_paginate_1.paginate)(query, this.rolesRepository, config);
        console.log('RolesService: Raw pagination result:', JSON.stringify(result, null, 2));
        const transformedResult = pagination_interface_1.PaginationTransformer.transform(result);
        console.log('RolesService: Transformed result meta:', JSON.stringify(transformedResult.meta, null, 2));
        return transformedResult;
    }
    async findOne(id) {
        const role = await this.rolesRepository.findOne({
            where: { role_id: id },
            relations: ['permissions', 'users'],
        });
        if (!role) {
            throw new common_1.NotFoundException('Role not found');
        }
        return role;
    }
    async findByName(name) {
        return this.rolesRepository.findOne({
            where: { name: name },
            relations: ['permissions'],
        });
    }
    async update(id, updateRoleDto) {
        const role = await this.findOne(id);
        if (updateRoleDto.permission_ids !== undefined) {
            if (updateRoleDto.permission_ids.length > 0) {
                const permissions = await this.permissionsRepository.findBy({
                    permission_id: (0, typeorm_2.In)(updateRoleDto.permission_ids),
                });
                role.permissions = permissions;
            }
            else {
                role.permissions = [];
            }
        }
        if (updateRoleDto.name)
            role.name = updateRoleDto.name;
        if (updateRoleDto.description !== undefined)
            role.description = updateRoleDto.description;
        return this.rolesRepository.save(role);
    }
    async remove(id) {
        const role = await this.findOne(id);
        if (role.users && role.users.length > 0) {
            throw new common_1.ConflictException('Cannot delete role that has assigned users');
        }
        await this.rolesRepository.softDelete(id);
    }
    async assignPermissions(roleId, permissionIds) {
        const role = await this.findOne(roleId);
        const permissions = await this.permissionsRepository.findBy({
            permission_id: (0, typeorm_2.In)(permissionIds),
        });
        role.permissions = permissions;
        return this.rolesRepository.save(role);
    }
    async removePermissions(roleId, permissionIds) {
        const role = await this.findOne(roleId);
        role.permissions = role.permissions.filter(permission => !permissionIds.includes(permission.permission_id));
        return this.rolesRepository.save(role);
    }
};
exports.RolesService = RolesService;
exports.RolesService = RolesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(1, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], RolesService);
//# sourceMappingURL=roles.service.js.map