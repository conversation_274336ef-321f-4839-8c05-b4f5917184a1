"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixStakeholdersUuidTypes1750862201000 = void 0;
class FixStakeholdersUuidTypes1750862201000 {
    name = 'FixStakeholdersUuidTypes1750862201000';
    async up(queryRunner) {
        try {
            await queryRunner.query(`
        ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_75516ad5098e0aada3ffe364bf2\`
      `);
        }
        catch (error) {
            console.log('Foreign key FK_75516ad5098e0aada3ffe364bf2 does not exist, continuing...');
        }
        try {
            await queryRunner.query(`
        ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_applicant_id\`
      `);
        }
        catch (error) {
            console.log('Foreign key FK_stakeholders_applicant_id does not exist, continuing...');
        }
        try {
            await queryRunner.query(`
        ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_created_by\`
      `);
        }
        catch (error) {
            console.log('Foreign key FK_stakeholders_created_by does not exist, continuing...');
        }
        try {
            await queryRunner.query(`
        ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_updated_by\`
      `);
        }
        catch (error) {
            console.log('Foreign key FK_stakeholders_updated_by does not exist, continuing...');
        }
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`applicant_id\` VARCHAR(36) NOT NULL,
      MODIFY COLUMN \`contact_id\` VARCHAR(36) NOT NULL,
      MODIFY COLUMN \`cv_document_id\` VARCHAR(36) NOT NULL,
      MODIFY COLUMN \`created_by\` VARCHAR(36) NOT NULL,
      MODIFY COLUMN \`updated_by\` VARCHAR(36) NULL
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      ADD CONSTRAINT \`FK_stakeholders_applicant_id\` 
      FOREIGN KEY (\`applicant_id\`) REFERENCES \`applicants\`(\`applicant_id\`) 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      ADD CONSTRAINT \`FK_stakeholders_contact_id\` 
      FOREIGN KEY (\`contact_id\`) REFERENCES \`contacts\`(\`contact_id\`) 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      ADD CONSTRAINT \`FK_stakeholders_created_by\` 
      FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      ADD CONSTRAINT \`FK_stakeholders_updated_by\` 
      FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_updated_by\``);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_created_by\``);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_contact_id\``);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_applicant_id\``);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`applicant_id\` CHAR(36) NOT NULL,
      MODIFY COLUMN \`contact_id\` CHAR(36) NOT NULL,
      MODIFY COLUMN \`cv_document_id\` CHAR(36) NOT NULL,
      MODIFY COLUMN \`created_by\` CHAR(36) NOT NULL,
      MODIFY COLUMN \`updated_by\` CHAR(36) NULL
    `);
    }
}
exports.FixStakeholdersUuidTypes1750862201000 = FixStakeholdersUuidTypes1750862201000;
//# sourceMappingURL=1750862201000-FixStakeholdersUuidTypes.js.map