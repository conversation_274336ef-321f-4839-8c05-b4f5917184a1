import { Logger, HttpStatus } from '@nestjs/common';
export interface ErrorContext {
    userId?: string;
    email?: string;
    action?: string;
    metadata?: Record<string, any>;
}
export declare class ErrorHandler {
    static handleError(logger: Logger, error: any, message: string, context?: ErrorContext, statusCode?: HttpStatus): never;
    static logError(logger: Logger, error: any, message: string, context?: ErrorContext): void;
    static logWarning(logger: Logger, message: string, context?: ErrorContext): void;
    static logInfo(logger: Logger, message: string, context?: ErrorContext): void;
    private static formatErrorMessage;
    private static formatLogContext;
    static createUserContext(user: {
        user_id?: string;
        email?: string;
    }, action?: string, metadata?: Record<string, any>): ErrorContext;
    static createActionContext(email: string, action: string, metadata?: Record<string, any>): ErrorContext;
}
