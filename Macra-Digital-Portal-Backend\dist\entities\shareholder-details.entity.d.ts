import { User } from './user.entity';
import { Stakeholder } from './stakeholders.entity';
export declare class ShareholderDetails {
    shareholder_id: string;
    stakeholder_id: string;
    shareholding_percent: number;
    description?: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    stakeholder: Stakeholder;
    creator: User;
    updater?: User;
    generateId(): void;
}
