import { LegalHistoryService } from './legal-history.service';
import { CreateLegalHistoryDto } from '../dto/legal-history/create-legal-history.dto';
import { UpdateLegalHistoryDto } from '../dto/legal-history/update-legal-history.dto';
import { LegalHistory } from '../entities/legal-history.entity';
export declare class LegalHistoryController {
    private readonly legalHistoryService;
    constructor(legalHistoryService: LegalHistoryService);
    create(createDto: CreateLegalHistoryDto, req: any): Promise<LegalHistory>;
    findAll(): Promise<LegalHistory[]>;
    findByApplication(applicationId: string): Promise<LegalHistory | null>;
    createOrUpdateForApplication(applicationId: string, createDto: Omit<CreateLegalHistoryDto, 'application_id'>, req: any): Promise<LegalHistory>;
    findOne(id: string): Promise<LegalHistory>;
    update(id: string, updateDto: UpdateLegalHistoryDto, req: any): Promise<LegalHistory>;
    remove(id: string): Promise<void>;
}
