{"version": 3, "file": "permissions.controller.js", "sourceRoot": "", "sources": ["../../src/permissions/permissions.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAUwB;AACxB,+DAA2D;AAC3D,mFAA8E;AAC9E,mFAA8E;AAC9E,kEAA6D;AAC7D,qDAA0D;AAMnD,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IACH;IAA7B,YAA6B,kBAAsC;QAAtC,uBAAkB,GAAlB,kBAAkB,CAAoB;IAAG,CAAC;IAGvE,MAAM,CAAS,mBAAwC;QACrD,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAC7D,CAAC;IAGD,OAAO,CAAa,KAAoB;QACtC,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAGD,cAAc;QACZ,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;IAClD,CAAC;IAGD,OAAO,CAA6B,EAAU;QAC5C,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7C,CAAC;IAGD,MAAM,CACwB,EAAU,EAC9B,mBAAwC;QAEhD,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE,mBAAmB,CAAC,CAAC;IACjE,CAAC;IAGK,AAAN,KAAK,CAAC,MAAM,CAA6B,EAAU;QACjD,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACzC,OAAO,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IACxD,CAAC;CACF,CAAA;AApCY,sDAAqB;AAIhC;IADC,IAAA,aAAI,GAAE;IACC,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAsB,2CAAmB;;mDAEtD;AAGD;IADC,IAAA,YAAG,GAAE;IACG,WAAA,IAAA,0BAAQ,GAAE,CAAA;;;;oDAElB;AAGD;IADC,IAAA,YAAG,EAAC,aAAa,CAAC;;;;2DAGlB;AAGD;IADC,IAAA,YAAG,EAAC,KAAK,CAAC;IACF,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;oDAElC;AAGD;IADC,IAAA,cAAK,EAAC,KAAK,CAAC;IAEV,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAsB,2CAAmB;;mDAGjD;AAGK;IADL,IAAA,eAAM,EAAC,KAAK,CAAC;IACA,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;mDAGvC;gCAnCU,qBAAqB;IAFjC,IAAA,mBAAU,EAAC,aAAa,CAAC;IACzB,IAAA,kBAAS,EAAC,6BAAY,CAAC;qCAE2B,wCAAkB;GADxD,qBAAqB,CAoCjC"}