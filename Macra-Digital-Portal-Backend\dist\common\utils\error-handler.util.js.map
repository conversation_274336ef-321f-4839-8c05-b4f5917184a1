{"version": 3, "file": "error-handler.util.js", "sourceRoot": "", "sources": ["../../../src/common/utils/error-handler.util.ts"], "names": [], "mappings": ";;;AAAA,2CAAmE;AASnE,MAAa,YAAY;IAIvB,MAAM,CAAC,WAAW,CAChB,MAAc,EACd,KAAU,EACV,OAAe,EACf,OAAsB,EACtB,aAAyB,mBAAU,CAAC,qBAAqB;QAEzD,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAElD,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAEpD,IAAI,KAAK,YAAY,sBAAa,EAAE,CAAC;YACnC,MAAM,KAAK,CAAC;QACd,CAAC;QAED,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC/C,CAAC;IAKD,MAAM,CAAC,QAAQ,CACb,MAAc,EACd,KAAU,EACV,OAAe,EACf,OAAsB;QAEtB,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAElD,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;IAKD,MAAM,CAAC,UAAU,CACf,MAAc,EACd,OAAe,EACf,OAAsB;QAEtB,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAElD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;IAC1C,CAAC;IAKD,MAAM,CAAC,OAAO,CACZ,MAAc,EACd,OAAe,EACf,OAAsB;QAEtB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAElD,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAKO,MAAM,CAAC,kBAAkB,CAAC,OAAe,EAAE,OAAsB;QACvE,IAAI,CAAC,OAAO;YAAE,OAAO,OAAO,CAAC;QAE7B,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,YAAY,CAAC,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,YAAY,CAAC,MAAM,GAAG,CAAC;YAC5B,CAAC,CAAC,GAAG,OAAO,KAAK,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;YAC3C,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAKO,MAAM,CAAC,gBAAgB,CAAC,OAAsB;QACpD,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,CAAC;QAExB,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAKD,MAAM,CAAC,iBAAiB,CACtB,IAA0C,EAC1C,MAAe,EACf,QAA8B;QAE9B,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,mBAAmB,CACxB,KAAa,EACb,MAAc,EACd,QAA8B;QAE9B,OAAO;YACL,KAAK;YACL,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;CACF;AAxID,oCAwIC"}