module.exports = {

"[project]/src/services/applicationService.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/services/applicationService.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/services/evaluationService.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_services_evaluationService_ts_6e2a7843._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/services/evaluationService.ts [app-ssr] (ecmascript)");
    });
});
}}),

};