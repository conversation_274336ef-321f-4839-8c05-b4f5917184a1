import { User } from './user.entity';
import { Address } from './address.entity';
import { Contacts } from './contacts.entity';
export declare class TypeApprovedManufacturer {
    manufacturer_id: string;
    manufacturer_name: string;
    address_id?: string;
    manufacturer_country_origin: string;
    manufacturer_region?: string;
    contact_id?: string;
    manufacturer_email?: string;
    manufacturer_phone?: string;
    manufacturer_website: string;
    manufacturer_approval_number: string;
    manufacturer_approval_date: Date;
    approval_certification_standard?: string;
    equipment_types?: string;
    created_at: Date;
    created_by?: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    creator?: User;
    updater?: User;
    address?: Address;
    contact?: Contacts;
    generateId(): void;
}
