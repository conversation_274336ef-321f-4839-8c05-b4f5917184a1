import { User } from './user.entity';
export declare class Applicants {
    applicant_id: string;
    name: string;
    business_registration_number: string;
    tpin: string;
    website: string;
    email: string;
    phone: string;
    fax?: string;
    level_of_insurance_cover?: string;
    date_incorporation: Date;
    place_incorporation: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    creator: User;
    updater?: User;
    generateId(): void;
}
