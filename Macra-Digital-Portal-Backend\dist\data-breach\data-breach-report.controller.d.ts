import { DataBreachReportService } from './data-breach-report.service';
import { CreateDataBreachReportDto, UpdateDataBreachReportDto, DataBreachReportFilterDto, UpdateDataBreachReportStatusDto } from './data-breach-report.dto';
import { PaginateQuery } from 'nestjs-paginate';
export declare class DataBreachReportController {
    private readonly reportService;
    constructor(reportService: DataBreachReportService);
    create(createDto: CreateDataBreachReportDto, files: Express.Multer.File[], req: any): Promise<import("./data-breach-report.dto").DataBreachReportResponseDto>;
    findAll(query: PaginateQuery, req: any): Promise<import("nestjs-paginate").Paginated<import("./data-breach-report.entity").DataBreachReport>>;
    findOne(id: string, req: any): Promise<import("./data-breach-report.dto").DataBreachReportResponseDto>;
    update(id: string, updateDto: UpdateDataBreachReportDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./data-breach-report.dto").DataBreachReportResponseDto;
    }>;
    delete(id: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    updateStatus(id: string, statusDto: UpdateDataBreachReportStatusDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./data-breach-report.dto").DataBreachReportResponseDto;
    }>;
    assignReport(id: string, assignedTo: string, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./data-breach-report.dto").DataBreachReportResponseDto;
    }>;
    getStatsSummary(req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            total: number;
            by_status: {};
            by_category: {};
            by_severity: {};
            by_priority: {};
        };
    }>;
    exportToCsv(filterDto: DataBreachReportFilterDto, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    addAttachments(id: string, files: Express.Multer.File[], req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    deleteAttachment(id: string, attachmentId: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    getUrgentAlerts(req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./data-breach-report.entity").DataBreachReport[];
    }>;
}
