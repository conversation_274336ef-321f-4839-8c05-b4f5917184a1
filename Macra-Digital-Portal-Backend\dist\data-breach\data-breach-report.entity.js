"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachReportStatusHistory = exports.DataBreachReportAttachment = exports.DataBreachReport = exports.DataBreachPriority = exports.DataBreachStatus = exports.DataBreachSeverity = exports.DataBreachCategory = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const class_validator_1 = require("class-validator");
const user_entity_1 = require("../entities/user.entity");
var DataBreachCategory;
(function (DataBreachCategory) {
    DataBreachCategory["UNAUTHORIZED_ACCESS"] = "Unauthorized Data Access";
    DataBreachCategory["DATA_MISUSE"] = "Data Misuse or Sharing";
    DataBreachCategory["PRIVACY_VIOLATIONS"] = "Privacy Violations";
    DataBreachCategory["IDENTITY_THEFT"] = "Identity Theft";
    DataBreachCategory["PHISHING_ATTEMPTS"] = "Phishing Attempts";
    DataBreachCategory["DATA_LOSS"] = "Data Loss or Theft";
    DataBreachCategory["CONSENT_VIOLATIONS"] = "Consent Violations";
    DataBreachCategory["OTHER"] = "Other";
})(DataBreachCategory || (exports.DataBreachCategory = DataBreachCategory = {}));
var DataBreachSeverity;
(function (DataBreachSeverity) {
    DataBreachSeverity["LOW"] = "low";
    DataBreachSeverity["MEDIUM"] = "medium";
    DataBreachSeverity["HIGH"] = "high";
    DataBreachSeverity["CRITICAL"] = "critical";
})(DataBreachSeverity || (exports.DataBreachSeverity = DataBreachSeverity = {}));
var DataBreachStatus;
(function (DataBreachStatus) {
    DataBreachStatus["SUBMITTED"] = "submitted";
    DataBreachStatus["UNDER_REVIEW"] = "under_review";
    DataBreachStatus["INVESTIGATING"] = "investigating";
    DataBreachStatus["RESOLVED"] = "resolved";
    DataBreachStatus["CLOSED"] = "closed";
})(DataBreachStatus || (exports.DataBreachStatus = DataBreachStatus = {}));
var DataBreachPriority;
(function (DataBreachPriority) {
    DataBreachPriority["LOW"] = "low";
    DataBreachPriority["MEDIUM"] = "medium";
    DataBreachPriority["HIGH"] = "high";
    DataBreachPriority["URGENT"] = "urgent";
})(DataBreachPriority || (exports.DataBreachPriority = DataBreachPriority = {}));
let DataBreachReport = class DataBreachReport {
    report_id;
    report_number;
    reporter_id;
    title;
    description;
    category;
    severity;
    status;
    priority;
    incident_date;
    organization_involved;
    affected_data_types;
    contact_attempts;
    assigned_to;
    resolution;
    internal_notes;
    resolved_at;
    created_at;
    updated_at;
    deleted_at;
    created_by;
    updated_by;
    reporter;
    assignee;
    creator;
    updater;
    attachments;
    status_history;
    generateId() {
        if (!this.report_id) {
            this.report_id = (0, uuid_1.v4)();
        }
        if (!this.report_number) {
            const year = new Date().getFullYear();
            const randomNum = Math.floor(Math.random() * 999) + 1;
            this.report_number = `BREACH-${year}-${randomNum.toString().padStart(3, '0')}`;
        }
    }
};
exports.DataBreachReport = DataBreachReport;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "report_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', unique: true }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "report_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "reporter_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DataBreachCategory,
    }),
    (0, class_validator_1.IsEnum)(DataBreachCategory),
    __metadata("design:type", String)
], DataBreachReport.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DataBreachSeverity,
    }),
    (0, class_validator_1.IsEnum)(DataBreachSeverity),
    __metadata("design:type", String)
], DataBreachReport.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DataBreachStatus,
        default: DataBreachStatus.SUBMITTED,
    }),
    (0, class_validator_1.IsEnum)(DataBreachStatus),
    __metadata("design:type", String)
], DataBreachReport.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DataBreachPriority,
        default: DataBreachPriority.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(DataBreachPriority),
    __metadata("design:type", String)
], DataBreachReport.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], DataBreachReport.prototype, "incident_date", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "organization_involved", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "affected_data_types", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "contact_attempts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "assigned_to", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "resolution", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "internal_notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], DataBreachReport.prototype, "resolved_at", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], DataBreachReport.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], DataBreachReport.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], DataBreachReport.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReport.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'reporter_id' }),
    __metadata("design:type", user_entity_1.User)
], DataBreachReport.prototype, "reporter", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'assigned_to' }),
    __metadata("design:type", user_entity_1.User)
], DataBreachReport.prototype, "assignee", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], DataBreachReport.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], DataBreachReport.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => DataBreachReportAttachment, (attachment) => attachment.report),
    __metadata("design:type", Array)
], DataBreachReport.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => DataBreachReportStatusHistory, (history) => history.report),
    __metadata("design:type", Array)
], DataBreachReport.prototype, "status_history", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DataBreachReport.prototype, "generateId", null);
exports.DataBreachReport = DataBreachReport = __decorate([
    (0, typeorm_1.Entity)('data_breach_reports')
], DataBreachReport);
let DataBreachReportAttachment = class DataBreachReportAttachment {
    attachment_id;
    report_id;
    file_name;
    file_path;
    file_type;
    file_size;
    uploaded_at;
    uploaded_by;
    report;
    uploader;
    generateId() {
        if (!this.attachment_id) {
            this.attachment_id = (0, uuid_1.v4)();
        }
    }
};
exports.DataBreachReportAttachment = DataBreachReportAttachment;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReportAttachment.prototype, "attachment_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReportAttachment.prototype, "report_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReportAttachment.prototype, "file_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReportAttachment.prototype, "file_path", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReportAttachment.prototype, "file_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", Number)
], DataBreachReportAttachment.prototype, "file_size", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], DataBreachReportAttachment.prototype, "uploaded_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReportAttachment.prototype, "uploaded_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => DataBreachReport, (report) => report.attachments),
    (0, typeorm_1.JoinColumn)({ name: 'report_id' }),
    __metadata("design:type", DataBreachReport)
], DataBreachReportAttachment.prototype, "report", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'uploaded_by' }),
    __metadata("design:type", user_entity_1.User)
], DataBreachReportAttachment.prototype, "uploader", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DataBreachReportAttachment.prototype, "generateId", null);
exports.DataBreachReportAttachment = DataBreachReportAttachment = __decorate([
    (0, typeorm_1.Entity)('data_breach_report_attachments')
], DataBreachReportAttachment);
let DataBreachReportStatusHistory = class DataBreachReportStatusHistory {
    history_id;
    report_id;
    status;
    comment;
    created_at;
    created_by;
    report;
    creator;
    generateId() {
        if (!this.history_id) {
            this.history_id = (0, uuid_1.v4)();
        }
    }
};
exports.DataBreachReportStatusHistory = DataBreachReportStatusHistory;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReportStatusHistory.prototype, "history_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReportStatusHistory.prototype, "report_id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DataBreachStatus,
    }),
    (0, class_validator_1.IsEnum)(DataBreachStatus),
    __metadata("design:type", String)
], DataBreachReportStatusHistory.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReportStatusHistory.prototype, "comment", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], DataBreachReportStatusHistory.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DataBreachReportStatusHistory.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => DataBreachReport, (report) => report.status_history),
    (0, typeorm_1.JoinColumn)({ name: 'report_id' }),
    __metadata("design:type", DataBreachReport)
], DataBreachReportStatusHistory.prototype, "report", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], DataBreachReportStatusHistory.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DataBreachReportStatusHistory.prototype, "generateId", null);
exports.DataBreachReportStatusHistory = DataBreachReportStatusHistory = __decorate([
    (0, typeorm_1.Entity)('data_breach_report_status_history')
], DataBreachReportStatusHistory);
//# sourceMappingURL=data-breach-report.entity.js.map