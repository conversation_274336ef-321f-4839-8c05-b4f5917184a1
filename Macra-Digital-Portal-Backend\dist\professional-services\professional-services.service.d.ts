import { Repository } from 'typeorm';
import { ProfessionalServices } from '../entities/professional-services.entity';
import { CreateProfessionalServicesDto } from '../dto/professional-services/create-professional-services.dto';
import { UpdateProfessionalServicesDto } from '../dto/professional-services/update-professional-services.dto';
export declare class ProfessionalServicesService {
    private professionalServicesRepository;
    constructor(professionalServicesRepository: Repository<ProfessionalServices>);
    create(dto: CreateProfessionalServicesDto, createdBy: string): Promise<ProfessionalServices>;
    findAll(): Promise<ProfessionalServices[]>;
    findOne(id: string): Promise<ProfessionalServices>;
    findByApplication(applicationId: string): Promise<ProfessionalServices | null>;
    update(id: string, dto: UpdateProfessionalServicesDto, updatedBy: string): Promise<ProfessionalServices>;
    softDelete(id: string): Promise<void>;
    createOrUpdate(applicationId: string, dto: Omit<CreateProfessionalServicesDto, 'application_id'>, userId: string): Promise<ProfessionalServices>;
}
