{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/dashboardService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\nexport interface DashboardOverview {\r\n  applications: {\r\n    total: number;\r\n    pending: number;\r\n    approved: number;\r\n    rejected: number;\r\n    submitted?: number;\r\n    under_review?: number;\r\n    evaluation?: number;\r\n    draft?: number;\r\n  };\r\n  users?: {\r\n    total: number;\r\n    active: number;\r\n    newThisMonth: number;\r\n    administrators: number;\r\n  };\r\n  licenses: {\r\n    total: number;\r\n    active: number;\r\n    expiringSoon: number;\r\n    expired: number;\r\n  };\r\n  financial: {\r\n    totalRevenue: number;\r\n    thisMonth: number;\r\n    pending: number;\r\n    transactions: number;\r\n  };\r\n  timestamp: string;\r\n}\r\n\r\nexport interface LicenseStats {\r\n  total: number;\r\n  active: number;\r\n  expiringSoon: number;\r\n  expired: number;\r\n}\r\n\r\nexport interface UserStats {\r\n  total: number;\r\n  active: number;\r\n  newThisMonth: number;\r\n  administrators: number;\r\n}\r\n\r\nexport interface FinancialStats {\r\n  totalRevenue: number;\r\n  thisMonth: number;\r\n  pending: number;\r\n  transactions: number;\r\n}\r\n\r\nexport interface RecentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  applicant?: {\r\n    company_name: string;\r\n  };\r\n  license_category?: {\r\n    category_name: string;\r\n  };\r\n}\r\n\r\nexport interface RecentActivity {\r\n  audit_id: string;\r\n  action: string;\r\n  module: string;\r\n  resource_type: string;\r\n  description: string;\r\n  created_at: string;\r\n  user?: {\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n}\r\n\r\n// Default fallback data\r\nconst getDefaultOverview = (): DashboardOverview => ({\r\n  applications: {\r\n    total: 0,\r\n    pending: 0,\r\n    approved: 0,\r\n    rejected: 0,\r\n    submitted: 0,\r\n    under_review: 0,\r\n    evaluation: 0,\r\n    draft: 0,\r\n  },\r\n  users: {\r\n    total: 0,\r\n    active: 0,\r\n    newThisMonth: 0,\r\n    administrators: 0,\r\n  },\r\n  licenses: {\r\n    total: 0,\r\n    active: 0,\r\n    expiringSoon: 0,\r\n    expired: 0,\r\n  },\r\n  financial: {\r\n    totalRevenue: 0,\r\n    thisMonth: 0,\r\n    pending: 0,\r\n    transactions: 0,\r\n  },\r\n  timestamp: new Date().toISOString(),\r\n});\r\n\r\nexport const dashboardService = {\r\n  // Get dashboard overview with all key metrics\r\n  async getOverview(): Promise<DashboardOverview> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/overview');\r\n      const data = processApiResponse(response);\r\n\r\n      // Ensure all required fields are present with fallbacks\r\n      return {\r\n        applications: data.applications || getDefaultOverview().applications,\r\n        users: data.users || getDefaultOverview().users,\r\n        licenses: data.licenses || getDefaultOverview().licenses,\r\n        financial: data.financial || getDefaultOverview().financial,\r\n        timestamp: data.timestamp || new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      console.error('DashboardService.getOverview error:', error);\r\n      // Return default data instead of throwing\r\n      return getDefaultOverview();\r\n    }\r\n  },\r\n\r\n  // Get license statistics\r\n  async getLicenseStats(): Promise<LicenseStats> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/licenses/stats');\r\n      const data = processApiResponse(response);\r\n      return data.data || data;\r\n    } catch (error) {\r\n      console.error('DashboardService.getLicenseStats error:', error);\r\n      return {\r\n        total: 0,\r\n        active: 0,\r\n        expiringSoon: 0,\r\n        expired: 0,\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get user statistics (admin only)\r\n  async getUserStats(): Promise<UserStats> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/users/stats');\r\n      const data = processApiResponse(response);\r\n      return data.data || data;\r\n    } catch (error) {\r\n      console.error('DashboardService.getUserStats error:', error);\r\n      return {\r\n        total: 0,\r\n        active: 0,\r\n        newThisMonth: 0,\r\n        administrators: 0,\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get financial statistics\r\n  async getFinancialStats(): Promise<FinancialStats> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/financial/stats');\r\n      const data = processApiResponse(response);\r\n      return data.data || data;\r\n    } catch (error) {\r\n      console.error('DashboardService.getFinancialStats error:', error);\r\n      return {\r\n        totalRevenue: 0,\r\n        thisMonth: 0,\r\n        pending: 0,\r\n        transactions: 0,\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get recent applications\r\n  async getRecentApplications(): Promise<RecentApplication[]> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/applications/recent');\r\n      const data = processApiResponse(response);\r\n      return data.data || data || [];\r\n    } catch (error) {\r\n      console.error('DashboardService.getRecentApplications error:', error);\r\n      return []; // Return empty array instead of throwing\r\n    }\r\n  },\r\n\r\n  // Get recent activities\r\n  async getRecentActivities(): Promise<RecentActivity[]> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/activities/recent');\r\n      const data = processApiResponse(response);\r\n      return data.data || data || [];\r\n    } catch (error) {\r\n      console.error('DashboardService.getRecentActivities error:', error);\r\n      return []; // Return empty array instead of throwing\r\n    }\r\n  },\r\n\r\n  // Legacy method for backward compatibility\r\n  async getDashboardStats(): Promise<any> {\r\n    try {\r\n      const overview = await this.getOverview();\r\n      return overview.applications;\r\n    } catch (error) {\r\n      console.error('DashboardService.getDashboardStats error:', error);\r\n      // Return default application stats instead of throwing\r\n      return getDefaultOverview().applications;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAiFA,wBAAwB;AACxB,MAAM,qBAAqB,IAAyB,CAAC;QACnD,cAAc;YACZ,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,cAAc;YACd,YAAY;YACZ,OAAO;QACT;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,cAAc;YACd,gBAAgB;QAClB;QACA,UAAU;YACR,OAAO;YACP,QAAQ;YACR,cAAc;YACd,SAAS;QACX;QACA,WAAW;YACT,cAAc;YACd,WAAW;YACX,SAAS;YACT,cAAc;QAChB;QACA,WAAW,IAAI,OAAO,WAAW;IACnC,CAAC;AAEM,MAAM,mBAAmB;IAC9B,8CAA8C;IAC9C,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAEhC,wDAAwD;YACxD,OAAO;gBACL,cAAc,KAAK,YAAY,IAAI,qBAAqB,YAAY;gBACpE,OAAO,KAAK,KAAK,IAAI,qBAAqB,KAAK;gBAC/C,UAAU,KAAK,QAAQ,IAAI,qBAAqB,QAAQ;gBACxD,WAAW,KAAK,SAAS,IAAI,qBAAqB,SAAS;gBAC3D,WAAW,KAAK,SAAS,IAAI,IAAI,OAAO,WAAW;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,0CAA0C;YAC1C,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,SAAS;YACX;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,gBAAgB;YAClB;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBACL,cAAc;gBACd,WAAW;gBACX,SAAS;gBACT,cAAc;YAChB;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI,QAAQ,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO,EAAE,EAAE,yCAAyC;QACtD;IACF;IAEA,wBAAwB;IACxB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI,QAAQ,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,OAAO,EAAE,EAAE,yCAAyC;QACtD;IACF;IAEA,2CAA2C;IAC3C,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW;YACvC,OAAO,SAAS,YAAY;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,uDAAuD;YACvD,OAAO,qBAAqB,YAAY;QAC1C;IACF;AACF", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport Link from 'next/link';\r\n\r\nimport { dashboardService, DashboardOverview, RecentApplication, RecentActivity } from '@/services/dashboardService';\r\nimport '@/styles/dashboard.css';\r\n\r\nexport default function Dashboard() {\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [dashboardData, setDashboardData] = useState<DashboardOverview | null>(null);\r\n  const [recentApplications, setRecentApplications] = useState<RecentApplication[]>([]);\r\n  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);\r\n  const [isMounted, setIsMounted] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  // Helper function to calculate pending applications\r\n  const getPendingApplicationsCount = () => {\r\n    if (!dashboardData?.applications) return 0;\r\n    return dashboardData.applications.pending || 0;\r\n  };\r\n\r\n  // Helper function to get new submissions count\r\n  const getNewSubmissionsCount = () => {\r\n    if (!dashboardData?.applications) return 0;\r\n    return dashboardData.applications.submitted || 0;\r\n  };\r\n\r\n  // Set mounted state to prevent hydration errors\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Only run on client side to prevent hydration errors\r\n    if (typeof window === 'undefined') return;\r\n\r\n    // Listen for tab changes from header\r\n    const handleTabChange = (event: CustomEvent) => {\r\n      setActiveTab(event.detail.tab);\r\n    };\r\n\r\n    window.addEventListener('tabChange', handleTabChange as EventListener);\r\n\r\n    return () => {\r\n      window.removeEventListener('tabChange', handleTabChange as EventListener);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Fetch dashboard data\r\n    const fetchDashboardData = async () => {\r\n      try {\r\n        setStatsLoading(true);\r\n        setError('');\r\n\r\n        // Fetch overview data and recent items in parallel\r\n        const [overview, applications, activities] = await Promise.all([\r\n          dashboardService.getOverview().catch(() => null),\r\n          dashboardService.getRecentApplications().catch(() => []),\r\n          dashboardService.getRecentActivities().catch(() => [])\r\n        ]);\r\n\r\n        setDashboardData(overview);\r\n        setRecentApplications(applications);\r\n        setRecentActivities(activities);\r\n      } catch (error) {\r\n        console.error('Failed to fetch dashboard data:', error);\r\n        setError('Failed to load dashboard data. Please try refreshing the page.');\r\n      } finally {\r\n        setStatsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDashboardData();\r\n  }, []);\r\n\r\n  // Don't render anything until mounted to prevent hydration errors\r\n  if (!isMounted) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if there's an error\r\n  if (error) {\r\n    return (\r\n      <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4\">\r\n            <div className=\"flex\">\r\n              <div className=\"flex-shrink-0\">\r\n                <i className=\"ri-error-warning-line text-red-400\"></i>\r\n              </div>\r\n              <div className=\"ml-3\">\r\n                <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">Error Loading Dashboard</h3>\r\n                <div className=\"mt-2 text-sm text-red-700 dark:text-red-300\">\r\n                  <p>{error}</p>\r\n                </div>\r\n                <div className=\"mt-4\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => window.location.reload()}\r\n                    className=\"bg-red-100 dark:bg-red-800 px-3 py-2 rounded-md text-sm font-medium text-red-800 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-700\"\r\n                  >\r\n                    Retry\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Tab content sections */}\r\n\r\n        {/* Overview Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'overview' ? '' : 'hidden'}`}>\r\n          {/* Page header */}\r\n          <div className=\"mb-6\">\r\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n              <div>\r\n                <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Dashboard Overview</h1>\r\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n                  Comprehensive view of your licenses, spectrum, users, and financial activities.\r\n                </p>\r\n              </div>\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto\">\r\n                <div className=\"flex space-x-3 place-content-start\">\r\n                  <div className=\"relative\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap w-full\"\r\n                    >\r\n                      <div className=\"w-4 h-4 flex items-center justify-center mr-2\">\r\n                        <i className=\"ri-calendar-line\"></i>\r\n                      </div>\r\n                      May 7, 2025\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"relative\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Key Metrics Section */}\r\n          <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden\">\r\n            <div className=\"p-6\">\r\n              <h3 className=\"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4\">Key Metrics</h3>\r\n              <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\r\n                {/* License Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-key-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Licenses</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                          ) : (\r\n                            dashboardData?.licenses?.total || 0\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-yellow-600\">{!isMounted || statsLoading ? '...' : dashboardData?.licenses?.expiringSoon || 0}</span> expiring soon\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/dashboard/licenses\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* User Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-user-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Users</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                          ) : (\r\n                            dashboardData?.users?.total || 0\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-green-600\">{!isMounted || statsLoading ? '...' : dashboardData?.users?.newThisMonth || 0}</span> new this month\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/users\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Financial Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-money-dollar-circle-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Revenue (<strong>MWK</strong>)</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-16 rounded\"></div>\r\n                          ) : (\r\n                            `${((dashboardData?.financial?.totalRevenue || 0) / 1000000).toFixed(1)}M`\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-green-600\">{!isMounted || statsLoading ? '...' : `${((dashboardData?.financial?.thisMonth || 0) / 1000000).toFixed(1)}M`}</span> this month\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/dashboard/financial\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Pending Applications Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-orange-100 dark:bg-orange-900 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-file-list-3-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending Applications</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\" title={!isMounted || statsLoading ? 'Loading...' : `Submitted: ${dashboardData?.applications?.submitted || 0}, Under Review: ${dashboardData?.applications?.under_review || 0}, Evaluation: ${dashboardData?.applications?.evaluation || 0}`}>\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-8 rounded\"></div>\r\n                          ) : (\r\n                            getPendingApplicationsCount()\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-orange-600\">{!isMounted || statsLoading ? '...' : getNewSubmissionsCount()}</span> new submissions\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/applications\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View Applications\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Licenses Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'licenses' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">License Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Manage and monitor all telecommunications licenses.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-key-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Licenses</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,482</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,425</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Expiring Soon</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">57</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-close-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Expired</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">12</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent License Applications</h3>\r\n                <Link href=\"/dashboard/licenses\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"overflow-x-auto\">\r\n                <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n                  <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n                    <tr>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">License ID</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Company</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Type</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Status</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Expiry</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                    {statsLoading ? (\r\n                      // Loading skeleton\r\n                      Array.from({ length: 3 }).map((_, index) => (\r\n                        <tr key={index}>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-24 rounded\"></div>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-32 rounded\"></div>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-20 rounded\"></div>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-16 rounded-full\"></div>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-20 rounded\"></div>\r\n                          </td>\r\n                        </tr>\r\n                      ))\r\n                    ) : recentApplications.length > 0 ? (\r\n                      recentApplications.map((application) => (\r\n                        <tr key={application.application_id}>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                            {application.application_number}\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                            {application.applicant?.company_name || 'N/A'}\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                            {application.license_category?.category_name || 'N/A'}\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\r\n                              application.status === 'approved' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :\r\n                              application.status === 'submitted' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :\r\n                              application.status === 'under_review' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :\r\n                              application.status === 'evaluation' ? 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200' :\r\n                              application.status === 'rejected' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :\r\n                              'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200'\r\n                            }`}>\r\n                              {application.status.charAt(0).toUpperCase() + application.status.slice(1).replace('_', ' ')}\r\n                            </span>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                            {new Date(application.created_at).toLocaleDateString()}\r\n                          </td>\r\n                        </tr>\r\n                      ))\r\n                    ) : (\r\n                      <tr>\r\n                        <td colSpan={5} className=\"px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400\">\r\n                          No recent applications found\r\n                        </td>\r\n                      </tr>\r\n                    )}\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Users Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'users' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">User Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Manage system users and their access permissions.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-user-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Users</p>\r\n                  <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {!isMounted || statsLoading ? (\r\n                      <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                    ) : (\r\n                      dashboardData?.users?.total || 0\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-user-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active Users</p>\r\n                  <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {!isMounted || statsLoading ? (\r\n                      <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                    ) : (\r\n                      dashboardData?.users?.active || 0\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-user-add-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">New This Month</p>\r\n                  <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {!isMounted || statsLoading ? (\r\n                      <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                    ) : (\r\n                      dashboardData?.users?.newThisMonth || 0\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-shield-user-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Administrators</p>\r\n                  <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {!isMounted || statsLoading ? (\r\n                      <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-8 rounded\"></div>\r\n                    ) : (\r\n                      dashboardData?.users?.administrators || 0\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent User Activity</h3>\r\n                <Link href=\"/users\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                {statsLoading ? (\r\n                  // Loading skeleton\r\n                  Array.from({ length: 3 }).map((_, index) => (\r\n                    <div key={index} className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex-shrink-0\">\r\n                        <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 w-8 h-8 rounded-full\"></div>\r\n                      </div>\r\n                      <div className=\"min-w-0 flex-1\">\r\n                        <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-48 rounded mb-2\"></div>\r\n                        <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-3 w-20 rounded\"></div>\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                ) : recentActivities.length > 0 ? (\r\n                  recentActivities.map((activity) => (\r\n                    <div key={activity.audit_id} className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex-shrink-0\">\r\n                        <div className={`w-8 h-8 flex items-center justify-center rounded-full ${\r\n                          activity.action === 'create' ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400' :\r\n                          activity.action === 'update' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' :\r\n                          activity.action === 'delete' ? 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400' :\r\n                          'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400'\r\n                        }`}>\r\n                          <i className={`${\r\n                            activity.action === 'create' ? 'ri-add-line' :\r\n                            activity.action === 'update' ? 'ri-edit-line' :\r\n                            activity.action === 'delete' ? 'ri-delete-bin-line' :\r\n                            'ri-eye-line'\r\n                          }`}></i>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"min-w-0 flex-1\">\r\n                        <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                          {activity.description || `${activity.action} ${activity.resource_type}`}\r\n                          {activity.user && ` by ${activity.user.first_name} ${activity.user.last_name}`}\r\n                        </p>\r\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          {new Date(activity.created_at).toLocaleString()}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                ) : (\r\n                  <div className=\"text-center text-sm text-gray-500 dark:text-gray-400\">\r\n                    No recent activities found\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Transactions Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'transactions' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Financial Transactions</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor payments, invoices, and financial activities.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-money-dollar-circle-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Revenue (MWK)</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">115.4M</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-exchange-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">This Month (MWK)</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">8.7M</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">23</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-purple-100 dark:bg-purple-900 rounded-lg\">\r\n                  <i className=\"ri-file-list-line text-xl text-purple-600 dark:text-purple-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Transactions</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">4,892</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent Transactions</h3>\r\n                <Link href=\"/dashboard/financial\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400\">\r\n                      <i className=\"ri-check-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Payment of MWK 2.450M received from Acme Corp</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">3 hours ago</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400\">\r\n                      <i className=\"ri-file-text-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Invoice INV-2025-0234 generated</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">5 hours ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Spectrum Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'spectrum' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Spectrum Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor frequency allocations and spectrum usage.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-radio-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Allocations</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,248</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-signal-tower-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,156</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-bar-chart-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Utilization</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">78%</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-alert-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Interference Issues</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">5</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Spectrum Bands Overview</h3>\r\n                <Link href=\"/dashboard/spectrum\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">VHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">30-300 MHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">85%</span>\r\n                    </div>\r\n                    <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1\">\r\n                      <div className=\"bg-blue-600 dark:bg-blue-500 h-2 rounded-full w-[85%]\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">UHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">300-3000 MHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">72%</span>\r\n                    </div>\r\n                    <div className=\"progress-container\">\r\n                      <div className=\"progress-fill progress-green progress-bar-72\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">SHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">3-30 GHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">45%</span>\r\n                    </div>\r\n                    <div className=\"progress-container\">\r\n                      <div className=\"progress-fill progress-yellow progress-bar-45\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Compliance Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'compliance' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Compliance Overview</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor regulatory compliance and audit information.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-shield-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Compliance Rate</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">92.1%</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-file-shield-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Audits Completed</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">156</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-alert-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Open Issues</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">8</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending Reviews</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">23</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent Compliance Activities</h3>\r\n                <Link href=\"/dashboard/audit\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400\">\r\n                      <i className=\"ri-check-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Compliance audit completed for Global Tech Inc.</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">2 hours ago</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400\">\r\n                      <i className=\"ri-alert-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Non-compliance issue detected for Quantum Solutions</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">4 hours ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </main>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC7E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACpF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,oDAAoD;IACpD,MAAM,8BAA8B;QAClC,IAAI,CAAC,eAAe,cAAc,OAAO;QACzC,OAAO,cAAc,YAAY,CAAC,OAAO,IAAI;IAC/C;IAEA,+CAA+C;IAC/C,MAAM,yBAAyB;QAC7B,IAAI,CAAC,eAAe,cAAc,OAAO;QACzC,OAAO,cAAc,YAAY,CAAC,SAAS,IAAI;IACjD;IAEA,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sDAAsD;QACtD,wCAAmC;;QAEnC,qCAAqC;QACrC,MAAM;IASR,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uBAAuB;QACvB,MAAM,qBAAqB;YACzB,IAAI;gBACF,gBAAgB;gBAChB,SAAS;gBAET,mDAAmD;gBACnD,MAAM,CAAC,UAAU,cAAc,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC7D,mIAAA,CAAA,mBAAgB,CAAC,WAAW,GAAG,KAAK,CAAC,IAAM;oBAC3C,mIAAA,CAAA,mBAAgB,CAAC,qBAAqB,GAAG,KAAK,CAAC,IAAM,EAAE;oBACvD,mIAAA,CAAA,mBAAgB,CAAC,mBAAmB,GAAG,KAAK,CAAC,IAAM,EAAE;iBACtD;gBAED,iBAAiB;gBACjB,sBAAsB;gBACtB,oBAAoB;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,SAAS;YACX,SAAU;gBACR,gBAAgB;YAClB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,kEAAkE;IAClE,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,uCAAuC;IACvC,IAAI,OAAO;QACT,qBACE,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;sDAAG;;;;;;;;;;;kDAEN,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4CACrC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUjB;IAEA,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAIb,8OAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCAEvE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAI/D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;;;;;;;;;;;4DACT;;;;;;;;;;;;8DAIV,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsE;;;;;;kDACpF,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFACZ,CAAC,aAAa,6BACb,8OAAC;gFAAI,WAAU;;;;;uFAEf,eAAe,UAAU,SAAS;;;;;;;;;;;kFAIxC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;;8FACd,8OAAC;oFAAK,WAAU;8FAAmB,CAAC,aAAa,eAAe,QAAQ,eAAe,UAAU,gBAAgB;;;;;;gFAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAKlI,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAsB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAOvS,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFACZ,CAAC,aAAa,6BACb,8OAAC;gFAAI,WAAU;;;;;uFAEf,eAAe,OAAO,SAAS;;;;;;;;;;;kFAIrC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;;8FACd,8OAAC;oFAAK,WAAU;8FAAkB,CAAC,aAAa,eAAe,QAAQ,eAAe,OAAO,gBAAgB;;;;;;gFAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAK9H,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAO1R,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;;4EAAuD;0FAAS,8OAAC;0FAAO;;;;;;4EAAY;;;;;;;kFAClG,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFACZ,CAAC,aAAa,6BACb,8OAAC;gFAAI,WAAU;;;;;uFAEf,GAAG,CAAC,CAAC,eAAe,WAAW,gBAAgB,CAAC,IAAI,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;kFAIhF,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;;8FACd,8OAAC;oFAAK,WAAU;8FAAkB,CAAC,aAAa,eAAe,QAAQ,GAAG,CAAC,CAAC,eAAe,WAAW,aAAa,CAAC,IAAI,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;gFAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAK9J,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAuB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAOxS,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAA0D,OAAO,CAAC,aAAa,eAAe,eAAe,CAAC,WAAW,EAAE,eAAe,cAAc,aAAa,EAAE,gBAAgB,EAAE,eAAe,cAAc,gBAAgB,EAAE,cAAc,EAAE,eAAe,cAAc,cAAc,GAAG;sFAClT,CAAC,aAAa,6BACb,8OAAC;gFAAI,WAAU;;;;;uFAEf;;;;;;;;;;;kFAIN,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;;8FACd,8OAAC;oFAAK,WAAU;8FAAmB,CAAC,aAAa,eAAe,QAAQ;;;;;;gFAAgC;;;;;;;;;;;;;;;;;;;;;;;;kEAKhH,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWzS,8OAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCACvE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAA0C;;;;;;;;;;;;kDAIvF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,WAAU;8DACf,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,8OAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,8OAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,8OAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,8OAAC;gEAAG,WAAU;0EAAoG;;;;;;;;;;;;;;;;;8DAGtH,8OAAC;oDAAM,WAAU;8DACd,eACC,mBAAmB;oDACnB,MAAM,IAAI,CAAC;wDAAE,QAAQ;oDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;;;;;;;;;;;;2DAdV;;;;oEAkBT,mBAAmB,MAAM,GAAG,IAC9B,mBAAmB,GAAG,CAAC,CAAC,4BACtB,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,YAAY,kBAAkB;;;;;;8EAEjC,8OAAC;oEAAG,WAAU;8EACX,YAAY,SAAS,EAAE,gBAAgB;;;;;;8EAE1C,8OAAC;oEAAG,WAAU;8EACX,YAAY,gBAAgB,EAAE,iBAAiB;;;;;;8EAElD,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAW,CAAC,yDAAyD,EACzE,YAAY,MAAM,KAAK,aAAa,sEACpC,YAAY,MAAM,KAAK,cAAc,kEACrC,YAAY,MAAM,KAAK,iBAAiB,0EACxC,YAAY,MAAM,KAAK,eAAe,0EACtC,YAAY,MAAM,KAAK,aAAa,8DACpC,iEACA;kFACC,YAAY,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,YAAY,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;8EAG3F,8OAAC;oEAAG,WAAU;8EACX,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;;2DAvB/C,YAAY,cAAc;;;;kFA4BrC,8OAAC;kEACC,cAAA,8OAAC;4DAAG,SAAS;4DAAG,WAAU;sEAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAa3G,8OAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,UAAU,KAAK,UAAU;;sCACpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAI,WAAU;kEACZ,CAAC,aAAa,6BACb,8OAAC;4DAAI,WAAU;;;;;mEAEf,eAAe,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;8CAMzC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAI,WAAU;kEACZ,CAAC,aAAa,6BACb,8OAAC;4DAAI,WAAU;;;;;mEAEf,eAAe,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM1C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAI,WAAU;kEACZ,CAAC,aAAa,6BACb,8OAAC;4DAAI,WAAU;;;;;mEAEf,eAAe,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;8CAMhD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAI,WAAU;kEACZ,CAAC,aAAa,6BACb,8OAAC;4DAAI,WAAU;;;;;mEAEf,eAAe,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA0C;;;;;;;;;;;;kDAI1E,8OAAC;wCAAI,WAAU;kDACZ,eACC,mBAAmB;wCACnB,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;+CANT;;;;wDAUV,iBAAiB,MAAM,GAAG,IAC5B,iBAAiB,GAAG,CAAC,CAAC,yBACpB,8OAAC;gDAA4B,WAAU;;kEACrC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAW,CAAC,sDAAsD,EACrE,SAAS,MAAM,KAAK,WAAW,sEAC/B,SAAS,MAAM,KAAK,WAAW,kEAC/B,SAAS,MAAM,KAAK,WAAW,8DAC/B,iEACA;sEACA,cAAA,8OAAC;gEAAE,WAAW,GACZ,SAAS,MAAM,KAAK,WAAW,gBAC/B,SAAS,MAAM,KAAK,WAAW,iBAC/B,SAAS,MAAM,KAAK,WAAW,uBAC/B,eACA;;;;;;;;;;;;;;;;kEAGN,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;;oEACV,SAAS,WAAW,IAAI,GAAG,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;oEACtE,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,IAAI,CAAC,SAAS,EAAE;;;;;;;0EAEhF,8OAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,SAAS,UAAU,EAAE,cAAc;;;;;;;;;;;;;+CAtBzC,SAAS,QAAQ;;;;sEA4B7B,8OAAC;4CAAI,WAAU;sDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUhF,8OAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,iBAAiB,KAAK,UAAU;;sCAC3E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAA0C;;;;;;;;;;;;kDAIxF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAG5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASpE,8OAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCACvE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAA0C;;;;;;;;;;;;kDAIvF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU7B,8OAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,eAAe,KAAK,UAAU;;sCACzE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DAA0C;;;;;;;;;;;;kDAIpF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAG5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,8OAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5E", "debugId": null}}]}