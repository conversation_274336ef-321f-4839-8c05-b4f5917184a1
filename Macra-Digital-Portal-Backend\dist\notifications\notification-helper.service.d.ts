import { NotificationsService } from './notifications.service';
import { EmailTemplateService } from './email-template.service';
export declare class NotificationHelperService {
    private readonly notificationsService;
    private readonly emailTemplateService;
    constructor(notificationsService: NotificationsService, emailTemplateService: EmailTemplateService);
    notifyApplicationStatus(applicationId: string, applicantId: string, applicantEmail: string, applicantPhone: string, applicationNumber: string, status: string, createdBy: string, applicantName?: string, licenseType?: string, oldStatus?: string): Promise<void>;
    notifyTaskAssignment(taskId: string, assigneeId: string, assigneeEmail: string, assigneePhone: string, taskTitle: string, taskDescription: string, createdBy: string, assigneeName?: string, applicationNumber?: string, applicantName?: string, priority?: string, dueDate?: string): Promise<void>;
    notifyLicenseExpiry(licenseId: string, customerId: string, customerEmail: string, customerPhone: string, licenseNumber: string, expiryDate: Date, daysUntilExpiry: number, createdBy: string): Promise<void>;
    notifyTaskCompletion(taskId: string, applicationId: string, applicantId: string, applicantEmail: string, assigneeId: string, assigneeEmail: string, taskTitle: string, applicationNumber: string, outcome: string, createdBy: string, applicantName?: string, licenseType?: string, comments?: string, nextSteps?: string): Promise<void>;
    notifyLicenseApproval(applicationId: string, applicantId: string, applicantEmail: string, applicationNumber: string, licenseNumber: string, licenseType: string, createdBy: string, applicantName?: string, expiryDate?: string): Promise<void>;
}
