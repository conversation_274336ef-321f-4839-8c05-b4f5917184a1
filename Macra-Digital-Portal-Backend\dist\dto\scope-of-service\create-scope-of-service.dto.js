"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateScopeOfServiceDto = void 0;
const class_validator_1 = require("class-validator");
class CreateScopeOfServiceDto {
    application_id;
    nature_of_service;
    premises;
    transport_type;
    customer_assistance;
}
exports.CreateScopeOfServiceDto = CreateScopeOfServiceDto;
__decorate([
    (0, class_validator_1.IsUUID)('4', { message: 'Application ID not valid!' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Application ID is required' }),
    __metadata("design:type", String)
], CreateScopeOfServiceDto.prototype, "application_id", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Nature of service contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(300, { message: 'Nature of service must not exceed 300 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nature of service is required' }),
    __metadata("design:type", String)
], CreateScopeOfServiceDto.prototype, "nature_of_service", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Premises contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(300, { message: 'Premises must not exceed 300 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Premises is required' }),
    __metadata("design:type", String)
], CreateScopeOfServiceDto.prototype, "premises", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Transport type contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(300, { message: 'Transport type must not exceed 300 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Transport type is required' }),
    __metadata("design:type", String)
], CreateScopeOfServiceDto.prototype, "transport_type", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Customer assistance contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(300, { message: 'Customer assistance must not exceed 300 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Customer assistance is required' }),
    __metadata("design:type", String)
], CreateScopeOfServiceDto.prototype, "customer_assistance", void 0);
//# sourceMappingURL=create-scope-of-service.dto.js.map