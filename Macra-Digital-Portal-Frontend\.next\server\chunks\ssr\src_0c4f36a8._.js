module.exports = {

"[project]/src/services/licenseCategoryService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addCodesToCategories": (()=>addCodesToCategories),
    "findCategoryByCode": (()=>findCategoryByCode),
    "findCategoryById": (()=>findCategoryById),
    "generateCategoryCode": (()=>generateCategoryCode),
    "licenseCategoryService": (()=>licenseCategoryService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/cacheService.ts [app-ssr] (ecmascript)");
;
;
const generateCategoryCode = (name)=>{
    return name.toLowerCase().replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
};
const addCodesToCategories = (categories)=>{
    return categories.map((category)=>({
            ...category,
            code: generateCategoryCode(category.name),
            children: category.children ? addCodesToCategories(category.children) : undefined
        }));
};
const findCategoryByCode = (categories, code)=>{
    for (const category of categories){
        if (category.code === code) {
            return category;
        }
        if (category.children) {
            const found = findCategoryByCode(category.children, code);
            if (found) return found;
        }
    }
    return null;
};
const findCategoryById = (categories, id)=>{
    for (const category of categories){
        if (category.license_category_id === id) {
            return category;
        }
        if (category.children) {
            const found = findCategoryById(category.children, id);
            if (found) return found;
        }
    }
    return null;
};
const licenseCategoryService = {
    // Get all license categories with pagination
    async getLicenseCategories (query = {}) {
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach(([key, value])=>{
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append(`filter.${key}`, v));
                } else {
                    params.set(`filter.${key}`, value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories?${params.toString()}`);
        return response.data;
    },
    // Get license category by ID with timeout and retry handling
    async getLicenseCategory (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/${id}`, {
                timeout: 30000
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching license category:', error);
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            throw error;
        }
    },
    // Get license categories by license type with improved error handling
    async getLicenseCategoriesByType (licenseTypeId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`, {
                timeout: 30000
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching license categories by type:', error);
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            if (error.response?.status === 429) {
                throw new Error('Too many requests - please wait a moment and try again');
            }
            throw error;
        }
    },
    // Create new license category
    async createLicenseCategory (licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/license-categories', licenseCategoryData);
        return response.data;
    },
    // Update license category
    async updateLicenseCategory (id, licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/license-categories/${id}`, licenseCategoryData);
        return response.data;
    },
    // Delete license category
    async deleteLicenseCategory (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(`/license-categories/${id}`);
        return response.data;
    },
    // Get all license categories (simple list for dropdowns) with caching
    async getAllLicenseCategories () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_KEYS"].LICENSE_CATEGORIES, async ()=>{
            console.log('Fetching license categories from API...');
            // Reduce limit to avoid rate limiting
            const response = await this.getLicenseCategories({
                limit: 100
            });
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_TTL"].LONG // Cache for 15 minutes
        );
    },
    // Get hierarchical tree of categories for a license type with caching
    async getCategoryTree (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`category-tree-${licenseTypeId}`, async ()=>{
            console.log(`Fetching category tree for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/tree`);
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get root categories (no parent) for a license type with caching
    async getRootCategories (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`root-categories-${licenseTypeId}`, async ()=>{
            console.log(`Fetching root categories for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/root`);
            return response.data;
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get license categories for parent selection dropdown
    async getCategoriesForParentSelection (licenseTypeId, excludeId) {
        try {
            const params = excludeId ? {
                excludeId
            } : {};
            console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);
            // Try the new endpoint first
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, {
                    params
                });
                if (response.data && Array.isArray(response.data.data)) {
                    console.log('✅ Valid array response with', response.data.data.length, 'items');
                    return response.data.data;
                } else {
                    console.warn('⚠️ API returned non-array data:', response.data);
                    return [];
                }
            } catch (newEndpointError) {
                console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);
                // Fallback to existing endpoint
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`);
                console.log('🔄 Fallback response:', response.data);
                if (response.data && Array.isArray(response.data)) {
                    // Filter out the excluded category if specified
                    let categories = response.data;
                    if (excludeId) {
                        categories = categories.filter((cat)=>cat.license_category_id !== excludeId);
                    }
                    console.log('✅ Fallback successful with', categories.length, 'items');
                    return categories;
                } else {
                    console.warn('⚠️ Fallback also returned non-array data:', response.data);
                    return [];
                }
            }
        } catch (error) {
            return [];
        }
    },
    // Get potential parent categories for a license type
    async getPotentialParents (licenseTypeId, excludeId) {
        const params = excludeId ? {
            excludeId
        } : {};
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, {
            params
        });
        return response.data;
    }
};
}}),
"[project]/src/config/licenseTypeStepConfig.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * License Type Step Configuration System
 *
 * SINGLE SOURCE OF TRUTH for all license type step configurations
 *
 * This is the consolidated configuration system that defines:
 * - Which form steps are required for each license type
 * - Step order and navigation flow
 * - Validation requirements and estimated times
 * - Fallback configurations for unknown license types
 *
 * Supports the 5 specified license type codes:
 * - telecommunications
 * - postal_services
 * - standards_compliance
 * - broadcasting
 * - spectrum_management
 *
 * Features:
 * - Optimized step loading based on license type codes
 * - Automatic fallback for unsupported types
 * - Smart license type resolution (UUID, code, name mapping)
 * - Comprehensive helper functions for navigation and progress tracking
 */ __turbopack_context__.s({
    "LICENSE_TYPE_STEP_CONFIGS": (()=>LICENSE_TYPE_STEP_CONFIGS),
    "calculateProgress": (()=>calculateProgress),
    "getLicenseTypeStepConfig": (()=>getLicenseTypeStepConfig),
    "getNextStep": (()=>getNextStep),
    "getOptimizedStepConfig": (()=>getOptimizedStepConfig),
    "getOptionalSteps": (()=>getOptionalSteps),
    "getPreviousStep": (()=>getPreviousStep),
    "getRequiredSteps": (()=>getRequiredSteps),
    "getStepByIndex": (()=>getStepByIndex),
    "getStepByRoute": (()=>getStepByRoute),
    "getStepIndex": (()=>getStepIndex),
    "getStepsByLicenseTypeCode": (()=>getStepsByLicenseTypeCode),
    "getSupportedLicenseTypeCodes": (()=>getSupportedLicenseTypeCodes),
    "getTotalSteps": (()=>getTotalSteps),
    "isLicenseTypeCodeSupported": (()=>isLicenseTypeCodeSupported),
    "setLicenseTypeUUIDToCodeMap": (()=>setLicenseTypeUUIDToCodeMap)
});
// Base steps that can be used across license types
const BASE_STEPS = {
    applicantInfo: {
        id: 'applicant-info',
        name: 'Applicant Information',
        component: 'ApplicantInfo',
        route: 'applicant-info',
        required: true,
        description: 'Personal or company information of the applicant',
        estimatedTime: '5'
    },
    addressInfo: {
        id: 'address-info',
        name: 'Address Information',
        component: 'AddressInfo',
        route: 'address-info',
        required: true,
        description: 'Physical and postal address details',
        estimatedTime: '3'
    },
    contactInfo: {
        id: 'contact-info',
        name: 'Contact Information',
        component: 'ContactInfo',
        route: 'contact-info',
        required: true,
        description: 'Contact details and communication preferences',
        estimatedTime: '5'
    },
    management: {
        id: 'management',
        name: 'Management Structure',
        component: 'Management',
        route: 'management',
        required: false,
        description: 'Management team and organizational structure',
        estimatedTime: '8'
    },
    professionalServices: {
        id: 'professional-services',
        name: 'Professional Services',
        component: 'ProfessionalServices',
        route: 'professional-services',
        required: false,
        description: 'External consultants and service providers',
        estimatedTime: '6'
    },
    serviceScope: {
        id: 'service-scope',
        name: 'Service Scope',
        component: 'ServiceScope',
        route: 'service-scope',
        required: true,
        description: 'Services offered and geographic coverage',
        estimatedTime: '8'
    },
    legalHistory: {
        id: 'legal-history',
        name: 'Legal History',
        component: 'LegalHistory',
        route: 'legal-history',
        required: true,
        description: 'Legal compliance and regulatory history',
        estimatedTime: '5'
    },
    documents: {
        id: 'documents',
        name: 'Required Documents',
        component: 'Documents',
        route: 'documents',
        required: true,
        description: 'Upload required documents for license application',
        estimatedTime: '10'
    },
    submit: {
        id: 'submit',
        name: 'Finalise Application',
        component: 'Submit',
        route: 'submit',
        required: true,
        description: 'Final Review and finalise and submission of application',
        estimatedTime: '5'
    }
};
const LICENSE_TYPE_STEP_CONFIGS = {
    telecommunications: {
        licenseTypeId: 'telecommunications',
        name: 'Telecommunications License',
        description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.management,
            BASE_STEPS.serviceScope,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '97 minutes',
        requirements: [
            'Business registration certificate',
            'Tax compliance certificate',
            'Technical specifications',
            'Financial statements',
            'Management CVs',
            'Network coverage plans'
        ]
    },
    postal_services: {
        licenseTypeId: 'postal_services',
        name: 'Postal Services License',
        description: 'License for postal and courier service providers',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '65 minutes',
        requirements: [
            'Business registration certificate',
            'Fleet inventory',
            'Service coverage map',
            'Insurance certificates',
            'Premises documentation'
        ]
    },
    standards_compliance: {
        licenseTypeId: 'standards_compliance',
        name: 'Standards Compliance License',
        description: 'License for standards compliance and certification services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.serviceScope,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '82 minutes',
        requirements: [
            'Accreditation certificates',
            'Technical competency proof',
            'Quality management system',
            'Laboratory facilities documentation',
            'Staff qualifications'
        ]
    },
    broadcasting: {
        licenseTypeId: 'broadcasting',
        name: 'Broadcasting License',
        description: 'License for radio and television broadcasting services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.management,
            BASE_STEPS.serviceScope,
            BASE_STEPS.professionalServices,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '86 minutes',
        requirements: [
            'Broadcasting equipment specifications',
            'Content programming plan',
            'Studio facility documentation',
            'Transmission coverage maps',
            'Local content compliance plan'
        ]
    },
    spectrum_management: {
        licenseTypeId: 'spectrum_management',
        name: 'Spectrum Management License',
        description: 'License for radio frequency spectrum management and allocation',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.management,
            BASE_STEPS.serviceScope,
            BASE_STEPS.professionalServices,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '89 minutes',
        requirements: [
            'Spectrum usage plan',
            'Technical interference analysis',
            'Equipment type approval',
            'Frequency coordination agreements',
            'Monitoring capabilities documentation'
        ]
    },
    clf: {
        licenseTypeId: 'clf',
        name: 'CLF License',
        description: 'Consumer Lending and Finance license',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.addressInfo,
            BASE_STEPS.contactInfo,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.legalHistory,
            BASE_STEPS.documents,
            BASE_STEPS.submit
        ],
        estimatedTotalTime: '51 minutes',
        requirements: [
            'Financial institution license',
            'Capital adequacy documentation',
            'Risk management framework',
            'Consumer protection policies',
            'Anti-money laundering procedures'
        ]
    }
};
// License type name to config key mapping
const LICENSE_TYPE_NAME_MAPPING = {
    'telecommunications': 'telecommunications',
    'postal services': 'postal_services',
    'postal_services': 'postal_services',
    'standards compliance': 'standards_compliance',
    'standards_compliance': 'standards_compliance',
    'broadcasting': 'broadcasting',
    'spectrum management': 'spectrum_management',
    'spectrum_management': 'spectrum_management',
    'clf': 'clf',
    'consumer lending and finance': 'clf'
};
// Default fallback configuration for unknown license types
const DEFAULT_FALLBACK_CONFIG = {
    licenseTypeId: 'default',
    name: 'Standard License Application',
    description: 'Standard license application process with all required steps',
    steps: [
        BASE_STEPS.applicantInfo,
        BASE_STEPS.addressInfo,
        BASE_STEPS.contactInfo,
        BASE_STEPS.management,
        BASE_STEPS.professionalServices,
        BASE_STEPS.serviceScope,
        BASE_STEPS.legalHistory,
        BASE_STEPS.documents,
        BASE_STEPS.submit
    ],
    estimatedTotalTime: '120 minutes',
    requirements: [
        'Business registration certificate',
        'Tax compliance certificate',
        'Financial statements',
        'Management CVs',
        'Professional qualifications',
        'Service documentation'
    ]
};
const getLicenseTypeStepConfig = (licenseTypeId)=>{
    // Check if licenseTypeId is valid
    if (!licenseTypeId || typeof licenseTypeId !== 'string') {
        return DEFAULT_FALLBACK_CONFIG;
    }
    // First try direct lookup with exact match
    let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];
    if (config) {
        return config;
    }
    // Try normalized lookup (lowercase with underscores)
    const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');
    config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];
    if (config) {
        return config;
    }
    // Try name mapping for common variations
    const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];
    if (mappedKey) {
        return LICENSE_TYPE_STEP_CONFIGS[mappedKey];
    }
    // If licenseTypeId looks like a UUID, try to get the code from license types
    if (isUUID(licenseTypeId)) {
        const code = getLicenseTypeCodeFromUUID(licenseTypeId);
        if (code) {
            const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];
            if (foundConfig) {
                return foundConfig;
            }
        }
    }
    // Try partial matching for known license type codes
    const knownCodes = Object.keys(LICENSE_TYPE_STEP_CONFIGS);
    const partialMatch = knownCodes.find((code)=>licenseTypeId.toLowerCase().includes(code) || code.includes(licenseTypeId.toLowerCase()));
    if (partialMatch) {
        return LICENSE_TYPE_STEP_CONFIGS[partialMatch];
    }
    return DEFAULT_FALLBACK_CONFIG;
};
// Helper function to check if a string is a UUID
const isUUID = (str)=>{
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
};
// Helper function to get license type code from UUID
// This will be populated by the license type service
let licenseTypeUUIDToCodeMap = {};
const setLicenseTypeUUIDToCodeMap = (map)=>{
    licenseTypeUUIDToCodeMap = map;
};
const getLicenseTypeCodeFromUUID = (uuid)=>{
    return licenseTypeUUIDToCodeMap[uuid] || null;
};
const getStepsByLicenseTypeCode = (licenseTypeCode)=>{
    // Validate known license type codes
    const validCodes = [
        'telecommunications',
        'postal_services',
        'standards_compliance',
        'broadcasting',
        'spectrum_management'
    ];
    if (validCodes.includes(licenseTypeCode)) {
        const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];
        if (config) {
            return config.steps;
        }
    }
    return DEFAULT_FALLBACK_CONFIG.steps;
};
const isLicenseTypeCodeSupported = (licenseTypeCode)=>{
    const validCodes = [
        'telecommunications',
        'postal_services',
        'standards_compliance',
        'broadcasting',
        'spectrum_management'
    ];
    return validCodes.includes(licenseTypeCode);
};
const getSupportedLicenseTypeCodes = ()=>{
    return [
        'telecommunications',
        'postal_services',
        'standards_compliance',
        'broadcasting',
        'spectrum_management'
    ];
};
const getStepByRoute = (licenseTypeId, stepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return null;
    return config.steps.find((step)=>step.route === stepRoute) || null;
};
const getStepByIndex = (licenseTypeId, stepIndex)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (stepIndex < 0 || stepIndex >= config.steps.length) return null;
    return config.steps[stepIndex];
};
const getStepIndex = (licenseTypeId, stepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config.steps.findIndex((step)=>step.route === stepRoute);
};
const getTotalSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config.steps.length;
};
const getRequiredSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config.steps.filter((step)=>step.required);
};
const getOptionalSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config.steps.filter((step)=>!step.required);
};
const calculateProgress = (licenseTypeId, completedSteps)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    const totalSteps = config.steps.length;
    const completed = completedSteps.length;
    return Math.round(completed / totalSteps * 100);
};
const getNextStep = (licenseTypeId, currentStepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);
    if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;
    return config.steps[currentIndex + 1];
};
const getPreviousStep = (licenseTypeId, currentStepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);
    if (currentIndex <= 0) return null;
    return config.steps[currentIndex - 1];
};
const getOptimizedStepConfig = (licenseTypeCode)=>{
    // Check if it's a supported license type code
    if (isLicenseTypeCodeSupported(licenseTypeCode)) {
        const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];
        return config;
    }
    return DEFAULT_FALLBACK_CONFIG;
};
}}),
"[project]/src/components/evaluation/EvaluationProgress.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseCategoryService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/licenseTypeStepConfig.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
// Cache for license data
const licenseDataCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const EvaluationProgress = ({ className = '' })=>{
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    // State
    const [applicationSteps, setApplicationSteps] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Get query parameters
    const licenseCategoryId = searchParams.get('license_category_id');
    const applicationId = searchParams.get('application_id');
    // Debug query parameters
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        console.log('🔍 EvaluationProgress Debug:', {
            pathname,
            licenseCategoryId,
            applicationId,
            allParams: Object.fromEntries(searchParams.entries())
        });
    }, [
        pathname,
        licenseCategoryId,
        applicationId,
        searchParams
    ]);
    // Get current step from pathname (memoized)
    const currentStepIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!applicationSteps.length) return -1;
        const pathSegments = pathname.split('/');
        const currentStepId = pathSegments[pathSegments.length - 1];
        return applicationSteps.findIndex((step)=>step.id === currentStepId);
    }, [
        pathname,
        applicationSteps
    ]);
    // Check cache for license data
    const getCachedLicenseData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((licenseCategoryId)=>{
        const cached = licenseDataCache.get(licenseCategoryId);
        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
            return cached.data;
        }
        return null;
    }, []);
    // Cache license data
    const cacheLicenseData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((licenseCategoryId, data)=>{
        licenseDataCache.set(licenseCategoryId, {
            data,
            timestamp: Date.now()
        });
    }, []);
    // Load application steps
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const loadSteps = async ()=>{
            // If no license_category_id, try to get it from application data
            let resolvedLicenseCategoryId = licenseCategoryId;
            if (!resolvedLicenseCategoryId && applicationId) {
                try {
                    console.log('🔍 EvaluationProgress: Trying to load application to get license_category_id');
                    const { applicationService } = await __turbopack_context__.r("[project]/src/services/applicationService.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                    const application = await applicationService.getApplication(applicationId);
                    resolvedLicenseCategoryId = application?.license_category_id;
                    console.log('🔍 EvaluationProgress: Got license_category_id from application:', resolvedLicenseCategoryId);
                } catch (err) {
                    console.error('Error loading application for license_category_id:', err);
                }
            }
            if (!resolvedLicenseCategoryId) {
                setError('License category ID is required');
                setLoading(false);
                return;
            }
            try {
                setLoading(true);
                setError(null);
                // Check cache first
                let licenseCategory = getCachedLicenseData(resolvedLicenseCategoryId);
                if (!licenseCategory) {
                    // Load license category from API
                    licenseCategory = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["licenseCategoryService"].getLicenseCategory(resolvedLicenseCategoryId);
                    if (!licenseCategory) {
                        throw new Error('License category not found');
                    }
                    // Cache the data
                    cacheLicenseData(resolvedLicenseCategoryId, licenseCategory);
                }
                // Debug license category data
                console.log('🔍 EvaluationProgress License Category Debug:', {
                    licenseCategory,
                    license_type: licenseCategory.license_type,
                    license_type_code: licenseCategory.license_type?.code,
                    category_code: licenseCategory.code
                });
                // Get license type code with fallback to URL path
                let licenseTypeCode = licenseCategory.license_type?.code || licenseCategory.code;
                // Fallback: extract license type from URL path
                if (!licenseTypeCode) {
                    const pathSegments = pathname.split('/');
                    const licenseTypeIndex = pathSegments.findIndex((segment)=>segment === 'applications') + 1;
                    const urlLicenseType = pathSegments[licenseTypeIndex];
                    console.log('🔄 Using license type from URL as fallback:', urlLicenseType);
                    licenseTypeCode = urlLicenseType;
                }
                if (!licenseTypeCode) {
                    console.error('❌ License type code not found:', {
                        licenseCategory,
                        license_type: licenseCategory.license_type,
                        available_fields: Object.keys(licenseCategory),
                        pathname,
                        pathSegments: pathname.split('/')
                    });
                    throw new Error('License type code not found');
                }
                console.log('✅ Using license type code:', licenseTypeCode);
                // Load steps based on license type
                let steps;
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isLicenseTypeCodeSupported"])(licenseTypeCode)) {
                    steps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getStepsByLicenseTypeCode"])(licenseTypeCode);
                } else {
                    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLicenseTypeStepConfig"])(licenseTypeCode);
                    steps = config.steps;
                }
                setApplicationSteps(steps);
            } catch (err) {
                console.error('Error loading application steps:', err);
                setError(err.message || 'Failed to load application steps');
            } finally{
                setLoading(false);
            }
        };
        loadSteps();
    }, [
        licenseCategoryId,
        getCachedLicenseData,
        cacheLicenseData
    ]);
    // Navigation handlers for evaluation
    const handleStepClick = (stepIndex)=>{
        // Prevent navigation to future steps if not editing an existing application
        if (!applicationId && stepIndex > currentStepIndex) {
            return;
        }
        const step = applicationSteps[stepIndex];
        // Extract license type from current pathname
        const pathSegments = pathname.split('/');
        const licenseTypeIndex = pathSegments.findIndex((segment)=>segment === 'applications') + 1;
        const licenseType = pathSegments[licenseTypeIndex];
        const params = new URLSearchParams();
        // Use the resolved license category ID or the original one
        const categoryId = licenseCategoryId || searchParams.get('license_category_id');
        if (categoryId) {
            params.set('license_category_id', categoryId);
        }
        if (applicationId) {
            params.set('application_id', applicationId);
        }
        // Navigate to evaluation URL instead of apply URL
        router.push(`/applications/${licenseType}/evaluate/${step.id}?${params.toString()}`);
    };
    // Loading state
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 205,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-3",
                        children: [
                            ...Array(5)
                        ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                        lineNumber: 209,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                        lineNumber: 210,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, i, true, {
                                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                lineNumber: 208,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 206,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                lineNumber: 204,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
            lineNumber: 203,
            columnNumber: 7
        }, this);
    }
    // Error state
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-700 p-6 ${className}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-error-warning-line text-2xl text-red-500 mb-2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 224,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-red-600 dark:text-red-400",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 225,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                lineNumber: 223,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
            lineNumber: 222,
            columnNumber: 7
        }, this);
    }
    // No steps available
    if (!applicationSteps.length) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-list-check text-2xl text-gray-400 mb-2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 236,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-500 dark:text-gray-400",
                        children: "No evaluation steps available"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 237,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                lineNumber: 235,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
            lineNumber: 234,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",
                children: "Evaluation Progress"
            }, void 0, false, {
                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                lineNumber: 245,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                style: {
                    maxHeight: 'calc(100vh - 17rem)',
                    overflowY: 'auto'
                },
                children: applicationSteps.map((step, index)=>{
                    const isCompleted = index < currentStepIndex;
                    const isCurrent = index === currentStepIndex;
                    const isClickable = applicationId || index <= currentStepIndex;
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `flex items-center space-x-3 p-3 rounded-lg transition-colors ${isClickable ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700' : 'cursor-not-allowed opacity-50'} ${isCurrent ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''}`,
                        onClick: ()=>isClickable && handleStepClick(index),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${isCompleted ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400' : isCurrent ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' : 'bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-400'}`,
                                children: isCompleted ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-check-line"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                    lineNumber: 278,
                                    columnNumber: 19
                                }, this) : index + 1
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                lineNumber: 270,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1 min-w-0",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `text-sm font-medium truncate ${isCurrent ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-gray-100'}`,
                                        children: step.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                        lineNumber: 286,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `text-xs truncate ${isCurrent ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}`,
                                        children: step.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                        lineNumber: 293,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                lineNumber: 285,
                                columnNumber: 15
                            }, this),
                            isCurrent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-shrink-0",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-2 h-2 bg-blue-500 rounded-full animate-pulse"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                    lineNumber: 305,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                lineNumber: 304,
                                columnNumber: 17
                            }, this)
                        ]
                    }, step.id, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 256,
                        columnNumber: 13
                    }, this);
                })
            }, void 0, false, {
                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                lineNumber: 249,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between text-sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-500 dark:text-gray-400",
                                children: [
                                    "Step ",
                                    Math.max(currentStepIndex + 1, 1),
                                    " of ",
                                    applicationSteps.length
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                lineNumber: 316,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-500 dark:text-gray-400",
                                children: [
                                    Math.round((currentStepIndex + 1) / applicationSteps.length * 100),
                                    "% Complete"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                                lineNumber: 319,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 315,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-blue-600 h-2 rounded-full transition-all duration-300",
                            style: {
                                width: `${(currentStepIndex + 1) / applicationSteps.length * 100}%`
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                            lineNumber: 324,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                        lineNumber: 323,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
                lineNumber: 314,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/evaluation/EvaluationProgress.tsx",
        lineNumber: 244,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = EvaluationProgress;
}}),
"[project]/src/components/evaluation/EvaluationLayout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationProgress$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/evaluation/EvaluationProgress.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
// Progress loading fallback
const ProgressLoadingFallback = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "animate-pulse",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"
                }, void 0, false, {
                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                    lineNumber: 40,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-3",
                    children: [
                        ...Array(5)
                    ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                    lineNumber: 44,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                    lineNumber: 45,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, i, true, {
                            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                            lineNumber: 43,
                            columnNumber: 11
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                    lineNumber: 41,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
            lineNumber: 39,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
        lineNumber: 38,
        columnNumber: 3
    }, this);
const EvaluationLayout = ({ children, applicationId, licenseTypeCode, currentStepRoute, onSubmit, onSave, onNext, onPrevious, isSubmitting = false, isSaving = false, showNextButton = true, showPreviousButton = true, showSaveButton = false, showSubmitButton = false, nextButtonText = 'Continue', previousButtonText = 'Back', saveButtonText = 'Save', submitButtonText = 'Submit', nextButtonDisabled = false, previousButtonDisabled = false, saveButtonDisabled = false, submitButtonDisabled = false, className = '', showProgress = true, progressFallback, stepValidationErrors = [], showStepInfo = true })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `min-h-screen bg-gray-50 overflow-y-auto  dark:bg-gray-900 ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "grid grid-cols-1 lg:grid-cols-4 gap-8 p-6  mb-20",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "lg:col-span-1",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "sticky top-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Suspense"], {
                            fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ProgressLoadingFallback, {}, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                lineNumber: 88,
                                columnNumber: 35
                            }, void 0),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationProgress$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                lineNumber: 89,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                            lineNumber: 88,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                        lineNumber: 87,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                    lineNumber: 86,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "lg:col-span-3",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",
                        children: [
                            showStepInfo && licenseTypeCode && currentStepRoute && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "text-sm font-medium text-blue-900 dark:text-blue-100",
                                                    children: [
                                                        "License Type: ",
                                                        licenseTypeCode.replace(/_/g, ' ').toUpperCase()
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                    lineNumber: 102,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-xs text-blue-700 dark:text-blue-300 mt-1",
                                                    children: [
                                                        "Current Step: ",
                                                        currentStepRoute.replace(/-/g, ' ').replace(/\b\w/g, (l)=>l.toUpperCase())
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                    lineNumber: 105,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                            lineNumber: 101,
                                            columnNumber: 21
                                        }, this),
                                        stepValidationErrors.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center text-red-600 dark:text-red-400",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    className: "ri-error-warning-line mr-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                    lineNumber: 111,
                                                    columnNumber: 25
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs",
                                                    children: [
                                                        stepValidationErrors.length,
                                                        " validation error",
                                                        stepValidationErrors.length !== 1 ? 's' : ''
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                    lineNumber: 112,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                            lineNumber: 110,
                                            columnNumber: 23
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                    lineNumber: 100,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                lineNumber: 99,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-6",
                                children: children
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                lineNumber: 120,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-t border-gray-200 dark:border-gray-700 px-6 py-4 bg-gray-50 dark:bg-gray-900/50 rounded-b-lg",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex space-x-3",
                                            children: showPreviousButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: onPrevious,
                                                disabled: previousButtonDisabled,
                                                className: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-arrow-left-line mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                        lineNumber: 135,
                                                        columnNumber: 25
                                                    }, this),
                                                    previousButtonText
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                lineNumber: 129,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                            lineNumber: 127,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex space-x-3",
                                            children: [
                                                showSaveButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: onSave,
                                                    disabled: saveButtonDisabled || isSaving,
                                                    className: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                                                    children: isSaving ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                className: "animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500",
                                                                xmlns: "http://www.w3.org/2000/svg",
                                                                fill: "none",
                                                                viewBox: "0 0 24 24",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                                        className: "opacity-25",
                                                                        cx: "12",
                                                                        cy: "12",
                                                                        r: "10",
                                                                        stroke: "currentColor",
                                                                        strokeWidth: "4"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                                        lineNumber: 152,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                        className: "opacity-75",
                                                                        fill: "currentColor",
                                                                        d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                                        lineNumber: 153,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                                lineNumber: 151,
                                                                columnNumber: 29
                                                            }, this),
                                                            "Saving..."
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                className: "ri-save-line mr-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                                lineNumber: 159,
                                                                columnNumber: 29
                                                            }, this),
                                                            saveButtonText
                                                        ]
                                                    }, void 0, true)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                    lineNumber: 143,
                                                    columnNumber: 23
                                                }, this),
                                                showSubmitButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: onSubmit,
                                                    disabled: submitButtonDisabled || isSubmitting,
                                                    className: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                                                    children: isSubmitting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                className: "animate-spin -ml-1 mr-2 h-4 w-4 text-white",
                                                                xmlns: "http://www.w3.org/2000/svg",
                                                                fill: "none",
                                                                viewBox: "0 0 24 24",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                                        className: "opacity-25",
                                                                        cx: "12",
                                                                        cy: "12",
                                                                        r: "10",
                                                                        stroke: "currentColor",
                                                                        strokeWidth: "4"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                                        lineNumber: 176,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                        className: "opacity-75",
                                                                        fill: "currentColor",
                                                                        d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                                        lineNumber: 177,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                                lineNumber: 175,
                                                                columnNumber: 29
                                                            }, this),
                                                            "Submitting..."
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                className: "ri-send-plane-line mr-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                                lineNumber: 183,
                                                                columnNumber: 29
                                                            }, this),
                                                            submitButtonText
                                                        ]
                                                    }, void 0, true)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                    lineNumber: 167,
                                                    columnNumber: 23
                                                }, this),
                                                showNextButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: onNext,
                                                    disabled: nextButtonDisabled,
                                                    className: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                                                    children: [
                                                        nextButtonText,
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: "ri-arrow-right-line ml-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                            lineNumber: 198,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                            lineNumber: 141,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                    lineNumber: 126,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                                lineNumber: 125,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                        lineNumber: 96,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
                    lineNumber: 95,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
            lineNumber: 84,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/evaluation/EvaluationLayout.tsx",
        lineNumber: 83,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = EvaluationLayout;
}}),
"[project]/src/lib/customer-api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CustomerApiService": (()=>CustomerApiService),
    "customerApi": (()=>customerApi),
    "customerApiClient": (()=>customerApiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-ssr] (ecmascript)");
;
;
;
// API Configuration
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:3001") || 'http://localhost:3001';
// Create axios instance for customer portal (same as staff portal)
const customerApiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    timeout: 120000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});
// Create auth-specific client (same as staff portal)
const customerAuthApiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: `${API_BASE_URL}/auth`,
    timeout: 120000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});
// Add debug logging to auth client (only in development)
customerAuthApiClient.interceptors.request.use((config)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Customer Auth API Request:', {
            url: `${config.baseURL}${config.url}`,
            method: config.method,
            headers: config.headers,
            data: config.data
        });
    }
    return config;
}, (error)=>{
    console.error('Customer Auth API Request Error:', error);
    return Promise.reject(error);
});
customerAuthApiClient.interceptors.response.use((response)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Customer Auth API Response Success:', {
            status: response.status,
            statusText: response.statusText,
            url: response.config.url
        });
    }
    return response;
}, (error)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        console.error('Customer Auth API Interceptor Error:', {
            message: error?.message || 'Unknown error',
            code: error?.code || 'NO_CODE',
            status: error?.response?.status || 'NO_STATUS',
            statusText: error?.response?.statusText || 'NO_STATUS_TEXT',
            url: error?.config?.url || 'NO_URL',
            method: error?.config?.method || 'NO_METHOD',
            baseURL: error?.config?.baseURL || 'NO_BASE_URL',
            isAxiosError: error?.isAxiosError || false,
            responseData: error?.response?.data || 'NO_RESPONSE_DATA',
            requestData: error?.config?.data || 'NO_REQUEST_DATA',
            headers: error?.config?.headers || 'NO_HEADERS'
        });
    }
    // Don't handle 401 here, let the login method handle it
    return Promise.reject(error);
});
// Request interceptor to add auth token
customerApiClient.interceptors.request.use((config)=>{
    const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('auth_token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor for error handling with retry logic
customerApiClient.interceptors.response.use((response)=>{
    return response;
}, async (error)=>{
    const originalRequest = error.config;
    // Handle 429 Rate Limiting
    if (error.response?.status === 429) {
        if (!originalRequest._retry) {
            originalRequest._retry = true;
            // Get retry delay from headers or use exponential backoff
            const retryAfter = error.response.headers['retry-after'];
            const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);
            originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;
            // Don't retry more than 3 times
            if (originalRequest._retryCount <= 3) {
                console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);
                await new Promise((resolve)=>setTimeout(resolve, delay));
                return customerApiClient(originalRequest);
            }
        }
    }
    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
        // Clear auth token and redirect to login
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove('auth_token');
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove('auth_user');
        window.location.href = '/auth/login';
    }
    return Promise.reject(error);
});
class CustomerApiService {
    api;
    pendingRequests = new Map();
    constructor(){
        this.api = customerApiClient;
    }
    // Request deduplication helper
    async deduplicateRequest(key, requestFn) {
        if (this.pendingRequests.has(key)) {
            return this.pendingRequests.get(key);
        }
        const promise = requestFn().finally(()=>{
            this.pendingRequests.delete(key);
        });
        this.pendingRequests.set(key, promise);
        return promise;
    }
    // Set auth token
    setAuthToken(token) {
        this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }
    // Remove auth token
    removeAuthToken() {
        delete this.api.defaults.headers.common['Authorization'];
    }
    async logout() {
        const response = await customerAuthApiClient.post('/logout');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async refreshToken() {
        const response = await customerAuthApiClient.post('/refresh');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // 2FA endpoints
    async generateTwoFactorCode(userId, action) {
        const response = await customerAuthApiClient.post('/generate-2fa', {
            user_id: userId,
            action
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async verify2FA(data) {
        const response = await customerAuthApiClient.post('/verify-2fa', data);
        // Handle response structure consistently with login
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response)?.data) {
            const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response).data;
            // Map backend field names to frontend expected format
            const mappedAuthData = {
                access_token: authData.access_token,
                user: {
                    id: authData.user.user_id,
                    firstName: authData.user.first_name,
                    lastName: authData.user.last_name,
                    email: authData.user.email,
                    roles: authData.user.roles || [],
                    isAdmin: (authData.user.roles || []).includes('administrator'),
                    profileImage: authData.user.profile_image,
                    createdAt: authData.user.created_at || new Date().toISOString(),
                    lastLogin: authData.user.last_login,
                    organizationName: authData.user.organization_name,
                    two_factor_enabled: authData.user.two_factor_enabled
                }
            };
            return mappedAuthData;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async setupTwoFactorAuth(data) {
        const response = await customerAuthApiClient.post('/setup-2fa', data);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // User profile endpoints
    async getProfile() {
        return this.deduplicateRequest('getProfile', async ()=>{
            const response = await this.api.get('/users/profile');
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        });
    }
    async updateProfile(profileData) {
        const response = await this.api.put('/users/profile', profileData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async deactivateAccount(deactivationData) {
        const response = await this.api.post('/users/deactivate', deactivationData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Addressing endpoints
    async getAddresses() {
        const response = await this.api.get('/address/all');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async createAddress(addressData) {
        const response = await this.api.post('/address/create', addressData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getAddress(id) {
        const response = await this.api.get(`/address/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async editAddress(addressData) {
        const { address_id, ...updateData } = addressData;
        if (!address_id) {
            throw new Error('Address ID is required for updating');
        }
        const response = await this.api.put(`/address/${address_id}`, updateData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getAddressesByEntity(entityType, entityId) {
        const response = await this.api.get(`/address/all?entity_type=${encodeURIComponent(entityType)}&entity_id=${encodeURIComponent(entityId)}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async deleteAddress(id) {
        const response = await this.api.delete(`/address/soft/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async searchPostcodes(searchParams) {
        const response = await this.api.post('/postal-codes/search', searchParams);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // License endpoints
    async getLicenses(params) {
        const response = await this.api.get('/licenses', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicense(id) {
        const response = await this.api.get(`/licenses/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async createLicenseApplication(applicationData) {
        const response = await this.api.post('/license-applications', applicationData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // License Types endpoints
    async getLicenseTypes(params) {
        const response = await this.api.get('/license-types', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicenseType(id) {
        const response = await this.api.get(`/license-types/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // License Categories endpoints
    async getLicenseCategories(params) {
        const response = await this.api.get('/license-categories', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicenseCategoriesByType(licenseTypeId) {
        const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicenseCategoryTree(licenseTypeId) {
        const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getLicenseCategory(id) {
        const response = await this.api.get(`/license-categories/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Application endpoints
    async getApplications(params) {
        const response = await this.api.get('/applications', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getApplication(id) {
        const response = await this.api.get(`/applications/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async createApplication(applicationData) {
        const response = await this.api.post('/applications', applicationData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async updateApplication(id, applicationData) {
        const response = await this.api.put(`/applications/${id}`, applicationData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Payment endpoints
    async getPayments(params) {
        const response = await this.api.get('/payments', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getPayment(id) {
        const response = await this.api.get(`/payments/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async createPayment(paymentData) {
        const response = await this.api.post('/payments', paymentData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async uploadProofOfPayment(paymentId, formData) {
        const response = await this.api.post(`/payments/${paymentId}/proof-of-payment`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getPaymentStatistics() {
        const response = await this.api.get('/payments/statistics');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Document endpoints
    async getDocuments(params) {
        const response = await this.api.get('/documents', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async uploadDocument(formData) {
        const response = await this.api.post('/documents/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async downloadDocument(id) {
        const response = await this.api.get(`/documents/${id}/download`, {
            responseType: 'blob'
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Dashboard statistics
    async getDashboardStats() {
        const response = await this.api.get('/dashboard/stats');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Notifications
    async getNotifications(params) {
        const response = await this.api.get('/notifications', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async markNotificationAsRead(id) {
        const response = await this.api.patch(`/notifications/${id}/read`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    // Procurement endpoints
    async getTenders(params) {
        const response = await this.api.get('/procurement/tenders', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
    async getTender(id) {
        const response = await this.api.get(`/procurement/tenders/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async payForTenderAccess(tenderId, paymentData) {
        const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async downloadTenderDocument(documentId) {
        const response = await this.api.get(`/procurement/documents/${documentId}/download`, {
            responseType: 'blob'
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async getMyBids(params) {
        const response = await this.api.get('/procurement/my-bids', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async getBid(id) {
        const response = await this.api.get(`/procurement/bids/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async submitBid(formData) {
        const response = await this.api.post('/procurement/bids', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async updateBid(id, formData) {
        const response = await this.api.put(`/procurement/bids/${id}`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async getProcurementPayments(params) {
        const response = await this.api.get('/procurement/payments', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async getProcurementPayment(id) {
        const response = await this.api.get(`/procurement/payments/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    // Consumer Affairs endpoints
    async getComplaints(params) {
        const response = await this.api.get('/consumer-affairs/complaints', {
            params
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async getComplaint(id) {
        const response = await this.api.get(`/consumer-affairs/complaints/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async submitComplaint(complaintData) {
        const formData = new FormData();
        formData.append('title', complaintData.title);
        formData.append('description', complaintData.description);
        formData.append('category', complaintData.category);
        if (complaintData.attachments) {
            complaintData.attachments.forEach((file, index)=>{
                formData.append(`attachments[${index}]`, file);
            });
        }
        const response = await this.api.post('/consumer-affairs/complaints', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async updateComplaint(id, updates) {
        const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
    async downloadComplaintAttachment(complaintId, attachmentId) {
        const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {
            responseType: 'blob'
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
    }
}
const customerApi = new CustomerApiService();
;
}}),
"[project]/src/hooks/useDynamicNavigation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDynamicNavigation": (()=>useDynamicNavigation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/licenseTypeStepConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$customer$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/customer-api.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const useDynamicNavigation = ({ currentStepRoute, licenseCategoryId, applicationId })=>{
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [licenseTypeCode, setLicenseTypeCode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [steps, setSteps] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Create customer API service instance
    const customerApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$customer$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CustomerApiService"](), []);
    // Load license type and steps
    const loadLicenseTypeSteps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!licenseCategoryId) {
            setError('License category ID is required');
            setLoading(false);
            return;
        }
        try {
            setLoading(true);
            setError(null);
            console.log('🧭 Loading license type for navigation:', licenseCategoryId);
            // Get license category and type
            const category = await customerApi.getLicenseCategory(licenseCategoryId);
            if (!category?.license_type_id) {
                throw new Error('License category does not have a license type ID');
            }
            // Add small delay to prevent rate limiting
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const licenseType = await customerApi.getLicenseType(category.license_type_id);
            if (!licenseType) {
                throw new Error('License type not found');
            }
            const typeCode = licenseType.code || licenseType.license_type_id;
            setLicenseTypeCode(typeCode);
            // Get steps based on license type code
            let licenseSteps = [];
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isLicenseTypeCodeSupported"])(typeCode)) {
                console.log('✅ Using optimized steps for supported license type:', typeCode);
                licenseSteps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getStepsByLicenseTypeCode"])(typeCode);
            } else {
                console.log('⚠️ Using fallback steps for license type:', typeCode);
                const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLicenseTypeStepConfig"])(typeCode);
                licenseSteps = config.steps;
            }
            setSteps(licenseSteps);
            console.log('🧭 Loaded steps for navigation:', licenseSteps.map((s)=>s.route));
        } catch (err) {
            console.error('Error loading license type steps:', err);
            setError(err.message || 'Failed to load navigation configuration');
            // Use default fallback steps
            const fallbackConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLicenseTypeStepConfig"])('default');
            setSteps(fallbackConfig.steps);
            setLicenseTypeCode('default');
        } finally{
            setLoading(false);
        }
    }, [
        licenseCategoryId,
        customerApi
    ]);
    // Load steps when dependencies change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadLicenseTypeSteps();
    }, [
        loadLicenseTypeSteps
    ]);
    // Computed values
    const currentStepIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return steps.findIndex((step)=>step.route === currentStepRoute);
    }, [
        steps,
        currentStepRoute
    ]);
    const currentStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return steps[currentStepIndex] || null;
    }, [
        steps,
        currentStepIndex
    ]);
    const nextStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return currentStepIndex >= 0 && currentStepIndex < steps.length - 1 ? steps[currentStepIndex + 1] : null;
    }, [
        steps,
        currentStepIndex
    ]);
    const previousStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return currentStepIndex > 0 ? steps[currentStepIndex - 1] : null;
    }, [
        steps,
        currentStepIndex
    ]);
    const totalSteps = steps.length;
    const isFirstStep = currentStepIndex === 0;
    const isLastStep = currentStepIndex === steps.length - 1;
    const canNavigateNext = !isLastStep && nextStep !== null;
    const canNavigatePrevious = !isFirstStep && previousStep !== null;
    // Navigation functions
    const createNavigationUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((stepRoute)=>{
        const params = new URLSearchParams();
        params.set('license_category_id', licenseCategoryId || '');
        if (applicationId) {
            params.set('application_id', applicationId);
        }
        return `/customer/applications/apply/${stepRoute}?${params.toString()}`;
    }, [
        licenseCategoryId,
        applicationId
    ]);
    const navigateToStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((stepRoute)=>{
        const url = createNavigationUrl(stepRoute);
        console.log('🧭 Navigating to step:', stepRoute, 'URL:', url);
        router.push(url);
    }, [
        createNavigationUrl,
        router
    ]);
    const handleNext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (saveFunction)=>{
        if (!canNavigateNext || !nextStep) {
            console.warn('⚠️ Cannot navigate to next step');
            return;
        }
        // If save function is provided, save first
        if (saveFunction) {
            console.log('💾 Saving current step before navigation...');
            try {
                const saved = await saveFunction();
                if (!saved) {
                    console.warn('⚠️ Save failed, not navigating');
                    return;
                }
            } catch (error) {
                console.error('❌ Error during save operation:', error);
                // Handle specific error types
                if (error.message?.includes('timeout')) {
                    console.error('Save operation timed out');
                } else if (error.message?.includes('Bad Request')) {
                    console.error('Invalid data provided for save operation');
                } else if (error.message?.includes('Too many requests')) {
                    console.error('Rate limit exceeded, please wait and try again');
                }
                // Don't navigate if save failed
                return;
            }
        }
        navigateToStep(nextStep.route);
    }, [
        canNavigateNext,
        nextStep,
        navigateToStep
    ]);
    const handlePrevious = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!canNavigatePrevious || !previousStep) {
            console.warn('⚠️ Cannot navigate to previous step');
            return;
        }
        navigateToStep(previousStep.route);
    }, [
        canNavigatePrevious,
        previousStep,
        navigateToStep
    ]);
    return {
        // Navigation functions
        handleNext,
        handlePrevious,
        navigateToStep,
        // Step information
        currentStep,
        nextStep,
        previousStep,
        currentStepIndex,
        totalSteps,
        // State
        loading,
        error,
        licenseTypeCode,
        // Utility functions
        isFirstStep,
        isLastStep,
        canNavigateNext,
        canNavigatePrevious
    };
};
}}),
"[project]/src/services/applicationService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicationService": (()=>applicationService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-ssr] (ecmascript)");
;
;
const applicationService = {
    // Get all applications with pagination and filters
    async getApplications (params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        // Add filters
        if (params?.filters?.licenseTypeId) {
            queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);
        }
        if (params?.filters?.licenseCategoryId) {
            queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);
        }
        if (params?.filters?.status) {
            queryParams.append('filter.status', params.filters.status);
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications?${queryParams.toString()}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by license type (through license category)
    async getApplicationsByLicenseType (licenseTypeId, params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.status) queryParams.append('filter.status', params.status);
        // Filter by license type through license category
        queryParams.append('filter.license_category.license_type_id', licenseTypeId);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications?${queryParams.toString()}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get single application by ID
    async getApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by applicant
    async getApplicationsByApplicant (applicantId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/by-applicant/${applicantId}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get applications by status
    async getApplicationsByStatus (status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/by-status/${status}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update application status
    async updateApplicationStatus (id, status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}/status?status=${status}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update application progress
    async updateApplicationProgress (id, currentStep, progressPercentage) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get application statistics
    async getApplicationStats () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/stats');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new application
    async createApplication (data) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/applications', data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Update application with improved error handling
    async updateApplication (id, data) {
        try {
            console.log('Updating application:', id, 'with data:', data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}`, data, {
                timeout: 30000
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating application:', error);
            // Handle specific error cases
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timeout - please try again');
            }
            if (error.response?.status === 400) {
                const message = error.response?.data?.message || 'Invalid application data';
                console.error('400 Bad Request details:', error.response?.data);
                throw new Error(`Bad Request: ${message}`);
            }
            if (error.response?.status === 429) {
                throw new Error('Too many requests - please wait a moment and try again');
            }
            throw error;
        }
    },
    // Delete application
    async deleteApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(`/applications/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new application with applicant data
    async createApplicationWithApplicant (data) {
        try {
            // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)
            const now = new Date();
            const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
            const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
            const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();
            const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;
            // Validate user_id is a proper UUID
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(data.user_id)) {
                throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);
            }
            // Create application using user_id as applicant_id
            // In most systems, the authenticated user is the applicant
            const application = await this.createApplication({
                application_number: applicationNumber,
                applicant_id: data.user_id,
                license_category_id: data.license_category_id,
                current_step: 1,
                progress_percentage: 0 // Start with 0% progress
            });
            return application;
        } catch (error) {
            console.error('Error creating application with applicant:', error);
            throw error;
        }
    },
    // Save application section data
    async saveApplicationSection (applicationId, sectionName, sectionData) {
        try {
            // Try to save using form data service, but continue if it fails
            let completedSections = 1; // At least one section is being saved
            // Estimate progress based on section name
            const sectionOrder = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'serviceScope',
                'businessPlan',
                'legalHistory',
                'reviewSubmit'
            ];
            const sectionIndex = sectionOrder.indexOf(sectionName);
            completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;
            // Calculate progress based on completed sections (excluding reviewSubmit from total)
            const totalSections = 6; // Total number of form sections (excluding reviewSubmit)
            const progressPercentage = Math.min(Math.round(completedSections / totalSections * 100), 100);
            // Update the application progress
            await this.updateApplication(applicationId, {
                progress_percentage: progressPercentage,
                current_step: completedSections
            });
        } catch (error) {
            throw error;
        }
    },
    // Get application section data
    async getApplicationSection (applicationId, sectionName) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${applicationId}/sections/${sectionName}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            throw error;
        }
    },
    // Submit application for review
    async submitApplication (applicationId) {
        try {
            // Update application status to submitted and set submission date
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}`, {
                status: 'submitted',
                submitted_at: new Date().toISOString(),
                progress_percentage: 100,
                current_step: 7
            });
            console.log('Application submitted successfully:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error submitting application:', error);
            throw error;
        }
    },
    // Get user's applications (filtered by authenticated user)
    async getUserApplications () {
        try {
            // Use dedicated endpoint that explicitly filters by current user
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/user-applications');
            const processedResponse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            // Handle paginated response structure
            let applications = [];
            if (processedResponse?.data) {
                applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];
            } else if (Array.isArray(processedResponse)) {
                applications = processedResponse;
            } else if (processedResponse) {
                // Single application or other structure
                applications = [
                    processedResponse
                ];
            }
            return applications;
        } catch (error) {
            throw error;
        }
    },
    // Save application as draft
    async saveAsDraft (applicationId, formData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}`, {
                form_data: formData,
                status: 'draft'
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error saving application as draft:', error);
            throw error;
        }
    },
    // Validate application before submission
    async validateApplication (applicationId) {
        try {
            // Get data from entity-specific APIs for validation
            let formData = {};
            const errors = [];
            const requiredSections = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'legalHistory'
            ];
            // Check if all required sections are completed
            for (const section of requiredSections){
                if (!formData[section] || Object.keys(formData[section]).length === 0) {
                    errors.push(`${section} section is incomplete`);
                }
            }
            return {
                isValid: errors.length === 0,
                errors
            };
        } catch (error) {
            console.error('Error validating application:', error);
            throw error;
        }
    },
    // Update application status
    async updateStatus (applicationId, status) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(`/applications/${applicationId}/status`, {
                status
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating application status:', error);
            throw error;
        }
    },
    // Assign application to an officer
    async assignApplication (applicationId, assignedTo) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(`/applications/${applicationId}/assign`, {
                assignedTo
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error assigning application:', error);
            throw error;
        }
    }
};
}}),
"[project]/src/services/applicantService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicantService": (()=>applicantService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-ssr] (ecmascript)");
;
;
const applicantService = {
    // Create new applicant
    async createApplicant (data) {
        try {
            console.log('Creating applicant with data:', data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/applicants', data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            "TURBOPACK unreachable";
        } catch (error) {
            console.error('Error creating applicant:', error);
            console.error('Error details:', error?.response?.data);
            throw error;
        }
    },
    // Get applicant by ID
    async getApplicant (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/applicants/${id}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error fetching applicant:', error);
            throw error;
        }
    },
    // Update applicant
    async updateApplicant (id, data) {
        try {
            console.log('Updating applicant:', id, data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].put(`/applicants/${id}`, data);
            console.log('Applicant updated successfully:', response.data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating applicant:', error);
            throw error;
        }
    },
    // Get applicants by user (if user can have multiple applicants)
    async getApplicantsByUser () {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/applicants/by-user');
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error fetching user applicants:', error);
            throw error;
        }
    },
    // Delete applicant
    async deleteApplicant (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(`/applicants/${id}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error deleting applicant:', error);
            throw error;
        }
    }
};
}}),
"[project]/src/components/evaluation/EvaluationForm.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const EvaluationForm = ({ applicationId, currentStep, onStatusUpdate, onCommentSave, onAttachmentUpload, isSubmitting = false, className = '' })=>{
    const [comment, setComment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedStatus, setSelectedStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [attachments, setAttachments] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const statusOptions = [
        {
            value: 'under_review',
            label: 'Under Review',
            color: 'yellow'
        },
        {
            value: 'evaluation',
            label: 'In Evaluation',
            color: 'purple'
        },
        {
            value: 'approved',
            label: 'Approved',
            color: 'green'
        },
        {
            value: 'rejected',
            label: 'Rejected',
            color: 'red'
        }
    ];
    const handleEmailApplicant = ()=>{
        if (selectedStatus && comment.trim() && onStatusUpdate) {
            onStatusUpdate(selectedStatus, comment.trim());
        }
    };
    const handleCommentSave = ()=>{
        if (comment.trim() && onCommentSave) {
            onCommentSave(comment.trim());
        }
    };
    const handleFileUpload = (event)=>{
        const files = event.target.files;
        if (files && files.length > 0) {
            const newFiles = Array.from(files);
            setAttachments((prev)=>[
                    ...prev,
                    ...newFiles
                ]);
            // Upload each file
            newFiles.forEach((file)=>{
                if (onAttachmentUpload) {
                    onAttachmentUpload(file);
                }
            });
        }
    };
    const removeAttachment = (index)=>{
        setAttachments((prev)=>prev.filter((_, i)=>i !== index));
    };
    const getStatusColor = (status)=>{
        const option = statusOptions.find((opt)=>opt.value === status);
        return option?.color || 'gray';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 dark:border-gray-700 pb-4 mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-clipboard-line mr-2 text-blue-600"
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 76,
                                columnNumber: 11
                            }, this),
                            "Evaluation for ",
                            currentStep.replace(/-/g, ' ').replace(/\b\w/g, (l)=>l.toUpperCase())
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 75,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-500 dark:text-gray-400 mt-1",
                        children: "Review the information and provide your evaluation comments"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                lineNumber: 74,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        htmlFor: "evaluation-comment",
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Evaluation Comments *"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 86,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        id: "evaluation-comment",
                        value: comment,
                        onChange: (e)=>setComment(e.target.value),
                        rows: 4,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "Enter your evaluation comments for this step...",
                        required: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xs text-gray-500 dark:text-gray-400 mt-1",
                        children: "Provide detailed feedback about this section of the application"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 98,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                lineNumber: 85,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        htmlFor: "status-select",
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Update Application Status"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                        id: "status-select",
                        value: selectedStatus,
                        onChange: (e)=>setSelectedStatus(e.target.value),
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: "",
                                children: "Select status..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 114,
                                columnNumber: 11
                            }, this),
                            statusOptions.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: option.value,
                                    children: option.label
                                }, option.value, false, {
                                    fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                    lineNumber: 116,
                                    columnNumber: 13
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 108,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                lineNumber: 104,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Attach Supporting Documents"
                    }, void 0, false, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 125,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "file",
                                multiple: true,
                                onChange: handleFileUpload,
                                className: "hidden",
                                id: "file-upload",
                                accept: ".pdf,.doc,.docx,.jpg,.jpeg,.png"
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 129,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "file-upload",
                                className: "cursor-pointer flex flex-col items-center justify-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-upload-cloud-line text-3xl text-gray-400 mb-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                        lineNumber: 141,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                        children: "Click to upload files or drag and drop"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                        lineNumber: 142,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs text-gray-500 dark:text-gray-500 mt-1",
                                        children: "PDF, DOC, DOCX, JPG, PNG up to 10MB each"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                        lineNumber: 145,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 128,
                        columnNumber: 9
                    }, this),
                    attachments.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: [
                                    "Uploaded Files (",
                                    attachments.length,
                                    ")"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-2",
                                children: attachments.map((file, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-file-line text-gray-400 mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                                        lineNumber: 161,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm text-gray-700 dark:text-gray-300",
                                                        children: file.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                                        lineNumber: 162,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-500 dark:text-gray-400 ml-2",
                                                        children: [
                                                            "(",
                                                            (file.size / 1024 / 1024).toFixed(2),
                                                            " MB)"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                                        lineNumber: 163,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                                lineNumber: 160,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: ()=>removeAttachment(index),
                                                className: "text-red-500 hover:text-red-700",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    className: "ri-close-line"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                                    lineNumber: 172,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                                lineNumber: 167,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, index, true, {
                                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                        lineNumber: 159,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 157,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 153,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                lineNumber: 124,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-wrap gap-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: handleCommentSave,
                        disabled: !comment.trim() || isSubmitting,
                        className: "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-save-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 189,
                                columnNumber: 11
                            }, this),
                            "Save Comment"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 183,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: handleEmailApplicant,
                        disabled: !comment.trim() || isSubmitting,
                        className: `px-4 py-2 rounded-md text-white flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${selectedStatus === 'approved' ? 'bg-green-600 hover:bg-green-700' : selectedStatus === 'rejected' ? 'bg-red-600 hover:bg-red-700' : 'bg-purple-600 hover:bg-purple-700'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-check-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 203,
                                columnNumber: 11
                            }, this),
                            "Save and Email Applicant"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 193,
                        columnNumber: 9
                    }, this),
                    isSubmitting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center text-gray-500 dark:text-gray-400",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                                lineNumber: 209,
                                columnNumber: 13
                            }, this),
                            "Processing..."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                        lineNumber: 208,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
                lineNumber: 182,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/evaluation/EvaluationForm.tsx",
        lineNumber: 73,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = EvaluationForm;
}}),
"[project]/src/components/evaluation/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/evaluation/EvaluationLayout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/evaluation/EvaluationForm.tsx [app-ssr] (ecmascript)");
;
;
}}),
"[project]/src/components/evaluation/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/evaluation/EvaluationLayout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/evaluation/EvaluationForm.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/evaluation/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/components/evaluation/EvaluationForm.tsx [app-ssr] (ecmascript) <export default as EvaluationForm>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EvaluationForm": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/evaluation/EvaluationForm.tsx [app-ssr] (ecmascript)");
}}),
"[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/evaluation/EvaluationLayout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useDynamicNavigation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useDynamicNavigation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicantService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicantService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/evaluation/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__EvaluationForm$3e$__ = __turbopack_context__.i("[project]/src/components/evaluation/EvaluationForm.tsx [app-ssr] (ecmascript) <export default as EvaluationForm>");
'use client';
;
;
;
;
;
;
;
;
;
const EvaluateApplicantInfoPage = ({ params })=>{
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const { isAuthenticated, loading: authLoading, user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    // Unwrap params using React.use()
    const resolvedParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["use"])(params);
    const licenseType = resolvedParams['license-type'];
    const applicationId = searchParams.get('application_id');
    // State
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [application, setApplication] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [applicantData, setApplicantData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [licenseCategoryId, setLicenseCategoryId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Dynamic navigation hook - same as apply pages
    const { handleNext: dynamicHandleNext, handlePrevious: dynamicHandlePrevious, nextStep, previousStep, currentStep, totalSteps, licenseTypeCode: navLicenseTypeCode } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useDynamicNavigation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDynamicNavigation"])({
        currentStepRoute: 'applicant-info',
        licenseCategoryId,
        applicationId
    });
    // Debug navigation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        console.log('🔍 Navigation Debug:', {
            licenseCategoryId,
            applicationId,
            licenseType,
            nextStep,
            previousStep,
            currentStep,
            totalSteps,
            navLicenseTypeCode
        });
    }, [
        licenseCategoryId,
        applicationId,
        licenseType,
        nextStep,
        previousStep,
        currentStep,
        totalSteps,
        navLicenseTypeCode
    ]);
    // Load application and applicant data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const loadData = async ()=>{
            if (!applicationId || !isAuthenticated) return;
            try {
                setLoading(true);
                setError(null);
                // Load application details
                const appResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["applicationService"].getApplication(applicationId);
                setApplication(appResponse);
                // Set license category ID for navigation
                if (appResponse?.license_category_id) {
                    setLicenseCategoryId(appResponse.license_category_id);
                }
                // Load applicant data if available
                if (appResponse.applicant_id) {
                    try {
                        const applicantResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicantService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["applicantService"].getApplicant(appResponse.applicant_id);
                        setApplicantData(applicantResponse);
                    } catch (err) {
                        console.error('Error loading applicant data:', err);
                    // Continue without applicant data
                    }
                }
            } catch (err) {
                console.error('Error loading application data:', err);
                setError('Failed to load application data');
            } finally{
                setLoading(false);
            }
        };
        loadData();
    }, [
        applicationId,
        isAuthenticated
    ]);
    // Navigation handlers - modified for evaluation
    const handleNext = ()=>{
        if (!applicationId || !nextStep) return;
        const params = new URLSearchParams();
        params.set('application_id', applicationId);
        if (licenseCategoryId) {
            params.set('license_category_id', licenseCategoryId);
        }
        router.push(`/applications/${licenseType}/evaluate/${nextStep.route}?${params.toString()}`);
    };
    const handlePrevious = ()=>{
        if (!applicationId || !previousStep) return;
        const params = new URLSearchParams();
        params.set('application_id', applicationId);
        if (licenseCategoryId) {
            params.set('license_category_id', licenseCategoryId);
        }
        router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
    };
    // Evaluation handlers
    const handleStatusUpdate = async (status, comment)=>{
        if (!applicationId) return;
        try {
            setIsSubmitting(true);
            // For now, just log the action - implement actual API call later
            console.log('Status update:', {
                applicationId,
                status,
                comment
            });
            // Show success message (you might want to add a toast notification here)
            console.log('Status updated successfully');
        } catch (err) {
            console.error('Error updating status:', err);
            setError('Failed to update application status');
        } finally{
            setIsSubmitting(false);
        }
    };
    const handleCommentSave = async (comment)=>{
        if (!applicationId) return;
        try {
            setIsSubmitting(true);
            // For now, just log the action - implement actual API call later
            console.log('Comment save:', {
                applicationId,
                step: 'applicant-info',
                comment
            });
            console.log('Comment saved successfully');
        } catch (err) {
            console.error('Error saving comment:', err);
            setError('Failed to save comment');
        } finally{
            setIsSubmitting(false);
        }
    };
    const handleAttachmentUpload = async (file)=>{
        if (!applicationId) return;
        try {
            setIsSubmitting(true);
            // For now, just log the action - implement actual API call later
            console.log('Attachment upload:', {
                applicationId,
                step: 'applicant-info',
                fileName: file.name
            });
            console.log('Attachment uploaded successfully');
        } catch (err) {
            console.error('Error uploading attachment:', err);
            setError('Failed to upload attachment');
        } finally{
            setIsSubmitting(false);
        }
    };
    // Loading state
    if (authLoading || loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 188,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 dark:text-gray-400",
                        children: "Loading application data..."
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 189,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                lineNumber: 187,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
            lineNumber: 186,
            columnNumber: 7
        }, this);
    }
    // Error state
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-error-warning-line text-4xl text-red-500 mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 200,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",
                        children: "Error Loading Application"
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 201,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 dark:text-gray-400 mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 202,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/applications'),
                        className: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",
                        children: "Back to Applications"
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 203,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                lineNumber: 199,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
            lineNumber: 198,
            columnNumber: 7
        }, this);
    }
    // No application found
    if (!application) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-file-search-line text-4xl text-gray-400 mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 219,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",
                        children: "Application Not Found"
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 220,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 dark:text-gray-400 mb-4",
                        children: "The requested application could not be found."
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 221,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/applications'),
                        className: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",
                        children: "Back to Applications"
                    }, void 0, false, {
                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                        lineNumber: 222,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                lineNumber: 218,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
            lineNumber: 217,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        applicationId: applicationId,
        licenseTypeCode: licenseType,
        currentStepRoute: "applicant-info",
        onNext: handleNext,
        onPrevious: handlePrevious,
        showNextButton: !!nextStep,
        showPreviousButton: !!previousStep,
        nextButtonDisabled: isSubmitting,
        previousButtonDisabled: isSubmitting,
        nextButtonText: nextStep ? `Continue to ${nextStep.name}` : "Continue",
        previousButtonText: previousStep ? `Back to ${previousStep.name}` : "Back",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "space-y-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                    children: "Organization Name"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 251,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-900 dark:text-gray-100",
                                        children: applicantData?.name || 'Not provided'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                        lineNumber: 255,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 254,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                            lineNumber: 250,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                    children: "Business Registration Number"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 262,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-900 dark:text-gray-100",
                                        children: applicantData?.business_registration_number || 'Not provided'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                        lineNumber: 266,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 265,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                            lineNumber: 261,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                    children: "TPIN"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 273,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-900 dark:text-gray-100",
                                        children: applicantData?.tpin || 'Not provided'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                        lineNumber: 277,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 276,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                            lineNumber: 272,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                    children: "Website"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 284,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-900 dark:text-gray-100",
                                        children: applicantData?.website || 'Not provided'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                        lineNumber: 288,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 287,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                            lineNumber: 283,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                    children: "Email"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 295,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-900 dark:text-gray-100",
                                        children: applicantData?.email || 'Not provided'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                        lineNumber: 299,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 298,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                            lineNumber: 294,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                    children: "Phone"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 306,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-900 dark:text-gray-100",
                                        children: applicantData?.phone || 'Not provided'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                        lineNumber: 310,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 309,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                            lineNumber: 305,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                    lineNumber: 249,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                    children: "Date of Incorporation"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 320,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-900 dark:text-gray-100",
                                        children: applicantData?.date_incorporation ? new Date(applicantData.date_incorporation).toLocaleDateString() : 'Not provided'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                        lineNumber: 324,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 323,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                            lineNumber: 319,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                                    children: "Place of Incorporation"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 334,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-900 dark:text-gray-100",
                                        children: applicantData?.place_incorporation || 'Not provided'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                        lineNumber: 338,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                                    lineNumber: 337,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                            lineNumber: 333,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                    lineNumber: 318,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$evaluation$2f$EvaluationForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__EvaluationForm$3e$__["EvaluationForm"], {
                    applicationId: applicationId,
                    currentStep: "applicant-info",
                    onStatusUpdate: handleStatusUpdate,
                    onCommentSave: handleCommentSave,
                    onAttachmentUpload: handleAttachmentUpload,
                    isSubmitting: isSubmitting
                }, void 0, false, {
                    fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
                    lineNumber: 346,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
            lineNumber: 248,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/applications/[license-type]/evaluate/applicant-info/page.tsx",
        lineNumber: 234,
        columnNumber: 7
    }, this);
};
const __TURBOPACK__default__export__ = EvaluateApplicantInfoPage;
}}),

};

//# sourceMappingURL=src_0c4f36a8._.js.map