{"version": 3, "file": "create-license.dto.js", "sourceRoot": "", "sources": ["../../../src/dto/licenses/create-license.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA8F;AAC9F,oEAA+D;AAE/D,MAAa,gBAAgB;IAG3B,cAAc,CAAS;IAGvB,cAAc,CAAS;IAGvB,YAAY,CAAS;IAGrB,eAAe,CAAS;IAIxB,MAAM,CAAiB;IAGvB,UAAU,CAAS;IAGnB,WAAW,CAAS;IAGpB,SAAS,CAAS;IAIlB,UAAU,CAAU;CACrB;AA9BD,4CA8BC;AA3BC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,yBAAO,EAAC,kCAAkC,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;;wDACnE;AAGvB;IADC,IAAA,wBAAM,GAAE;;wDACc;AAGvB;IADC,IAAA,wBAAM,GAAE;;sDACY;AAGrB;IADC,IAAA,wBAAM,GAAE;;yDACe;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,+BAAa,CAAC;;gDACC;AAGvB;IADC,IAAA,8BAAY,GAAE;;oDACI;AAGnB;IADC,IAAA,8BAAY,GAAE;;qDACK;AAGpB;IADC,IAAA,wBAAM,GAAE;;mDACS;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACS"}