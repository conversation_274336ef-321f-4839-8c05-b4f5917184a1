"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Paginate = void 0;
const common_1 = require("@nestjs/common");
exports.Paginate = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const query = request.query;
    const page = parseInt(query.page) || 1;
    const limit = Math.min(parseInt(query.limit) || 10, 100);
    let sortBy = [];
    if (query.sortBy) {
        if (Array.isArray(query.sortBy)) {
            sortBy = query.sortBy;
        }
        else {
            sortBy = [query.sortBy];
        }
    }
    let searchBy = [];
    if (query.searchBy) {
        if (Array.isArray(query.searchBy)) {
            searchBy = query.searchBy;
        }
        else {
            searchBy = [query.searchBy];
        }
    }
    const search = query.search || '';
    const filter = {};
    Object.keys(query).forEach(key => {
        if (key.startsWith('filter.')) {
            const filterKey = key.replace('filter.', '');
            filter[filterKey] = query[key];
        }
    });
    let select = [];
    if (query.select) {
        if (Array.isArray(query.select)) {
            select = query.select;
        }
        else {
            select = query.select.split(',');
        }
    }
    return {
        page,
        limit,
        sortBy,
        searchBy,
        search,
        filter,
        select,
    };
});
//# sourceMappingURL=paginate.decorator.js.map