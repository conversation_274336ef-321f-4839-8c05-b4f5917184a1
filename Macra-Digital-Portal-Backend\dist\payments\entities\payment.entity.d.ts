import { User } from 'src/entities';
export declare enum PaymentStatus {
    PENDING = "pending",
    PAID = "paid",
    OVERDUE = "overdue",
    CANCELLED = "cancelled",
    REFUNDED = "refunded"
}
export declare enum PaymentType {
    LICENSE_FEE = "License Fee",
    PROCUREMENT_FEE = "Procurement Fee",
    APPLICATION_FEE = "Application Fee",
    RENEWAL_FEE = "Renewal Fee",
    PENALTY_FEE = "Penalty Fee",
    INSPECTION_FEE = "Inspection Fee"
}
export declare enum Currency {
    MWK = "MWK",
    USD = "USD",
    EUR = "EUR"
}
export declare class Payment {
    payment_id: string;
    invoice_number: string;
    amount: number;
    currency: Currency;
    status: PaymentStatus;
    payment_type: PaymentType;
    description: string;
    due_date: Date;
    issue_date: Date;
    paid_date?: Date;
    payment_method?: string;
    notes?: string;
    transaction_reference?: string;
    entity_type?: string;
    entity_id?: string;
    user_id: string;
    user: User;
    created_by: string;
    creator: User;
    updated_by?: string;
    updater?: User;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    generateId(): void;
}
