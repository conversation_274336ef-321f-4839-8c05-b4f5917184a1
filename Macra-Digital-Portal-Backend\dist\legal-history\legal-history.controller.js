"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegalHistoryController = void 0;
const common_1 = require("@nestjs/common");
const legal_history_service_1 = require("./legal-history.service");
const create_legal_history_dto_1 = require("../dto/legal-history/create-legal-history.dto");
const update_legal_history_dto_1 = require("../dto/legal-history/update-legal-history.dto");
const swagger_1 = require("@nestjs/swagger");
const legal_history_entity_1 = require("../entities/legal-history.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let LegalHistoryController = class LegalHistoryController {
    legalHistoryService;
    constructor(legalHistoryService) {
        this.legalHistoryService = legalHistoryService;
    }
    create(createDto, req) {
        return this.legalHistoryService.create(createDto, req.user.userId);
    }
    findAll() {
        return this.legalHistoryService.findAll();
    }
    findByApplication(applicationId) {
        return this.legalHistoryService.findByApplication(applicationId);
    }
    createOrUpdateForApplication(applicationId, createDto, req) {
        return this.legalHistoryService.createOrUpdate(applicationId, createDto, req.user.userId);
    }
    findOne(id) {
        return this.legalHistoryService.findOne(id);
    }
    update(id, updateDto, req) {
        return this.legalHistoryService.update(id, updateDto, req.user.userId);
    }
    remove(id) {
        return this.legalHistoryService.softDelete(id);
    }
};
exports.LegalHistoryController = LegalHistoryController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new legal history record' }),
    (0, swagger_1.ApiBody)({ type: create_legal_history_dto_1.CreateLegalHistoryDto, description: 'Create legal history DTO' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Legal history created successfully', type: legal_history_entity_1.LegalHistory }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_legal_history_dto_1.CreateLegalHistoryDto, Object]),
    __metadata("design:returntype", void 0)
], LegalHistoryController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all legal history records' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of legal history records', type: [legal_history_entity_1.LegalHistory] }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LegalHistoryController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get legal history by application ID' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', type: 'string', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Legal history found', type: legal_history_entity_1.LegalHistory }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('applicationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LegalHistoryController.prototype, "findByApplication", null);
__decorate([
    (0, common_1.Post)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Create or update legal history for application' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', type: 'string', description: 'Application UUID' }),
    (0, swagger_1.ApiBody)({ type: create_legal_history_dto_1.CreateLegalHistoryDto, description: 'Legal history data (without application_id)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Legal history created or updated', type: legal_history_entity_1.LegalHistory }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('applicationId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], LegalHistoryController.prototype, "createOrUpdateForApplication", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get legal history by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', description: 'Legal history UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Legal history found', type: legal_history_entity_1.LegalHistory }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LegalHistoryController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update legal history by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiBody)({ type: update_legal_history_dto_1.UpdateLegalHistoryDto, description: 'Update legal history DTO' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Legal history updated', type: legal_history_entity_1.LegalHistory }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_legal_history_dto_1.UpdateLegalHistoryDto, Object]),
    __metadata("design:returntype", void 0)
], LegalHistoryController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete legal history by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Legal history deleted' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LegalHistoryController.prototype, "remove", null);
exports.LegalHistoryController = LegalHistoryController = __decorate([
    (0, swagger_1.ApiTags)('Legal History'),
    (0, common_1.Controller)('legal-history'),
    __metadata("design:paramtypes", [legal_history_service_1.LegalHistoryService])
], LegalHistoryController);
//# sourceMappingURL=legal-history.controller.js.map