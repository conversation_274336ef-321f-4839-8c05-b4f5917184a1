module.exports = {

"[project]/src/services/evaluationService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "evaluationService": (()=>evaluationService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-ssr] (ecmascript)");
'use client';
;
;
const evaluationService = {
    // Get all evaluations with pagination
    async getEvaluations (params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/evaluations?${queryParams.toString()}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get evaluation by ID
    async getEvaluation (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/evaluations/${id}`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get evaluation by application ID
    async getEvaluationByApplication (applicationId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/evaluations/application/${applicationId}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            throw error;
        }
    },
    // Get evaluation criteria
    async getEvaluationCriteria (evaluationId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/evaluations/${evaluationId}/criteria`);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new evaluation
    async createEvaluation (data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/evaluations', data);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update evaluation
    async updateEvaluation (id, data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(`/evaluations/${id}`, data);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Delete evaluation
    async deleteEvaluation (id) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(`/evaluations/${id}`);
    },
    // Get evaluation statistics
    async getEvaluationStats () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/evaluations/stats');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Submit evaluation (mark as completed)
    async submitEvaluation (id, data) {
        return this.updateEvaluation(id, {
            ...data,
            status: 'completed'
        });
    },
    // Calculate total score from criteria
    calculateTotalScore (criteria) {
        if (!criteria || criteria.length === 0) return 0;
        const weightedSum = criteria.reduce((sum, criterion)=>{
            return sum + criterion.score * criterion.weight;
        }, 0);
        const totalWeight = criteria.reduce((sum, criterion)=>sum + criterion.weight, 0);
        return totalWeight > 0 ? weightedSum / totalWeight : 0;
    },
    // Update application status with evaluation comments and email applicant
    async updateApplicationStatusWithEmail (applicationId, data) {
        try {
            const formData = new FormData();
            formData.append('status', data.status);
            formData.append('comment', data.comment);
            formData.append('send_email', 'true');
            if (data.step) {
                formData.append('step', data.step);
            }
            // Add attachments if provided
            if (data.attachments && data.attachments.length > 0) {
                data.attachments.forEach((file, index)=>{
                    formData.append(`attachments`, file);
                });
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(`/application-status/${applicationId}/status`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error updating application status with email:', error);
            throw error;
        }
    },
    // Upload evaluation attachment
    async uploadEvaluationAttachment (applicationId, step, file) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('step', step);
            formData.append('type', 'evaluation');
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(`/applications/${applicationId}/attachments`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error uploading evaluation attachment:', error);
            throw error;
        }
    },
    // Save evaluation comment
    async saveEvaluationComment (applicationId, step, comment) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(`/applications/${applicationId}/evaluation-comments`, {
                step,
                comment
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('Error saving evaluation comment:', error);
            throw error;
        }
    },
    // Get evaluation template based on license type
    getEvaluationTemplate (licenseType) {
        const templates = {
            postal_service: [
                {
                    category: 'financial_capacity',
                    subcategory: 'financial_documents',
                    score: 0,
                    weight: 0.15,
                    max_marks: 15
                },
                {
                    category: 'financial_capacity',
                    subcategory: 'capital_adequacy',
                    score: 0,
                    weight: 0.10,
                    max_marks: 10
                },
                {
                    category: 'financial_capacity',
                    subcategory: 'financial_projections',
                    score: 0,
                    weight: 0.10,
                    max_marks: 10
                },
                {
                    category: 'financial_capacity',
                    subcategory: 'credit_worthiness',
                    score: 0,
                    weight: 0.05,
                    max_marks: 5
                },
                {
                    category: 'business_plan',
                    subcategory: 'market_analysis',
                    score: 0,
                    weight: 0.10,
                    max_marks: 10
                },
                {
                    category: 'business_plan',
                    subcategory: 'business_model',
                    score: 0,
                    weight: 0.10,
                    max_marks: 10
                },
                {
                    category: 'business_plan',
                    subcategory: 'revenue_projections',
                    score: 0,
                    weight: 0.05,
                    max_marks: 5
                },
                {
                    category: 'business_plan',
                    subcategory: 'growth_strategy',
                    score: 0,
                    weight: 0.05,
                    max_marks: 5
                },
                {
                    category: 'technical_expertise',
                    subcategory: 'technical_capacity',
                    score: 0,
                    weight: 0.10,
                    max_marks: 10
                },
                {
                    category: 'technical_expertise',
                    subcategory: 'operational_plan',
                    score: 0,
                    weight: 0.10,
                    max_marks: 10
                },
                {
                    category: 'technical_expertise',
                    subcategory: 'implementation_timeline',
                    score: 0,
                    weight: 0.05,
                    max_marks: 5
                },
                {
                    category: 'organizational_structure',
                    subcategory: 'management_structure',
                    score: 0,
                    weight: 0.05,
                    max_marks: 5
                }
            ],
            telecommunications: [
                {
                    category: 'financial_capacity',
                    subcategory: 'financial_documents',
                    score: 0,
                    weight: 0.20,
                    max_marks: 20
                },
                {
                    category: 'financial_capacity',
                    subcategory: 'capital_adequacy',
                    score: 0,
                    weight: 0.15,
                    max_marks: 15
                },
                {
                    category: 'technical_expertise',
                    subcategory: 'network_design',
                    score: 0,
                    weight: 0.20,
                    max_marks: 20
                },
                {
                    category: 'technical_expertise',
                    subcategory: 'technical_capacity',
                    score: 0,
                    weight: 0.15,
                    max_marks: 15
                },
                {
                    category: 'business_plan',
                    subcategory: 'market_analysis',
                    score: 0,
                    weight: 0.10,
                    max_marks: 10
                },
                {
                    category: 'business_plan',
                    subcategory: 'business_model',
                    score: 0,
                    weight: 0.10,
                    max_marks: 10
                },
                {
                    category: 'organizational_structure',
                    subcategory: 'management_structure',
                    score: 0,
                    weight: 0.05,
                    max_marks: 5
                },
                {
                    category: 'organizational_structure',
                    subcategory: 'compliance_framework',
                    score: 0,
                    weight: 0.05,
                    max_marks: 5
                }
            ]
        };
        return templates[licenseType] || templates.postal_service;
    }
};
}}),

};

//# sourceMappingURL=src_services_evaluationService_ts_6e2a7843._.js.map