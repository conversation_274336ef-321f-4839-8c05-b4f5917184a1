{"version": 3, "file": "consumer-affairs-complaint.dto.js", "sourceRoot": "", "sources": ["../../src/consumer-affairs/consumer-affairs-complaint.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA2G;AAC3G,2FAA4G;AAE5G,MAAa,iCAAiC;IAI5C,KAAK,CAAS;IAId,WAAW,CAAS;IAGpB,QAAQ,CAAoB;IAI5B,QAAQ,CAAqB;CAC9B;AAhBD,8EAgBC;AAZC;IAHC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;IACrE,IAAA,2BAAS,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;;gEACtD;AAId;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,iDAAiD,EAAE,CAAC;;sEAC1D;AAGpB;IADC,IAAA,wBAAM,EAAC,qDAAiB,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;;mEACzC;AAI5B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qDAAiB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;mEACpC;AAG/B,MAAa,iCAAiC;IAK5C,KAAK,CAAU;IAKf,WAAW,CAAU;IAIrB,QAAQ,CAAqB;IAI7B,MAAM,CAAmB;IAIzB,QAAQ,CAAqB;IAI7B,WAAW,CAAU;IAIrB,UAAU,CAAU;IAIpB,cAAc,CAAU;IAIxB,WAAW,CAAQ;CACpB;AAvCD,8EAuCC;AAlCC;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;IACrE,IAAA,2BAAS,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;;gEACrD;AAKf;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,iDAAiD,EAAE,CAAC;;sEACzD;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qDAAiB,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;;mEACxC;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,mDAAe,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;;iEACxC;AAIzB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qDAAiB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;mEACpC;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;;sEACzB;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qEACS;AAIpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yEACa;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;8BACD,IAAI;sEAAC;AAGrB,MAAa,mCAAmC;IAC9C,YAAY,CAAS;IACrB,gBAAgB,CAAS;IACzB,cAAc,CAAS;IACvB,KAAK,CAAS;IACd,WAAW,CAAS;IACpB,QAAQ,CAAoB;IAC5B,MAAM,CAAkB;IACxB,QAAQ,CAAoB;IAC5B,WAAW,CAAU;IACrB,UAAU,CAAU;IACpB,WAAW,CAAQ;IACnB,UAAU,CAAO;IACjB,UAAU,CAAO;IAGjB,WAAW,CAKT;IAEF,QAAQ,CAKN;IAEF,WAAW,CAMP;IAEJ,cAAc,CAUV;CACL;AAjDD,kFAiDC;AAED,MAAa,2CAA2C;IAEtD,YAAY,CAAS;IAIrB,SAAS,CAAS;IAIlB,SAAS,CAAS;IAIlB,SAAS,CAAS;IAElB,SAAS,CAAS;CACnB;AAjBD,kGAiBC;AAfC;IADC,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;iFAC1B;AAIrB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;8EACjC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;8EACjC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;8EACjC;AAKpB,MAAa,uCAAuC;IAElD,MAAM,CAAkB;IAIxB,OAAO,CAAU;CAClB;AAPD,0FAOC;AALC;IADC,IAAA,wBAAM,EAAC,mDAAe,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;;uEACzC;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wEACM;AAGnB,MAAa,iCAAiC;IAG5C,QAAQ,CAAqB;IAI7B,MAAM,CAAmB;IAIzB,QAAQ,CAAqB;IAI7B,cAAc,CAAU;IAIxB,WAAW,CAAU;IAIrB,SAAS,CAAU;IAInB,OAAO,CAAU;IAIjB,MAAM,CAAU;IAGhB,IAAI,CAAU;IAGd,KAAK,CAAU;IAGf,OAAO,CAAU;IAGjB,UAAU,CAAkB;CAC7B;AA5CD,8EA4CC;AAzCC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qDAAiB,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;;mEACxC;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,mDAAe,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;;iEACxC;AAIzB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qDAAiB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;mEACpC;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;yEACzB;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;;sEACzB;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;;oEAChD;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;;kEAChD;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iEACK;AAGhB;IADC,IAAA,4BAAU,GAAE;;+DACC;AAGd;IADC,IAAA,4BAAU,GAAE;;gEACE;AAGf;IADC,IAAA,4BAAU,GAAE;;kEACI;AAGjB;IADC,IAAA,4BAAU,GAAE;;qEACe"}