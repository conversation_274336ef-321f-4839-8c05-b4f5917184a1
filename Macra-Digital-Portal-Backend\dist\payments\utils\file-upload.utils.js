"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUploadUtils = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const uuid_1 = require("uuid");
const common_1 = require("@nestjs/common");
class FileUploadUtils {
    static UPLOAD_DIR = path.join(process.cwd(), 'uploads', 'proof-of-payments');
    static MAX_FILE_SIZE = 5 * 1024 * 1024;
    static ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'application/pdf',
    ];
    static validateFile(file) {
        if (!file) {
            throw new common_1.BadRequestException('No file uploaded');
        }
        if (file.size > this.MAX_FILE_SIZE) {
            throw new common_1.BadRequestException('File size exceeds 5MB limit');
        }
        if (!this.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
            throw new common_1.BadRequestException('Only PDF and image files (JPG, PNG) are allowed');
        }
        const fileExtension = path.extname(file.originalname).toLowerCase();
        const allowedExtensions = ['.pdf', '.jpg', '.jpeg', '.png'];
        if (!allowedExtensions.includes(fileExtension)) {
            throw new common_1.BadRequestException('Invalid file extension');
        }
    }
    static saveFile(file) {
        this.validateFile(file);
        this.ensureUploadDirExists();
        const fileExtension = path.extname(file.originalname);
        const filename = `${(0, uuid_1.v4)()}${fileExtension}`;
        const filepath = path.join(this.UPLOAD_DIR, filename);
        try {
            fs.writeFileSync(filepath, file.buffer);
            return { filename, filepath };
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to save file');
        }
    }
    static deleteFile(filepath) {
        try {
            if (fs.existsSync(filepath)) {
                fs.unlinkSync(filepath);
            }
        }
        catch (error) {
            console.error('Failed to delete file:', error);
        }
    }
    static fileExists(filepath) {
        return fs.existsSync(filepath);
    }
    static getFileStats(filepath) {
        try {
            return fs.statSync(filepath);
        }
        catch (error) {
            return null;
        }
    }
    static createFileStream(filepath) {
        if (!this.fileExists(filepath)) {
            throw new common_1.BadRequestException('File not found');
        }
        return fs.createReadStream(filepath);
    }
    static ensureUploadDirExists() {
        if (!fs.existsSync(this.UPLOAD_DIR)) {
            fs.mkdirSync(this.UPLOAD_DIR, { recursive: true });
        }
    }
    static getMimeTypeFromExtension(filename) {
        const ext = path.extname(filename).toLowerCase();
        const mimeTypes = {
            '.pdf': 'application/pdf',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
        };
        return mimeTypes[ext] || 'application/octet-stream';
    }
    static sanitizeFilename(filename) {
        return filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    }
    static formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0)
            return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
}
exports.FileUploadUtils = FileUploadUtils;
//# sourceMappingURL=file-upload.utils.js.map