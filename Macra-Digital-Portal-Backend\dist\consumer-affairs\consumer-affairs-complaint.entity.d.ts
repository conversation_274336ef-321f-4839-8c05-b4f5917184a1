import { User } from '../entities/user.entity';
export declare enum ComplaintCategory {
    BILLING_CHARGES = "Billing & Charges",
    SERVICE_QUALITY = "Service Quality",
    NETWORK_ISSUES = "Network Issues",
    CUSTOMER_SERVICE = "Customer Service",
    CONTRACT_DISPUTES = "Contract Disputes",
    ACCESSIBILITY = "Accessibility",
    FRAUD_SCAMS = "Fraud & Scams",
    OTHER = "Other"
}
export declare enum ComplaintStatus {
    SUBMITTED = "submitted",
    UNDER_REVIEW = "under_review",
    INVESTIGATING = "investigating",
    RESOLVED = "resolved",
    CLOSED = "closed"
}
export declare enum ComplaintPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare class ConsumerAffairsComplaint {
    complaint_id: string;
    complaint_number: string;
    complainant_id: string;
    title: string;
    description: string;
    category: ComplaintCategory;
    status: ComplaintStatus;
    priority: ComplaintPriority;
    assigned_to?: string;
    resolution?: string;
    internal_notes?: string;
    resolved_at?: Date;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    created_by?: string;
    updated_by?: string;
    complainant: User;
    assignee?: User;
    creator?: User;
    updater?: User;
    attachments: ConsumerAffairsComplaintAttachment[];
    status_history: ConsumerAffairsComplaintStatusHistory[];
    generateId(): void;
}
export declare class ConsumerAffairsComplaintAttachment {
    attachment_id: string;
    complaint_id: string;
    file_name: string;
    file_path: string;
    file_type: string;
    file_size: number;
    uploaded_at: Date;
    uploaded_by: string;
    complaint: ConsumerAffairsComplaint;
    uploader: User;
    generateId(): void;
}
export declare class ConsumerAffairsComplaintStatusHistory {
    history_id: string;
    complaint_id: string;
    status: ComplaintStatus;
    comment?: string;
    created_at: Date;
    created_by: string;
    complaint: ConsumerAffairsComplaint;
    creator: User;
    generateId(): void;
}
