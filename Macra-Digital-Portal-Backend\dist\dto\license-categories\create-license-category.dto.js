"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLicenseCategoryDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateLicenseCategoryDto {
    license_type_id;
    parent_id;
    name;
    fee;
    description;
    authorizes;
}
exports.CreateLicenseCategoryDto = CreateLicenseCategoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'License type ID that this category belongs to',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateLicenseCategoryDto.prototype, "license_type_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent category ID for hierarchical categories (optional)',
        example: '123e4567-e89b-12d3-a456-************',
        required: false,
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateLicenseCategoryDto.prototype, "parent_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the license category',
        example: 'Class A ISP License',
        maxLength: 255,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateLicenseCategoryDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Fee for this license category',
        example: '5000.00',
        maxLength: 20,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateLicenseCategoryDto.prototype, "fee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the license category',
        example: 'License for large-scale internet service providers',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateLicenseCategoryDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'What this license category authorizes',
        example: 'Provision of internet services to residential and commercial customers',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateLicenseCategoryDto.prototype, "authorizes", void 0);
//# sourceMappingURL=create-license-category.dto.js.map